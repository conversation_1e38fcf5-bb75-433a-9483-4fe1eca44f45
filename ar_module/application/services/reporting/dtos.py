from ths_common.value_objects import UserData
from treebo_commons.utils import dateutils

from ar_module.domain.constants import UserType


class DebtorSummaryRequestData:
    def __init__(
        self,
        debtor_id,
        from_date=None,
        to_date=None,
        to_recipients=None,
        cc_recipients=None,
        additional_to_recipients=None,
        additional_cc_recipients=None,
        include_mapped_records=False,
        user_data=None,
        **kwargs,
    ):
        self.debtor_id = debtor_id
        self.from_date = from_date
        self.to_date = to_date
        self.to_recipients = to_recipients
        self.cc_recipients = cc_recipients
        self.additional_to_recipients = additional_to_recipients
        self.additional_cc_recipients = additional_cc_recipients
        self.include_mapped_records = include_mapped_records
        self.user_data = user_data

    def serialize(self):
        user_data = dict()
        if self.user_data:
            user_data = dict(
                user_type=self.user_data.user_type,
                user=self.user_data.user,
                hotel_id=self.user_data.hotel_id,
            )
        return dict(
            debtor_id=self.debtor_id,
            from_date=str(self.from_date) if self.from_date else None,
            to_date=str(self.to_date) if self.to_date else None,
            to_recipients=self.to_recipients,
            cc_recipients=self.cc_recipients,
            additional_to_recipients=self.additional_to_recipients,
            additional_cc_recipients=self.additional_cc_recipients,
            include_mapped_records=self.include_mapped_records,
            user_data=user_data,
        )

    @staticmethod
    def load_from_dict(data):
        user_data = data.get("user_data", {})
        user_data = UserData(
            user_type=user_data.get("user_type") or UserType.BACKEND_SYSTEM,
            user=user_data.get("user") or UserType.BACKEND_SYSTEM,
            hotel_id=user_data.get("hotel_id"),
            user_auth_id=user_data.get("user_auth_id"),
        )
        return DebtorSummaryRequestData(
            debtor_id=data.get("debtor_id"),
            from_date=data.get("from_date"),
            to_date=data.get("to_date"),
            to_recipients=data.get("to_recipients"),
            cc_recipients=data.get("cc_recipients"),
            additional_to_recipients=data.get("additional_to_recipients"),
            additional_cc_recipients=data.get("additional_cc_recipients"),
            include_mapped_records=data.get("include_mapped_records"),
            user_data=user_data,
        )


class DebtorSummaryRequestDto:
    def __init__(
        self,
        report_name: str,
        report_request_data: DebtorSummaryRequestData,
    ):
        self.report_name = report_name
        self.report_request_data = report_request_data

    def serialize(self):
        return dict(
            report_name=self.report_name,
            report_request_data=self.report_request_data.serialize(),
        )


class DebitLineItemDto:
    def __init__(
        self,
        debit_date,
        reference_number,
        posttax_amount,
        unsettled_amount,
        booking_reference_number,
        due_date,
        pretax_amount,
        tax_amount,
        debit_template_url,
        guest_name,
        hotel_name,
        checkin,
        checkout,
    ):
        self.debit_date = debit_date
        self.reference_number = reference_number
        self.posttax_amount = posttax_amount
        self.unsettled_amount = unsettled_amount
        self.booking_reference_number = booking_reference_number
        self.due_date = due_date
        self.pretax_amount = pretax_amount
        self.tax_amount = tax_amount
        self.debit_template_url = debit_template_url
        self.guest_name = guest_name
        self.hotel_name = hotel_name
        self.checkin = checkin
        self.checkout = checkout

    @staticmethod
    def create_from_debit_data(debit_data):
        invoice_meta = debit_data.invoice_meta or dict()
        return DebitLineItemDto(
            debit_date=debit_data.debit_date,
            reference_number=debit_data.reference_number,
            posttax_amount=debit_data.posttax_amount,
            unsettled_amount=debit_data.unsettled_amount,
            booking_reference_number=debit_data.booking_reference_number,
            due_date=debit_data.due_date,
            pretax_amount=debit_data.pretax_amount,
            tax_amount=debit_data.tax_amount,
            debit_template_url=debit_data.debit_template_url,
            guest_name=invoice_meta.get("guest_name"),
            hotel_name=invoice_meta.get("hotel_name"),
            checkin=dateutils.ymd_str_to_date(invoice_meta.get("checkin")).strftime(
                "%d-%m-%Y"
            )
            if invoice_meta.get("checkin")
            else None,
            checkout=dateutils.ymd_str_to_date(invoice_meta.get("checkout")).strftime(
                "%d-%m-%Y"
            )
            if invoice_meta.get("checkout")
            else None,
        )
