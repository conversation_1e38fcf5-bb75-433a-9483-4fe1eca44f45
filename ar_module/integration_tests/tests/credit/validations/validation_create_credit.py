import datetime
import json

from ar_module.common.utils import get_last_date_of_quarter
from ar_module.integration_tests.config.common_config import CREDIT_CREATED, SUPER_ADMIN
from ar_module.integration_tests.config.sheet_names import CREATE_CREDIT_SHEET_NAME
from ar_module.integration_tests.resources.db_queries import (
    GET_CREDIT_ID,
    GET_LINKED_DEBIT_ID,
)
from ar_module.integration_tests.tests.base_validation import BaseValidations
from ar_module.integration_tests.utilities.common_utils import (
    assert_,
    query_execute,
    return_date,
    sanitize_blank,
    sanitize_test_data,
)
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateCredit(BaseValidations):
    def __init__(
        self,
        client,
        test_case_id,
        response,
        credit_request,
        audit_trail_request,
        debtor_id,
        debit_request,
        debtor_ids,
        hotel_id,
    ):
        self.client = client
        self.test_data = get_test_case_data(CREATE_CREDIT_SHEET_NAME, test_case_id)[0]
        self.response = response
        self.credit_request = credit_request
        self.audit_trail_request = audit_trail_request
        self.debtor_id = debtor_id
        self.debit_request = debit_request
        self.debtor_ids = debtor_ids
        self.hotel_id = hotel_id

    def validate_response(self):
        assert_(
            sanitize_test_data(
                self.response["data"]["credit"]["amount_in_base_currency"]
            ),
            sanitize_test_data(self.test_data["expected_base_currency"]),
        )
        assert_(
            sanitize_test_data(
                self.response["data"]["credit"]["amount_in_credit_currency"]
            ),
            sanitize_test_data(self.test_data["expected_credit_currency"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["credit"]["credit_type"]),
            sanitize_test_data(self.test_data["credit_type"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["credit"]["mode_of_credit"]),
            sanitize_test_data(self.test_data["mode_of_credit"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["credit"]["unused_credit_amount"]),
            sanitize_test_data(self.test_data["unused_credit_amount"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["credit"]["date"]),
            sanitize_test_data(str(return_date(int(self.test_data["date"])))),
        )

    def validate_all_credits_response(self):
        get_credits_response = self.credit_request.get_credits_request(
            self.client, 200, self.debtor_id, self.hotel_id, SUPER_ADMIN
        )["data"]["credits"]
        expected_credits_data = json.loads(
            self.test_data["expected_get_credits_response"]
        )

        credit_ids = []
        for credit_id in query_execute(GET_CREDIT_ID):
            credit_ids.append(credit_id[0])

        for actual_data, expected_data in zip(
            get_credits_response, expected_credits_data
        ):
            assert_(
                actual_data["reference_number"],
                self.credit_request.credit_reference_number,
            )
            assert actual_data["credit_id"] in credit_ids
            assert_(
                actual_data["amount_in_base_currency"],
                expected_data["amount_in_base_currency"],
            )
            assert_(
                actual_data["amount_in_credit_currency"],
                expected_data["amount_in_credit_currency"],
            )
            assert_(
                actual_data["unused_credit_amount"],
                expected_data["unused_credit_amount"],
            )
            assert_(actual_data["credit_type"], expected_data["credit_type"])
            assert_(actual_data["mode_of_credit"], expected_data["mode_of_credit"])
            assert_(actual_data["debtor_id"], self.debtor_id)
            assert_(actual_data["reference_id"], expected_data["reference_id"])
            assert_(actual_data["status"], expected_data["status"])
            assert_(
                actual_data["used_to_auto_settle_debit"],
                expected_data["used_to_auto_settle_debit"],
            )

            if expected_data.get("approval_document"):
                assert_(
                    actual_data["approval_document"], expected_data["approval_document"]
                )

    def validate_linked_debit(self):
        if sanitize_blank(self.test_data["expected_get_linked_debit"]) is not None:
            get_debit_response = self.debit_request.get_debit_request(
                self.client, 200, SUPER_ADMIN
            )["data"]["debits"]
            expected_response = json.loads(self.test_data["expected_get_linked_debit"])
            get_linked_debit_id_response = query_execute(GET_LINKED_DEBIT_ID).fetchall()

            for actual_data, expected_data in zip(
                get_debit_response, expected_response
            ):
                assert_(actual_data["debit_id"], get_linked_debit_id_response[0][0])
                assert_(
                    actual_data["settlement_status"], expected_data["settlement_status"]
                )
                assert_(
                    actual_data["unsettled_amount"], expected_data["unsettled_amount"]
                )
                assert_(
                    actual_data["reference_number"], expected_data["reference_number"]
                )
                assert_(actual_data["debit_type"], expected_data["debit_type"])
                assert_(actual_data["debtor_id"], self.debtor_ids[0])
                assert_(
                    actual_data["debit_date"],
                    datetime.date.strftime(return_date(0), "%Y-%m-%d"),
                )
                assert_(
                    actual_data["due_date"],
                    datetime.date.strftime(
                        get_last_date_of_quarter(return_date(0)), "%Y-%m-%d"
                    ),
                )
                assert_(
                    actual_data["auto_settled_via_credit"],
                    expected_data["auto_settled_via_credit"],
                )
                assert_(
                    actual_data["debit_amount"]["posttax_amount"],
                    expected_data["debit_amount"]["posttax_amount"],
                )
                assert_(
                    actual_data["debit_amount"]["pretax_amount"],
                    expected_data["debit_amount"]["pretax_amount"],
                )
                assert_(
                    actual_data["debit_amount"]["tax_amount"],
                    expected_data["debit_amount"]["tax_amount"],
                )

    def validate_audit_trail(self):
        self.validate_audit_data(
            self.client,
            self.test_data,
            CREDIT_CREATED,
            self.audit_trail_request,
            self.credit_request,
        )
