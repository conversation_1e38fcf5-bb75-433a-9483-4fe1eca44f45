from ar_module.exceptions import BaseARException


class MissingCashRegisterError(BaseARException):
    error_code = "3001"

    def __init__(self, message, extra_payload=None):
        self.message = message
        super().__init__(extra_payload=extra_payload)


class MissingCashierSessionError(BaseARException):
    error_code = "3002"

    def __init__(self, message, extra_payload=None):
        self.message = message
        super().__init__(extra_payload=extra_payload)


class RecordPaymentToCashierError(BaseARException):
    error_code = "3003"

    def __init__(self, message, extra_payload=None):
        self.message = message
        super().__init__(extra_payload=extra_payload)


class BilledEntityIdMismatchError(BaseARException):
    error_code = "3004"

    def __init__(self, message, extra_payload=None):
        self.message = message
        super().__init__(extra_payload=extra_payload)
