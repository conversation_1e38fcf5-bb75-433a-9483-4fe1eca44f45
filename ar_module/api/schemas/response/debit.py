from marshmallow import Schema, fields
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from ar_module.api.schemas import AmountSchema


class DebitSchema(Schema):
    debit_id = fields.String()
    debtor_id = fields.String()
    hotel_id = fields.String()
    debit_date = fields.Date()
    due_date = fields.Date()
    debit_amount = fields.Nested(AmountSchema, required=True)
    reference_number = fields.String()
    debit_type = fields.String()
    reference_id = fields.String()
    settlement_status = fields.String()
    unsettled_amount = MoneyField()
    debit_template_url = fields.String()
    remarks = fields.String()
    booking_reference_number = fields.String()
    bill_to_debtor_code = fields.String()
    auto_settled_via_credit = fields.Boolean()
    created_at = fields.LocalDateTime()
    tenant_id = fields.String(allow_none=True)


class DebitResponseSchema(Schema):
    debits = fields.Nested(DebitSchema, many=True)
    limit = fields.Integer(description="Number of results")
    offset = fields.Integer(description="Offset from which the results are fetched")
    total = fields.Integer(description="Total number of debits for the query")
