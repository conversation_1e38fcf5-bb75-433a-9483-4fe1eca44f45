import datetime
import json
import logging
import os
import uuid

import click
from flask.cli import with_appcontext
from ths_common.value_objects import EmailAttachment
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationSenderName,
    NotificationServiceClient,
)
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("ingest_invoice")
@click.option(
    "--inv_file_path",
    help="file path of invoice json payload",
    default=None,
)
@click.option(
    "--cn_file_path",
    help="file path of cn json payload",
    default=None,
)
@click.option(
    "--email",
    help="EMail to receive details run report",
    default=None,
)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=None,
)
@with_appcontext
@inject(
    accounts_receivable_service=AccountReceivableApplicationService,
    notification_service_client=NotificationServiceClient,
)
def ingest_invoice(
    accounts_receivable_service,
    notification_service_client,
    inv_file_path,
    cn_file_path,
    email,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    information_log = []
    processed = set()
    if inv_file_path:
        with open(inv_file_path, "r") as inv_file:
            payloads = json.load(inv_file)
            for index, data in enumerate(payloads):
                try:
                    invoice_id = data.get("invoice_id")
                    if invoice_id in processed:
                        continue
                    accounts_receivable_service.process_crs_invoices_event([data])
                    processed.add(invoice_id)
                    message = "Success"
                except Exception as err:
                    message = f"Invoice Ingestion failed: error {str(err)}"
                    logger.exception(err)
                information_log.append(
                    dict(type="Invoice", id=invoice_id, message=message)
                )
                click.echo(f"{index} : {invoice_id}: {message}")
    processed = set()
    if cn_file_path:
        with open(cn_file_path, "r") as cn_file:
            payloads = json.load(cn_file)
            for index, data in enumerate(payloads):
                try:
                    credit_note_id = data.get("credit_note_id")
                    if credit_note_id in processed:
                        continue
                    accounts_receivable_service.process_credit_notes_event(
                        [data], should_raise_for_invoice_missing=True
                    )
                    processed.add(credit_note_id)
                    message = "Success"
                except Exception as err:
                    message = f"CN Ingestion failed: error {str(err)}"
                    logger.exception(err)
                information_log.append(
                    dict(type="CN", id=credit_note_id, message=message)
                )
                click.echo(f"{index} : {credit_note_id}: {message}")

    if email:
        _generate_csv_upload_results_and_send_email(
            notification_service_client, information_log, email
        )


def _generate_csv_upload_results_and_send_email(
    notification_service_client, data_list, email
):
    column_values = data_list[0].keys()
    file_path, file_name = _generate_file_name()
    with CsvWriter(file_path) as csv_writer:
        csv_writer.write_aggregates(data_list, column_values)
        pre_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            "ar-debit-upload-results/",
            csv_writer.file_path,
            604800,
        )
    email_attachments = [EmailAttachment(url=pre_signed_url, filename=file_name)]
    # pylint: disable=no-member
    notification_service_client.email(
        body_html="Hi <br><br>PFA<br><br>",
        subject="Bulk upload of Invoice and CN",
        sender=NotificationEmailIds.NOREPLY.value,
        recievers=[email],
        attachments=email_attachments,
        sender_name=NotificationSenderName.TREEBO_HOTELS.value,
        raise_on_failure=False,
        cc_list=[],
    )
    # pylint: enable=no-member


def _generate_file_name(extension="csv"):
    identifier = str(uuid.uuid4())
    file_name = f"{datetime.datetime.now().strftime('%Y-%m-%d')}-debit-bulk-upload-{identifier}.{extension}"
    return (
        os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/") + file_name
    ), file_name
