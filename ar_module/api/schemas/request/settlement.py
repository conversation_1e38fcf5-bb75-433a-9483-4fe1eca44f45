from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from treebo_commons.money.money_field import <PERSON><PERSON>ield

from ar_module.api.schemas.validators import validate_positive_integer
from ar_module.application.dtos.credit_dto import SettlementDto


class SettlementRequestSchema(Schema):
    amount = MoneyField(required=True, validate=validate_positive_integer)
    debit_id = fields.String(required=True)
    tds_settlement_amount = MoneyField(
        required=False, allow_none=True, validate=validate_positive_integer
    )
    remarks = fields.String(required=False, allow_none=True)

    @post_load
    def create_object(self, data):
        return SettlementDto(
            amount=data.get("amount"),
            debit_id=data.get("debit_id"),
            tds_settlement_amount=data.get("tds_settlement_amount"),
            remarks=data.get("remarks"),
        )


class SettlementSearchSchema(Schema):
    reference_id = fields.String(
        required=False,
        description="Invoice Id for which settlements needs to be fetched.",
    )
    reference_number = fields.String(
        required=False,
        description="Invoice Number for which settlements needs to be fetched.",
    )
    credit_id = fields.String(
        required=False,
        description="Credit Id for which settlements needs to be fetched.",
    )
    debit_id = fields.String(
        required=False,
        description="Debit Id for which settlements needs to be fetched.",
    )

    @validates_schema
    def validate_request_data(self, data):
        if data.get("reference_id") and data.get("reference_number"):
            raise ValidationError(
                "Only one of reference_id or reference_number can be provided"
            )
