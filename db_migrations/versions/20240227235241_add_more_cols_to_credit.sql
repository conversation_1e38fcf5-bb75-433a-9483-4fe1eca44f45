-- revision: '20240227235241_add_more_cols_to_credit'
-- down_revision: '20240318211856_remove_ref_id_unique_index'

-- upgrade
ALTER TABLE credit ADD COLUMN cancellation_date DATE;
ALTER TABLE credit ADD COLUMN recorded_via VARCHAR;
ALTER TABLE debit ADD COLUMN recorded_via VARCHAR;
CREATE INDEX ix_credit_cancellation_date ON credit USING btree (cancellation_date);
CREATE INDEX ix_credit_created_at ON credit USING btree (created_at);

-- downgrade
ALTER TABLE credit DROP COLUMN cancellation_date;
ALTER TABLE credit DROP COLUMN recorded_via;
ALTER TABLE debit DROP COLUMN recorded_via;
DROP INDEX ix_credit_cancellation_date;
DROP INDEX ix_credit_created_at;
