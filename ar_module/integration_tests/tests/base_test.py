from ar_module.integration_tests.config.common_config import HOTEL_ID, SUPER_ADMIN
from ar_module.integration_tests.config.error_messages import ErrorMessage
from ar_module.integration_tests.config.sheet_names import CREATE_SETTLEMENT_SHEET_NAME
from ar_module.integration_tests.requests.audit_trail_request import AuditTrailRequest
from ar_module.integration_tests.requests.credit_request import CreditRequest
from ar_module.integration_tests.requests.debit_request import DebitRequest
from ar_module.integration_tests.requests.debtor_request import DebtorRequest
from ar_module.integration_tests.requests.settlement_request import SettlementRequest
from ar_module.integration_tests.utilities.common_utils import assert_


class BaseTest(object):
    debtor_request = DebtorRequest()
    debit_request = DebitRequest()
    credit_request = CreditRequest()
    settlement_request = SettlementRequest()
    audit_trail_request = AuditTrailRequest()

    def common_request_caller(
        self, client, test_case_id_plus_action_to_be_performed_list
    ):
        for action in test_case_id_plus_action_to_be_performed_list:
            test_case_id = action["id"] if "id" in action else None
            action_type = action["type"]
            config_value = action["config_value"] if "config_value" in action else None
            user_type = action["user_type"] if "user_type" in action else SUPER_ADMIN
            sheet_name = (
                action["sheet_name"]
                if "sheet_name" in action
                else CREATE_SETTLEMENT_SHEET_NAME
            )
            debit_id = (
                [action["debit_id"]]
                if "debit_id" in action
                else self.debit_request.debit_id
            )
            debtor_id = (
                action["debtor_id"]
                if "debtor_id" in action
                else self.debtor_request.debtor_id
            )
            credit_id = (
                action["credit_id"]
                if "credit_id" in action
                else self.credit_request.credit_id
            )
            if action_type == "create_debtor":
                self.debtor_request.create_new_debtor_request(
                    client, test_case_id, 200, user_type, config_value=config_value
                )
            elif action_type == "create_debit":
                self.debit_request.create_new_debit_request(
                    client,
                    test_case_id,
                    debtor_id,
                    200,
                    user_type,
                    HOTEL_ID[0],
                )
            elif action_type == "create_credit":
                self.credit_request.create_new_credit_request(
                    client,
                    test_case_id,
                    debtor_id,
                    debit_id,
                    200,
                    user_type,
                    HOTEL_ID[0],
                )
            elif action_type == "create_settlement":
                self.settlement_request.create_settlement_request(
                    client,
                    test_case_id,
                    debit_id,
                    credit_id,
                    200,
                    user_type,
                    self.credit_request.pay_mode,
                    sheet_name=sheet_name,
                    hotel_id=HOTEL_ID[0],
                )
            elif action_type == "cancel_credit":
                self.credit_request.cancel_credit(
                    client,
                    credit_id,
                    200,
                    HOTEL_ID[0],
                    user_type,
                    self.credit_request.pay_mode,
                    test_case_id,
                )
            else:
                raise ValueError(
                    action["id"] + " is not handled in Common request caller"
                )

    @staticmethod
    def response_validation_negative_cases(response, test_case_id):
        code = None
        dev_message = None
        extra_payload = None
        for error_message_index in range(len(response["errors"])):
            assert_(
                response["errors"][error_message_index]["message"]
                in ErrorMessage[test_case_id].value,
                True,
                "actual error message is not correct",
            )
        if code:
            assert_(response["errors"][0]["code"], code)
        if dev_message:
            assert_(response["errors"][0]["developer_message"], dev_message)
        if extra_payload:
            assert_(response["errors"][0]["extra_payload"], extra_payload)
