import logging

from thsc.crs.entities.billing import Invoice
from thsc.crs.entities.booking import Booking, BookingSearchQuery
from thsc.crs.entities.customer import Customer

from ar_module.application.dtos.debtor_dto import DebtorDTO
from ar_module.infrastructure.external_clients.exceptions import (
    BilledEntityIdMismatchError,
)

logger = logging.getLogger(__name__)


class CrsClient(object):
    @staticmethod
    def get_debtor_detail(booking_id, customer_id, hotel_id):
        # Customer.get() returns booking version too. Ignoring that field in tuple
        customer, _ = Customer.get(booking_id, customer_id)
        logger.info(
            "Booking customer details fetched for booking_id: %s => %s",
            booking_id,
            customer_id,
        )
        debtor_name = "{0} {1}".format(customer.first_name, customer.last_name or "")
        return DebtorDTO(
            debtor_name,
            customer.reference_id,
            customer.customer_id,
            customer.user_profile_id,
            hotel_id=hotel_id,
        )

    @staticmethod
    def get_invoice(invoice_id):
        return Invoice.get(invoice_id, with_invoice_charges=True)

    @staticmethod
    def get_debtor_detail_for_billed_entity_id(booking, billed_entity_id, hotel_id):

        if (
            booking.company_details
            and booking.company_details.billed_entity_id == billed_entity_id
        ):
            return DebtorDTO(
                booking.company_details.legal_details.legal_name,
                reference_id=booking.company_details.legal_details.external_reference_id,
                hotel_id=hotel_id,
            )
        elif (
            booking.travel_agent_details
            and booking.travel_agent_details.billed_entity_id == billed_entity_id
        ):
            return DebtorDTO(
                booking.travel_agent_details.legal_details.legal_name,
                reference_id=booking.travel_agent_details.legal_details.external_reference_id,
                hotel_id=hotel_id,
            )
        else:
            for customer in booking.customers:
                if customer.billed_entity_id == billed_entity_id:
                    debtor_name = "{0} {1}".format(
                        customer.first_name, customer.last_name or ""
                    )
                    return DebtorDTO(
                        debtor_name,
                        customer.reference_id,
                        customer.customer_id,
                        customer.user_profile_id,
                        hotel_id=hotel_id,
                    )
            raise BilledEntityIdMismatchError(
                message="Matching billed entity not found in booking",
                extra_payload=dict(
                    billed_entity_id=billed_entity_id,
                    company_details=booking.company_details.billed_entity_id,
                    travel_agent_details=booking.travel_agent_details.billed_entity_id,
                ),
            )

    @staticmethod
    def get_bookings(reference_numbers, **kwargs):
        search_query = BookingSearchQuery(reference_numbers=reference_numbers)
        booking = Booking.search(search_query, **kwargs)
        return booking
