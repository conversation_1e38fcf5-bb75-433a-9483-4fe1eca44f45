import pytest

from ar_module.integration_tests.config.common_config import (
    AR_MANAGER,
    CREATE_DEBTOR_WITH_CREDIT,
    CREATE_DEBTOR_WITH_CREDIT_AND_MULTIPLE_DEBIT,
    CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT,
    ERROR_CODES,
    FDM,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.settlement.validations.validation_create_settlement import (
    ValidationCreateSettlement,
)


class TestCreateSettlement(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message",
        [
            (
                "CreateSettlement_01",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle Complete Amount for debtor",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_02",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle Complete Amount (tds included)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_03",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle partially settled debit)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_04",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle partially settled debit(tds included)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_05",
                CREATE_DEBTOR_WITH_CREDIT_AND_MULTIPLE_DEBIT,
                "Settle Complete amount for Multiple debit",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_06",
                CREATE_DEBTOR_WITH_CREDIT_AND_MULTIPLE_DEBIT,
                "Settle Complete amount for Multiple debit(tds included)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_07",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle when settlement amount > credit amount",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_08",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle when amount is null",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_09",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle when amount is negative",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_10",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle when tds amount is negative",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_11",
                CREATE_DEBTOR_WITH_CREDIT,
                "Settle Complete Amount as FDM",
                403,
                FDM,
                None,
            ),
            (
                "CreateSettlement_12",
                CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT,
                "Create settlement of a debit with write off credit as super-admin who is not allowed to create settlement for this credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateSettlement_13",
                CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT,
                "Create settlement of a debit with write off credit as ar-manager who is allowed to create settlement for this credit",
                200,
                AR_MANAGER,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_create_settlement(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[0]

        self.debit_request.debit_id.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.settlement_request.create_settlement_request(
            client_,
            test_case_id,
            self.debit_request.debit_id,
            self.credit_request.credit_id,
            status_code,
            user_type,
            self.credit_request.pay_mode,
            hotel_id=hotel_id,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                test_case_id,
                response,
                self.credit_request,
                self.debit_request,
                self.audit_trail_request,
                self.settlement_request,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_,
        test_case_id,
        response,
        credit_request,
        debit_request,
        audit_trail_request,
        settlement_request,
    ):
        validation = ValidationCreateSettlement(
            client_,
            test_case_id,
            response,
            credit_request,
            debit_request,
            audit_trail_request,
            settlement_request,
        )
        validation.validate_response()
        validation.validate_get_settlement()
        validation.validate_audit_trail()
