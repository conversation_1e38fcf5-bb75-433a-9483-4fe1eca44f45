from ar_module.integration_tests.builders.common_builder import Settlement
from ar_module.integration_tests.config.sheet_names import SETTLEMENT_DATA_SHEET_NAME
from ar_module.integration_tests.utilities import excel_utils
from ar_module.integration_tests.utilities.common_utils import (
    return_date,
    sanitize_blank,
    sanitize_test_data,
)


class CreateCreditBuilder:
    def __init__(self, sheet_name, test_case_id, debtor_id, debit_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = CreditData(test_data, test_case_id, debtor_id, debit_id).__dict__


class CreditData:
    def __init__(self, test_data, test_case_id, debtor_id, debit_id):
        self.amount_in_base_currency = sanitize_blank(test_data["base_currency_amount"])
        self.amount_in_credit_currency = sanitize_blank(
            test_data["amount_credit_currency"]
        )
        self.date = (
            str(return_date(int(test_data["date"]))) if test_data["date"] else None
        )
        self.credit_type = sanitize_blank(test_data["credit_type"])
        if test_case_id == "CreateCredit_06":
            self.debtor_id = None
        elif test_case_id == "CreateCredit_07":
            self.debtor_id = "IncorrectDebtorId"
        else:
            self.debtor_id = debtor_id
        self.mode_of_credit = sanitize_blank(test_data["mode_of_credit"])
        self.reference_number = sanitize_blank(test_data["reference_number"])
        self.settlements = []
        if sanitize_blank(test_data["approval_document"]) is not None:
            self.approval_document = sanitize_blank(test_data["approval_document"])
        if sanitize_blank(test_data["settlement"]) is not None:
            total_settlements = test_data["settlement"].split(",")
            for settlement_index, settlements in enumerate(total_settlements):
                settlements_data = excel_utils.get_test_case_data(
                    SETTLEMENT_DATA_SHEET_NAME, settlements
                )[0]
                self.settlements.append(
                    sanitize_test_data(
                        Settlement(
                            settlements_data, debit_id[settlement_index]
                        ).__dict__
                    )
                )
