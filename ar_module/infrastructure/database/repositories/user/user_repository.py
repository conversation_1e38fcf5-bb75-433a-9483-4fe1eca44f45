from ar_module.domain.aggregates.user_aggregate import UserAggregate
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.user_model import UserModel
from ar_module.infrastructure.database.repositories.user.db_adaptor.user_adaptor import (
    UserAdaptor,
)
from object_registry import register_instance


@register_instance()
class UserRepository(BaseRepository):
    user_adaptor = UserAdaptor()

    def from_aggregate(self, aggregate: UserAggregate = None):
        user = aggregate.user
        user_model = self.user_adaptor.to_db_entity(user)
        return user_model

    def search_user_by_id(self, user_id, return_user_model=False):
        query = self.query(UserModel)
        query = query.filter(UserModel.user_id == user_id)

        user_model = query.first()
        if return_user_model:
            return user_model
        return (
            UserAggregate(self.user_adaptor.to_domain_entity(user_model))
            if user_model
            else None
        )

    def get_debtors_associated_with_user(self, user_id):
        query = self.query(UserModel)
        query = query.filter(UserModel.user_id == user_id)
        user_model = query.first()
        return user_model.debtors if user_model else []

    def save(self, user_aggregate):
        user_model = self.user_adaptor.to_db_entity(user_aggregate.user)
        self._save(user_model)
        self.flush_session()
        return user_model

    def update(self, aggregate: UserAggregate):
        user_model = self.from_aggregate(aggregate)
        self._update(user_model)
        self.flush_session()

    def update_user_model(self, user_model: UserModel):
        self._update(user_model)
        self.flush_session()
