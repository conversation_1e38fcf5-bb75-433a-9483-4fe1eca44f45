from collections import defaultdict
from typing import List

from marshmallow import ValidationError

from ar_module.application.dtos.credit_dto import (
    CreditDto,
    SettlementDto,
    SettlementDtoV2,
)
from ar_module.application.dtos.settlement_credit_details_dto import (
    SettlementCreditDetailsDto,
)
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.utils import (
    validate_credit_reversal,
    validate_payment_mode_based_on_role,
)
from ar_module.core.common.globals import global_context
from ar_module.domain.aggregates.credit_aggregate import CreditAggregate
from ar_module.domain.constants import AuditType, CreditType, UserType
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.credit_factory import CreditFactory
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_settlements_facts import (
    CreateSettlementsFacts,
)
from ar_module.exceptions import PolicyAuthException, ResourceNotFound
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.settlement.settlement_repository import (
    SettlementRepository,
)
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import register_instance


@register_instance(
    dependencies=[
        SettlementRepository,
        AuditTrailService,
        DebtorService,
        DebitRepository,
        CreditRepository,
        TenantSettings,
        UserRepository,
    ]
)
class SettlementService(object):
    def __init__(
        self,
        settlement_repo: SettlementRepository,
        audit_trail_service: AuditTrailService,
        debtor_service: DebtorService,
        debit_repo: DebitRepository,
        credit_repo: CreditRepository,
        tenant_settings: TenantSettings,
        user_repo: UserRepository,
    ):

        self.settlement_repo = settlement_repo
        self.audit_trail_service = audit_trail_service
        self.debtor_service = debtor_service
        self.debit_repo = debit_repo
        self.credit_repo = credit_repo
        self.tenant_settings = tenant_settings
        self.user_repo = user_repo

    def search_settlements(self, filter_criteria, user_data):
        credit_id = filter_criteria.get("credit_id")

        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )

        if credit_id:
            return self._find_settlements_under_credit(
                credit_id, is_hotel_level_ar_configured, user_data
            )

        return self._find_settlements_under_debit(
            filter_criteria, is_hotel_level_ar_configured, user_data
        )

    def _find_settlements_under_debit(
        self, filter_criteria, is_hotel_level_ar_configured, user_data
    ):
        debit_id = filter_criteria.get("debit_id")
        reference_id = filter_criteria.get("reference_id")
        reference_number = filter_criteria.get("reference_number")

        debtor_ids = None
        if (
            user_data.user_auth_id and user_data.user_type == UserType.FINANCE_POC
        ) or is_hotel_level_ar_configured:
            debtors = self._get_debtors(None, is_hotel_level_ar_configured, user_data)
            if not debtors:
                raise PolicyAuthException(
                    error=PolicyError.VIEW_SETTLEMENTS_NOT_ALLOWED,
                    description=f"Don't have access to debtor",
                )
            debtor_ids = [debtor.debtor_id for debtor in debtors]

        debit_search_query = DebitSearchQuery(
            reference_id=reference_id,
            reference_numbers=reference_number,
            debit_ids=[debit_id] if debit_id else None,
            debtor_ids=debtor_ids,
        )

        debit_aggregates = self.debit_repo.search_debits(debit_search_query)
        settlements = []
        for debit_aggregate in debit_aggregates:
            settlements_associated_with_debit = debit_aggregate.settlements
            if settlements_associated_with_debit:
                settlements.extend(settlements_associated_with_debit)
        debit_id_debit_map = {
            debit_aggregate.debit.debit_id: debit_aggregate.debit
            for debit_aggregate in debit_aggregates
        }
        linked_credit_aggregates = self.credit_repo.load_all(
            [settlement.credit_id for settlement in settlements]
        )
        credit_id_credit_map = {
            aggregate.credit_id: aggregate.credit
            for aggregate in linked_credit_aggregates
        }
        return self._link_credit_info_with_settlements(
            settlements, debit_id_debit_map, credit_id_credit_map
        )

    def _find_settlements_under_credit(
        self, credit_id, is_hotel_level_ar_configured, user_data
    ):
        credit_aggregate = self.credit_repo.load(credit_id)
        debtors = self._get_debtors(
            credit_aggregate.debtor_id, is_hotel_level_ar_configured, user_data
        )
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.VIEW_SETTLEMENTS_NOT_ALLOWED,
                description=f"Don't have access to debtor",
            )
        settlements = self.settlement_repo.load_settlements(
            credit_id=credit_id,
        )
        debit_search_query = DebitSearchQuery(
            debit_ids=[settlement.debit_id for settlement in settlements]
        )
        linked_debits = self.debit_repo.search_debits(
            debit_search_query, debits_only=True
        )
        debit_id_debit_map = {debit.debit_id: debit for debit in linked_debits}
        credit_id_credit_map = {credit_aggregate.credit_id: credit_aggregate.credit}
        settlements = self._link_credit_info_with_settlements(
            settlements, debit_id_debit_map, credit_id_credit_map
        )
        return settlements

    def create_settlements(
        self,
        credit_aggregate: CreditAggregate,
        settlement_dtos: List[SettlementDto],
        only_reverse_settlements_under_same_debit=False,
    ):
        (
            unmapped_settlements_debit_aggregates,
            debit_id_to_unmapped_settlements_mapping,
        ) = self.reverse_existing_settlements_for_given_payment(
            credit_aggregate,
            settlement_dtos,
            only_reverse_settlements_under_same_debit,
        )
        debit_ids = [settlement.debit_id for settlement in settlement_dtos]
        debit_aggregates = self.debit_repo.load_all_for_update(debit_ids=debit_ids)
        grouped_debit_aggregates = {
            aggregate.debit.debit_id: aggregate for aggregate in debit_aggregates
        }
        debit_id_to_new_settlements_mapping = defaultdict(list)
        settlements = []
        tds_credit_aggregates = []
        for settlement_dto in settlement_dtos:
            debit_aggregate = grouped_debit_aggregates.get(settlement_dto.debit_id)
            if not debit_aggregate:
                raise ResourceNotFound(
                    message="Debit with debit_id: {0} not found".format(
                        settlement_dto.debit_id
                    )
                )
            settlement = debit_aggregate.create_new_settlement(
                credit_aggregate, settlement_dto.amount, settlement_dto.remarks
            )
            credit_aggregate.knock_off_settlement(settlement)
            settlements.append(settlement)
            debit_id_to_new_settlements_mapping[debit_aggregate.debit.debit_id].append(
                settlement
            )
            if settlement_dto.tds_settlement_amount:
                tds_credit_aggregates.append(
                    self._create_tds_settlement(
                        credit_aggregate,
                        debit_aggregate,
                        settlement_dto.tds_settlement_amount,
                    )
                )
        return (
            debit_aggregates,
            settlements,
            debit_id_to_new_settlements_mapping,
            tds_credit_aggregates,
            unmapped_settlements_debit_aggregates,
            debit_id_to_unmapped_settlements_mapping,
        )

    @unit_of_work
    def create_settlements_from_existing_credit(
        self, credit_id, settlement_dtos: List[SettlementDto], user_data=None
    ):
        RuleEngine.action_allowed(
            action="create_settlements",
            facts=CreateSettlementsFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_id_associated_with_credit = self.credit_repo.load(
            credit_id
        ).credit.debtor_id
        debtors = self._get_debtors(
            debtor_id_associated_with_credit, is_hotel_level_ar_configured, user_data
        )
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.CREATE_SETTLEMENTS_NOT_ALLOWED,
                description=f"Don't have access to debtor {debtor_id_associated_with_credit}",
            )

        debit_ids = [settlement.debit_id for settlement in settlement_dtos]
        debit_aggregates = self.debit_repo.load_all_for_update(debit_ids=debit_ids)
        debtor_ids_of_to_be_mapped_debits = [
            debit_aggregate.debit.debtor_id for debit_aggregate in debit_aggregates
        ]
        if set(debtor_ids_of_to_be_mapped_debits) - {debtor_id_associated_with_credit}:
            raise ValidationError(
                "Trying to map debits and credit of different debtors"
            )

        return self.map_credit_and_debit_to_create_settlement(
            settlement_dtos,
            credit_id,
            debtors[0],
            user_data,
        )

    def map_credit_and_debit_to_create_settlement(
        self,
        settlement_dtos: List[SettlementDto],
        credit_id,
        debtor_aggregate,
        user_data=None,
        only_reverse_settlements_under_same_debit=False,
    ):
        credit_aggregate = self.credit_repo.load_for_update(credit_id)
        validate_credit_reversal(credit_aggregate.credit.credit_type)
        validate_payment_mode_based_on_role(
            credit_aggregate.credit.mode_of_credit, user_data
        )
        (
            debit_aggregates,
            settlements,
            debit_id_to_new_settlements_mapping,
            tds_credit_aggregates,
            unmapped_settlements_debit_aggregates,
            debit_id_to_unmapped_settlements_mapping,
        ) = self.create_settlements(
            credit_aggregate,
            settlement_dtos,
            only_reverse_settlements_under_same_debit,
        )
        if tds_credit_aggregates:
            self.credit_repo.save_all(tds_credit_aggregates)
        self.credit_repo.update(credit_aggregate)
        self.debit_repo.update_all(debit_aggregates)
        self._create_audit_for_mapped_settlements(
            debit_aggregates,
            credit_aggregate,
            debtor_aggregate,
            debit_id_to_new_settlements_mapping,
            user_data,
        )
        if unmapped_settlements_debit_aggregates:
            self._create_audit_for_unmapped_settlements(
                unmapped_settlements_debit_aggregates,
                credit_aggregate,
                debtor_aggregate,
                debit_id_to_unmapped_settlements_mapping,
                user_data,
            )
        return settlements

    def unmap_settlement(
        self, user_data, unmap_request_dto: SettlementDtoV2, debtor_aggregate
    ):
        debit_aggregate = self.debit_repo.load(unmap_request_dto.debit_id)
        credit_aggregate = self.credit_repo.load_for_update(unmap_request_dto.credit_id)
        validate_credit_reversal(credit_aggregate.credit.credit_type)
        validate_payment_mode_based_on_role(
            credit_aggregate.credit.mode_of_credit, user_data
        )
        settlement_to_reverse = debit_aggregate.reverse_payment_to_unmap_settlement(
            unmap_request_dto.credit_id,
            unmap_request_dto.amount,
        )
        credit_aggregate.reverse_settlement_amount(unmap_request_dto.amount)
        self.audit_trail_service.create_settlement_audit_trail(
            debtor_aggregate,
            debit_aggregate,
            settlement_to_reverse,
            credit_aggregate,
            debtor_aggregate.hotel_id,
            AuditType.SETTLEMENT_CANCELLED,
            user_data=user_data,
        )
        self.credit_repo.update(credit_aggregate)
        self.debit_repo.update(debit_aggregate)
        return settlement_to_reverse

    def reverse_existing_settlements_for_given_payment(
        self,
        credit_aggregate,
        settlement_dtos,
        only_reverse_settlements_under_same_debit,
    ):
        settlements = self.settlement_repo.load_settlements(
            credit_id=credit_aggregate.credit_id
        )
        if not settlements:
            return [], {}
        unmapped_settlements_debit_aggregates = []
        debit_ids = [settlement.debit_id for settlement in settlements]
        debit_aggregates = self.debit_repo.load_all_for_update(debit_ids=debit_ids)
        grouped_debit_aggregates = {
            aggregate.debit.debit_id: aggregate for aggregate in debit_aggregates
        }
        debit_id_to_unmapped_settlements_mapping = defaultdict(list)
        settlement_hash_table = {item.debit_id for item in settlement_dtos}
        for settlement in settlements:
            if (
                not only_reverse_settlements_under_same_debit
                or settlement.debit_id in settlement_hash_table
            ):
                debit_aggregate = grouped_debit_aggregates.get(settlement.debit_id)
                reversed_settlement = (
                    debit_aggregate.reverse_settlement_associated_with_given_payment(
                        credit_aggregate.credit_id
                    )
                )
                credit_aggregate.reverse_settlement_amount(reversed_settlement.amount)
                debit_id_to_unmapped_settlements_mapping[
                    debit_aggregate.debit.debit_id
                ].append(settlement)
                unmapped_settlements_debit_aggregates.append(debit_aggregate)
        if not unmapped_settlements_debit_aggregates:
            return [], {}
        self.debit_repo.update_all(unmapped_settlements_debit_aggregates)
        self.credit_repo.update(credit_aggregate)
        return (
            unmapped_settlements_debit_aggregates,
            debit_id_to_unmapped_settlements_mapping,
        )

    def _create_audit_for_mapped_settlements(
        self,
        debit_aggregates,
        credit_aggregate,
        debtor_aggregate,
        debit_id_to_new_settlements_mapping,
        user_data,
    ):
        debit_id_to_debit_aggregate_map = {
            debit_aggregate.debit.debit_id: debit_aggregate
            for debit_aggregate in debit_aggregates
        }
        for debit_id, settlements in debit_id_to_new_settlements_mapping.items():
            debit_aggregate = debit_id_to_debit_aggregate_map.get(debit_id)
            for settlement in settlements:
                self.audit_trail_service.create_settlement_audit_trail(
                    debtor_aggregate,
                    debit_aggregate,
                    settlement,
                    credit_aggregate,
                    debtor_aggregate.hotel_id,
                    AuditType.SETTLEMENT_CREATED,
                    user_data,
                )

    def _create_audit_for_unmapped_settlements(
        self,
        debit_aggregates,
        credit_aggregate,
        debtor_aggregate,
        debit_id_to_unmapped_settlements_mapping,
        user_data,
    ):
        debit_id_to_debit_aggregate_map = {
            debit_aggregate.debit.debit_id: debit_aggregate
            for debit_aggregate in debit_aggregates
        }
        for debit_id, settlements in debit_id_to_unmapped_settlements_mapping.items():
            debit_aggregate = debit_id_to_debit_aggregate_map.get(debit_id)
            for settlement in settlements:
                self.audit_trail_service.create_settlement_audit_trail(
                    debtor_aggregate,
                    debit_aggregate,
                    settlement,
                    credit_aggregate,
                    debtor_aggregate.hotel_id,
                    AuditType.SETTLEMENT_CANCELLED,
                    user_data,
                )

    @staticmethod
    def _link_credit_info_with_settlements(
        settlements, debit_id_debit_map, credit_id_credit_map
    ):
        settlements = [
            SettlementCreditDetailsDto(
                settlement,
                credit_id_credit_map[settlement.credit_id],
                debit_id_debit_map[settlement.debit_id],
            )
            for settlement in settlements
        ]
        return settlements

    @staticmethod
    def _create_tds_settlement(
        credit_aggregate, debit_aggregate, tds_settlement_amount
    ):
        credit_dto = CreditDto(
            debtor_id=credit_aggregate.credit.debtor_id,
            credit_type=CreditType.TDS,
            date=credit_aggregate.credit.date,
            amount_in_base_currency=tds_settlement_amount,
            reference_number=credit_aggregate.credit.reference_number,
            reference_id=credit_aggregate.credit.reference_id,
            mode_of_credit=None,
            amount_in_credit_currency=tds_settlement_amount,
            settlements=[],
            tenant_id=debit_aggregate.tenant_id,
        )
        tds_credit_aggregate = CreditFactory.create_new_credit(credit_dto=credit_dto)
        settlement = debit_aggregate.create_new_settlement(
            tds_credit_aggregate, tds_settlement_amount
        )
        tds_credit_aggregate.knock_off_settlement(settlement)
        return tds_credit_aggregate

    def _get_debtors(
        self,
        debtor_id,
        is_hotel_level_ar_configured,
        user_data,
    ):
        debtors = None
        if (
            (user_data.user_auth_id and user_data.user_type == UserType.FINANCE_POC)
            or is_hotel_level_ar_configured
            or debtor_id
        ):
            debtors, _ = self.debtor_service.get_debtors(
                DebtorSearchQuery(
                    debtor_id=debtor_id,
                    hotel_id=user_data.hotel_id,
                ),
                user_data=user_data,
                hotel_level_accounts_receivable=is_hotel_level_ar_configured,
            )
        return debtors
