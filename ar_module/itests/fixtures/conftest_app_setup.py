import os

import flask
import pytest
from dotenv import load_dotenv
from treebo_commons.multitenancy.sqlalchemy import db_engine

from ar_module import AR_ROOT_DIR
from ar_module.app import create_app
from ar_module.infrastructure.database.models.audit_trail_models import AuditTrailModel
from ar_module.infrastructure.database.models.credit_model import CreditModel
from ar_module.infrastructure.database.models.debit_model import DebitModel
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from ar_module.infrastructure.database.models.settlement_model import SettlementModel
from object_registry import finalize_app_initialization


def setup_app_for_test():
    _app = create_app()
    finalize_app_initialization(_app)
    return _app


def ensure_valid_test_env_setup(app):
    assert app.config["DEBUG"]
    assert app.config["TESTING"], "App Config 'TESTING' must be True for running tests"
    assert (
        os.environ.get("APP_ENV") == "testing"
    ), "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()
    assert all(
        db_creds.dbname == "ar_module_test"
        for tenant_id, db_creds in database_uris.items()
    ), "Database name should be 'ar_module_test' for running tests"
    assert all(
        db_creds.host == "localhost" for tenant_id, db_creds in database_uris.items()
    ), "Database host should be 'localhost' for running tests"


@pytest.fixture(scope="session")
def load_env():
    test_env = AR_ROOT_DIR.joinpath("dotenvs/test.env")
    load_dotenv(test_env, override=True)


@pytest.fixture(scope="session", autouse=True)
def app(load_env):
    _app = setup_app_for_test()
    ensure_valid_test_env_setup(_app)

    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    db_engine.Base.metadata.drop_all(bind=db_engine.get_engine(None))
    db_engine.Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")
    yield _app

    print("===========> Teardown app fixture")
    ctx.pop()


@pytest.fixture(scope="session")
def client_(app, request):
    # test_client = app_.test_client()

    with app.test_client() as client:
        yield client

    while True:
        top = flask._request_ctx_stack.top
        if top is not None and top.preserved:
            top.pop()
        else:
            break

    def teardown():
        pass  # databases and resources have to be freed at the end. But so far we don't have anything

    request.addfinalizer(teardown)
    return client


@pytest.fixture(scope="function", autouse=True)
def clear_data_around_test():
    yield
    print("======>> Clearing All Data")
    db_engine.get_session(None).query(CreditModel).delete()
    db_engine.get_session(None).query(DebitModel).delete()
    db_engine.get_session(None).query(DebtorModel).delete()
    db_engine.get_session(None).query(SettlementModel).delete()
    db_engine.get_session(None).query(AuditTrailModel).delete()
    db_engine.get_session(None).commit()
