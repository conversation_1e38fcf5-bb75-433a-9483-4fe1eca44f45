"""

"""
import json
import logging

from kombu import Connection, Exchange, Queue, binding
from kombu.mixins import ConsumerMixin


class BaseRMQConsumer(ConsumerMixin):
    """
    base class for all rabbit-mq workers that consume from any of the catalog queues
    """

    def __init__(self, config):
        """
        :param config: all necessary configuration needed for the worker
        """

        logger = logging.getLogger(self.__class__.__name__)

        try:
            self.connection = Connection(
                config.rabbitmq_url, transport_options={"confirm_publish": True}
            )

        except Exception as e:
            logger.exception(
                "Error connecting to rabbitmq: %s: %s", config.rabbitmq_url
            )
        self._setup_entities(config)

    def _setup_entities(self, config):
        self._setup_exchanges(config)
        self._setup_queue(config)

    def _setup_exchanges(self, config):
        self.exchange = Exchange(config.exchange_name, type=config.exchange_type)

    def _setup_queue(self, config):
        """
        Setup queue bindings
        """
        bindings = []
        for routing_key in config.routing_keys:
            bindings.append(binding(self.exchange, routing_key=routing_key))
        self.queue = Queue(config.queue_name, bindings, exclusive=config.exclusive)

    def process_message(self, body, message):
        pass

    def get_consumers(self, Consumer, channel):
        consumer = Consumer(queues=[self.queue], callbacks=[self.process_message])
        consumer.qos(prefetch_count=1)
        return [consumer]

    def start_consumer(self):
        try:
            self.run()
        except Exception as e:
            logging.critical(e)

    @staticmethod
    def parse_body(body):
        if isinstance(body, bytes):
            body = body.decode()
        if isinstance(body, str):
            body = json.loads(body)
        return body
