from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import OneOf
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas import validate_positive_integer
from ar_module.application.dtos.credit_dto import SettlementDtoV2
from ar_module.domain.constants import SettlementUploaderAction


class SettlementUploadSchema(Schema):
    payment_amount = MoneyField(required=True, validate=validate_positive_integer)
    payment_ref_id = fields.String()
    credit_id = fields.String()
    invoice_number = fields.String(required=True)
    action = fields.String(
        validate=[
            OneOf(
                [SettlementUploaderAction.MAPPING, SettlementUploaderAction.UNMAPPING]
            )
        ],
        required=True,
    )
    remarks = fields.String()

    @post_load
    def create_object(self, data):
        amount = data.get("payment_amount")
        amount = Money(
            amount.amount, amount.currency or self.context.get("base_currency")
        )
        return SettlementDtoV2(
            amount=amount,
            invoice_number=data.get("invoice_number"),
            payment_id=data.get("payment_ref_id"),
            credit_id=data.get("credit_id"),
            action=data.get("action"),
            remarks=data.get("remarks"),
        )

    @validates_schema
    def validate_data(self, data):
        payment_ref_id = data.get("payment_ref_id", "").strip()
        credit_id = data.get("credit_id", "").strip()
        if not (payment_ref_id or credit_id):
            raise ValidationError("Both credit id and payment id can't be empty")
        data["payment_ref_id"] = payment_ref_id
        data["credit_id"] = credit_id
        return data
