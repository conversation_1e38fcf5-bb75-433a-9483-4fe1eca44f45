from ar_module.domain.aggregates.audit_trail_aggregates import AuditTrailAggregate
from ar_module.domain.constants import SortOrders
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.audit_trail_models import AuditTrailModel
from ar_module.infrastructure.database.repositories.audit_trail.db_adaptor.audit_trail_adaptor import (
    AuditTrailAdaptor,
)
from object_registry import register_instance


@register_instance()
class AuditTrailRepository(BaseRepository):
    audit_trail_adaptor = AuditTrailAdaptor()

    def search_audits(self, filter_criteria=None):
        audit_trail_models = self.query(AuditTrailModel)
        audit_trail_models = self._apply_filters_on_audit_trail_models(
            audit_trail_models, filter_criteria
        )
        if filter_criteria.get("sort_order"):
            if filter_criteria.get("sort_order") == SortOrders.ASC:
                audit_trail_models = audit_trail_models.order_by(
                    AuditTrailModel.created_at.asc()
                )
            else:
                audit_trail_models = audit_trail_models.order_by(
                    AuditTrailModel.created_at.desc()
                )
        else:
            audit_trail_models = audit_trail_models.order_by(
                AuditTrailModel.created_at.desc()
            )
        if (
            filter_criteria.get("limit") is not None
            and filter_criteria.get("offset") is not None
        ):
            audit_trail_models = audit_trail_models.limit(
                filter_criteria.get("limit")
            ).offset(filter_criteria.get("offset"))
        audit_trail_models = audit_trail_models.all()
        return [
            AuditTrailAggregate(self.audit_trail_adaptor.to_domain_entity(db_model))
            for db_model in audit_trail_models
        ]

    def get_audit_trail_count(self, filter_criteria=None):
        audit_trail_models = self.query(AuditTrailModel)
        audit_trail_models = self._apply_filters_on_audit_trail_models(
            audit_trail_models, filter_criteria
        )
        return audit_trail_models.count()

    @staticmethod
    def _apply_filters_on_audit_trail_models(audit_trail_models, filter_criteria):
        if filter_criteria.get("hotel_id"):
            audit_trail_models = audit_trail_models.filter(
                AuditTrailModel.hotel_id == filter_criteria.get("hotel_id")
            )
        if filter_criteria.get("audit_types"):
            audit_trail_models = audit_trail_models.filter(
                AuditTrailModel.audit_type.in_(filter_criteria.get("audit_types"))
            )
        if filter_criteria.get("from_date"):
            audit_trail_models = audit_trail_models.filter(
                AuditTrailModel.created_at >= filter_criteria.get("from_date")
            )
        if filter_criteria.get("to_date"):
            audit_trail_models = audit_trail_models.filter(
                AuditTrailModel.created_at <= filter_criteria.get("to_date")
            )
        return audit_trail_models

    def from_aggregate(self, aggregate: AuditTrailAggregate = None):
        audit_trail = aggregate.audit_trail
        audit_trail_model = self.audit_trail_adaptor.to_db_entity(audit_trail)
        return audit_trail_model

    def save(self, aggregate: AuditTrailAggregate):
        audit_trail_model = self.from_aggregate(aggregate)
        self._save(audit_trail_model)
        self.flush_session()

    def save_all(self, aggregates):
        for audit_trail_aggregate in aggregates:
            self.save(audit_trail_aggregate)
