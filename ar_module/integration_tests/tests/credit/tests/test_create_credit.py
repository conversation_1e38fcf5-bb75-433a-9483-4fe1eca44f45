import pytest

from ar_module.integration_tests.config.common_config import (
    AR_MANAGER,
    CREATE_DEBTOR,
    CREATE_MULTIPLE_DEBIT,
    CREATE_MULTIPLE_DEBTOR_TDS_CASE,
    CREATE_SINGLE_DEBIT,
    CREATE_TDS_DEBTOR,
    ERROR_CODES,
    FDM,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.credit.validations.validation_create_credit import (
    ValidationCreateCredit,
)


class TestCreateCredit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message",
        [
            (
                "CreateCredit_01",
                CREATE_DEBTOR,
                "add credit for debtor with all valid entries",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_02",
                CREATE_DEBTOR,
                "add credit without amount in base currency",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_03",
                CREATE_DEBTOR,
                "add credit without amount in credit currency",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_04",
                CREATE_DEBTOR,
                "add credit without base and credit currency",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_05",
                CREATE_DEBTOR,
                "add credit without without date",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_06",
                CREATE_DEBTOR,
                "add credit without debitor id",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_07",
                CREATE_DEBTOR,
                "add credit with incorrect debitor id",
                404,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_08",
                CREATE_DEBTOR,
                "add credit without settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_09",
                CREATE_DEBTOR,
                "add credit without reference_number",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_10",
                CREATE_DEBTOR,
                "add credit with credit_type as tds",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_11",
                CREATE_DEBTOR,
                "add credit with credit_type as credit_note",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_12",
                CREATE_SINGLE_DEBIT,
                "add credit with amount as null in settlement",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_13",
                CREATE_SINGLE_DEBIT,
                "add credit with tds as null in settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_14",
                CREATE_SINGLE_DEBIT,
                "add credit with settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_15",
                CREATE_SINGLE_DEBIT,
                "add credit and do half settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_16",
                CREATE_SINGLE_DEBIT,
                "add credit and do settlement using tds and amount",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_17",
                CREATE_SINGLE_DEBIT,
                "add credit and settlement using amount more than credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_18",
                CREATE_DEBTOR,
                "add credit with base and credit in different currency",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_19",
                CREATE_DEBTOR,
                "add credit with negative amount in base currency",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_20",
                CREATE_DEBTOR,
                "add credit with negative amount in credit currency",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_21",
                CREATE_DEBTOR,
                "add credit without mode_of_credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_22",
                CREATE_SINGLE_DEBIT,
                "add credit and do settlement with amount = 0",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_23",
                CREATE_SINGLE_DEBIT,
                "add credit and do settlement with tds>amount",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_24",
                CREATE_DEBTOR,
                "add a past dated credit",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_25",
                CREATE_DEBTOR,
                "add a future dated credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_26",
                CREATE_MULTIPLE_DEBIT,
                "add credit and partially settle multiple debits",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_27",
                CREATE_MULTIPLE_DEBIT,
                "add credit and completly settle multiple debits",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_28",
                CREATE_MULTIPLE_DEBIT,
                "add credit and partially settle multiple debits(tds included)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_29",
                CREATE_MULTIPLE_DEBIT,
                "add credit and completly settle multiple debits(tds included)",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_30",
                CREATE_DEBTOR,
                "add credit of INR 3000",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_31",
                CREATE_DEBTOR,
                "add credit with paymode as Razorpay and reference number",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_32",
                CREATE_DEBTOR,
                "add credit with paymode as Bank Transfer and reference number",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_33",
                CREATE_DEBTOR,
                "add credit with paymode as TDS and reference number",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_34",
                CREATE_SINGLE_DEBIT,
                "add credit with paymode as Razorpay and settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_35",
                CREATE_MULTIPLE_DEBIT,
                "add credit and completly settle multiple debits",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_36",
                CREATE_DEBTOR,
                "add credit with reference number as null",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_37",
                CREATE_MULTIPLE_DEBIT,
                "add credit as FDM",
                403,
                FDM,
                None,
            ),
            (
                "CreateCredit_40",
                CREATE_DEBTOR,
                "add credit with paymode as Writeoff and role which is allowed to use this paymode",
                200,
                AR_MANAGER,
                None,
            ),
            (
                "CreateCredit_41",
                CREATE_DEBTOR,
                "add credit with paymode as Writeoff and role which is not allowed to use this paymode",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_42",
                CREATE_DEBTOR,
                "add credit with paymode as Writeoff and reference_number starting from WRO",
                200,
                AR_MANAGER,
                None,
            ),
            (
                "CreateCredit_43",
                CREATE_DEBTOR,
                "add credit with paymode as Writeoff and with approval document",
                200,
                AR_MANAGER,
                None,
            ),
            (
                "CreateCredit_44",
                CREATE_MULTIPLE_DEBTOR_TDS_CASE,
                "add credit with paymode as TDS such that it creates a debit in tds debtor",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_45",
                CREATE_TDS_DEBTOR,
                "add credit with paymode as TDS reconciled for tds debtor",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CreateCredit_46",
                CREATE_TDS_DEBTOR,
                "add credit with paymode as razorpay for tds debtor",
                400,
                SUPER_ADMIN,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_create_credit(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[0]
        self.debit_request.debit_id.clear()
        self.debtor_request.debtor_ids.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.credit_request.create_new_credit_request(
            client_,
            test_case_id,
            self.debtor_request.debtor_id,
            self.debit_request.debit_id,
            status_code,
            user_type,
            hotel_id,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                test_case_id,
                response,
                self.credit_request,
                self.audit_trail_request,
                self.debtor_request.debtor_id,
                self.debit_request,
                self.debtor_request.debtor_ids,
                hotel_id,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_,
        test_case_id,
        response,
        credit_request,
        audit_trail_request,
        debtor_id,
        debit_request,
        debtor_ids,
        hotel_id,
    ):
        validation = ValidationCreateCredit(
            client_,
            test_case_id,
            response,
            credit_request,
            audit_trail_request,
            debtor_id,
            debit_request,
            debtor_ids,
            hotel_id,
        )
        validation.validate_response()
        validation.validate_all_credits_response()
        validation.validate_audit_trail()
        validation.validate_linked_debit()
