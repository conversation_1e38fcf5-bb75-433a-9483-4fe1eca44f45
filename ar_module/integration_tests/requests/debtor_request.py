import json

from ar_module.integration_tests.builders import (
    create_debtor_builder,
    preview_debtor_summary_builder,
)
from ar_module.integration_tests.config.common_config import SUCCESS_CODES
from ar_module.integration_tests.config.request_uris import (
    CREATE_DEBTOR_URI,
    GET_DEBTORS_URI,
    PREVIEW_DEBTOR_SUMMARY_URI,
)
from ar_module.integration_tests.config.sheet_names import CREATE_DEBTOR_SHEET_NAME
from ar_module.integration_tests.mockers import (
    mock_chain_base_currency,
    mock_get_poc_details,
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
)
from ar_module.integration_tests.requests.base_request import BaseRequest
from ar_module.integration_tests.utilities.common_utils import del_none


class DebtorRequest(BaseRequest):
    def create_new_debtor_request(
        self, client, test_case_id, status_code, user_type, config_value=True
    ):
        request_json = json.dumps(
            del_none(
                create_debtor_builder.CreateDebtorBuilder(
                    CREATE_DEBTOR_SHEET_NAME, test_case_id
                ).__dict__
            )
        )
        hotel_id = json.loads(request_json)["data"]["hotel_id"]
        with mock_get_tenant_configs(
            config_value
        ), mock_is_hotel_level_accounts_receivable_configured(config_value):
            with mock_get_roles():
                response = self.request_processor(
                    client,
                    "POST",
                    CREATE_DEBTOR_URI,
                    status_code,
                    request_json,
                    user_type=user_type,
                    hotel_id=hotel_id,
                )
        if status_code in SUCCESS_CODES:
            self.debtor_id = response["data"]["debtor"]["debtor_id"]
            self.hotel_id = response["data"]["debtor"]["hotel_id"]
            self.debtor_ids.append(response["data"]["debtor"]["debtor_id"])
        return response

    def get_debtors_request(
        self,
        client,
        status_code,
        hotel_id,
        user_type,
        config_value=False,
        query_param=None,
    ):
        uri = (
            GET_DEBTORS_URI + query_param
            if query_param
            else GET_DEBTORS_URI + f"hotel_id={hotel_id}"
        )
        with mock_get_tenant_configs(
            config_value
        ), mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ), mock_get_roles():
            response = self.request_processor(
                client, "GET", uri, status_code, user_type=user_type
            )
        return response

    def preview_debtor_summary_request(
        self,
        client,
        status_code,
        user_type,
        debtor_id,
        from_date,
        to_date,
        config_value=False,
    ):
        request_json = json.dumps(
            del_none(
                preview_debtor_summary_builder.PreviewDebtorSummaryBuilder(
                    debtor_id, from_date, to_date
                ).__dict__
            )
        )
        with mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ), mock_chain_base_currency(), mock_get_poc_details():
            with mock_get_roles():
                response = self.request_processor(
                    client,
                    "POST",
                    PREVIEW_DEBTOR_SUMMARY_URI,
                    status_code,
                    request_json,
                    user_type=user_type,
                )
        return response
