import logging

from ar_module.core.common.globals import global_context
from ar_module.domain.policy.facts.facts import Facts
from ar_module.domain.policy.rules.cancel_credit_reversal_rule import (
    CancelCreditReversalRule,
)
from ar_module.domain.policy.rules.cancel_credit_rule import CancelCreditRule
from ar_module.domain.policy.rules.create_credit_reversal_rule import (
    CreateCreditReversalRule,
)
from ar_module.domain.policy.rules.create_credit_rule import CreateCreditRule
from ar_module.domain.policy.rules.create_manual_debit_rule import CreateManualDebitRule
from ar_module.domain.policy.rules.create_new_debtor_rule import CreateNewDebtorRule
from ar_module.domain.policy.rules.create_settlements_rule import CreateSettlementsRule
from ar_module.domain.policy.rules.view_credit_rule import ViewCreditRule
from ar_module.domain.policy.rules.view_debtor_summary_rule import ViewDebtorSummaryRule
from ar_module.domain.policy.rules.view_debtors_rule import ViewDebtorsRule
from ar_module.exceptions import PolicyAuthException
from ar_module.infrastructure.external_clients.role_privilege.role_privilege_client import (
    RoleManagerClient,
)
from ar_module.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)

logger = logging.getLogger(__name__)


class RuleEngine(object):
    action_rule_map = dict(
        create_manual_debit=CreateManualDebitRule,
        create_new_debtor=CreateNewDebtorRule,
        create_credit=CreateCreditRule,
        create_credit_reversal=CreateCreditReversalRule,
        cancel_credit_reversal=CancelCreditReversalRule,
        create_settlements=CreateSettlementsRule,
        view_credit=ViewCreditRule,
        view_debtors=ViewDebtorsRule,
        cancel_credit=CancelCreditRule,
        view_debtor_summary=ViewDebtorSummaryRule,
    )

    @staticmethod
    def action_allowed(action, facts: Facts, fail_on_error=False):
        rule = RuleEngine.action_rule_map.get(action)
        assert rule is not None, f"mapping not defined for {action} in action_rule_map"

        privileges = dict()
        if global_context.privileges_as_dict is not None:
            privileges = global_context.privileges_as_dict
        else:
            if facts.user_type:
                logger.debug(
                    "Getting user_type {0} for action {1}".format(
                        facts.user_type, action
                    )
                )
                try:
                    role_privilege_dtos = (
                        RoleManagerClient().get_privilege_by_role_name(facts.user_type)
                    )
                except:
                    role_privilege_dtos = []

                privileges = RolePrivilegesDTO.array_to_dict(
                    role_privilege_dtos=role_privilege_dtos
                )
                global_context.privileges_as_dict = privileges
                global_context.role_privilege_dtos = role_privilege_dtos

        try:
            return rule().allow(facts, privileges)
        except PolicyAuthException as pae:
            if fail_on_error:
                raise
            logger.debug(
                "Policy evaluation failed for action: %s: %s", action, pae.message
            )
            return False
