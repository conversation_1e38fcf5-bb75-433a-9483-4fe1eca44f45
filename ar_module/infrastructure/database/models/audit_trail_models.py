from sqlalchemy import <PERSON><PERSON><PERSON>, NUMERIC, Column, Date, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)


class AuditTrailModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "audit_trail"

    audit_id = Column(String, primary_key=True)
    user = Column("user", String)
    user_type = Column(String)
    request_id = Column(String)
    hotel_id = Column(String, index=True)
    audit_type = Column(String)
    audit_payload = Column(JSON)
