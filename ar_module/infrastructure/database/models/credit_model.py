from sqlalchemy import <PERSON><PERSON>ER<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)


class CreditModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit"

    credit_id = Column(String, primary_key=True)
    debtor_id = Column(String)
    credit_type = Column(String)
    credit_date = Column(Date)
    reference_number = Column(String)
    reference_id = Column(String)
    tenant_id = Column(String)
    mode_of_credit = Column(String)
    amount_in_base_currency = Column(NUMERIC)
    base_currency = Column(String)
    unused_credit_amount = Column(NUMERIC)
    refunded_amount = Column(NUMERIC)
    amount_in_credit_currency = Column(NUMERIC)
    credit_currency = Column(String)
    used_to_auto_settle_debit = Column(Boolean)
    linked_debit_id = Column(String)
    status = Column(String)
    approval_document = Column(String)
    cancellation_date = Column(Date)
    recorded_via = Column(String)
    cancellation_reason = Column(String)


class CreditReferenceNumberSeriesModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_reference_number_series"

    series_id = Column(Integer, primary_key=True)
    debtor_id = Column(String)
    credit_type = Column(String)
    credit_date = Column(Date)
    last_sequence_number = Column(Integer, default=0)


class CreditReversalMappingModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "credit_reversal_mapping"

    id = Column(Integer, primary_key=True, autoincrement=True)
    payment_credit_id = Column(
        String, ForeignKey("credit.credit_id", ondelete="CASCADE"), nullable=False
    )
    refund_credit_id = Column(
        String, ForeignKey("credit.credit_id", ondelete="CASCADE"), nullable=False
    )
    amount_in_credit_currency = Column(NUMERIC)
    amount_in_base_currency = Column(NUMERIC)
    remarks = Column(String)
    status = Column(String)
    base_currency = Column(String)
    credit_currency = Column(String)
    cancellation_date = Column(Date)

    payment_credit = relationship("CreditModel", foreign_keys=[payment_credit_id])
    refund_credit = relationship("CreditModel", foreign_keys=[refund_credit_id])
