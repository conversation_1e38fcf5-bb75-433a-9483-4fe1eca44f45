import datetime
import os
import uuid
from collections import OrderedDict
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from pathlib import Path
from typing import List

from flask import current_app as app
from treebo_commons.aws_services.s3_client import S3Client
from treebo_commons.utils import dateutils

from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.reporting.debtor_summary_reports.mis.invoice_archiver import (
    FileMeta,
    InputFileMetaForInvoiceArchiver,
    InvoiceArchiver,
)
from ar_module.application.services.reporting.dtos import (
    DebitLineItemDto,
    DebtorSummaryRequestData,
)
from ar_module.application.services.reporting.excel_writer import ExcelWriter
from ar_module.common.tenant_utils import get_tenant_id
from ar_module.domain.constants import AuditType, CreditType, SettlementStatus
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

MIS_S3_PATH = "ar-reports/"
MAX_FILE_SIZ_FOR_ATTACHMENT = 24


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        CommunicationServiceClient,
        AuditTrailService,
    ]
)
class MISReminderDispatcher:

    debits_report_column_mapping = OrderedDict(
        dict(
            debit_date="Invoice Date",
            reference_number="Invoice Number",
            posttax_amount="Invoice Amount",
            unsettled_amount="Pending Amount",
            booking_reference_number="Booking Id",
            due_date="Due Date",
            pretax_amount="Pre Tax",
            tax_amount="Tax",
            guest_name="Guest Name",
            hotel_name="Hotel Name",
            checkin="Check In",
            checkout="Check Out",
        )
    )

    credits_report_column_mapping = OrderedDict(
        dict(
            credit_date="Payment Date",
            reference_number="Payment Id",
            amount_in_base_currency="Payment Amount",
            unused_credit_amount="Unused Amount",
            mode_of_credit="Mode of Payment",
        )
    )

    email_identifier = "ar_mis_communication"

    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        communication_service_client: CommunicationServiceClient,
        audit_trail_service: AuditTrailService,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.communication_service_client = communication_service_client
        self.audit_trail_service = audit_trail_service

    def dispatch(self, request: DebtorSummaryRequestData):
        debtor = self.debtor_repo.load(request.debtor_id)
        name = debtor.debtor_name
        from_date = request.from_date
        to_date = request.to_date
        report_filename = self._generate_report_filename(name, from_date, to_date)
        mis_report_file_path = os.path.join(
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/"), report_filename
        )
        debit_search_query = DebitSearchQuery(
            debtor_id=request.debtor_id,
            from_date=request.from_date,
            to_date=request.to_date,
        )
        credit_search_query = CreditSearchQuery(
            debtor_id=request.debtor_id,
            from_date=request.from_date,
            to_date=request.to_date,
            show_cancelled_credits=False,
            credit_types=[CreditType.PAYMENT, CreditType.TDS, CreditType.CREDIT_NOTE],
        )

        if not request.include_mapped_records:
            debit_search_query.settlement_status = [
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ]
            credit_search_query.has_unused_credit = True

        # TODO if data volume is increasing above 10k use yield, currently it is below 500
        # this report can only be generated for range of 31 days
        debits = self.debit_repo.search_debits(debit_search_query, debits_only=True)
        debits = [DebitLineItemDto.create_from_debit_data(debit) for debit in debits]
        credit_items = self.credit_repo.load_credits(
            credit_search_query, credits_only=True
        )

        total_open_balance = 0
        total_unmapped_amount = 0
        invoice_files_to_process = []
        for debit in debits:
            if debit.debit_template_url:
                invoice_files_to_process.append(
                    InputFileMetaForInvoiceArchiver(
                        file_url=debit.debit_template_url,
                        file_name=f"{debit.reference_number or uuid.uuid4()}.pdf",
                    )
                )
            total_open_balance += debit.unsettled_amount

        for credit in credit_items:
            total_unmapped_amount += credit.unused_credit_amount

        net_payable = total_open_balance - total_unmapped_amount

        invoice_archive_file_meta = None
        if invoice_files_to_process:
            invoice_archive_file_meta: FileMeta = (
                self._prepare_and_upload_invoice_archive(invoice_files_to_process)
            )

        with ExcelWriter(mis_report_file_path) as excel_writer:
            if debits:
                excel_writer.write_aggregates(
                    debits, self.debits_report_column_mapping, sheet_name="Invoice"
                )
            if credit_items:
                excel_writer.write_aggregates(
                    credit_items,
                    self.credits_report_column_mapping,
                    sheet_name="Payments",
                )
            if not debits and credit_items:
                signed_url_of_mis_reports = None
            else:
                excel_writer.save_file()
                signed_url_of_mis_reports = (
                    S3Client.upload_file_to_s3_and_get_cloudfront_signed_url(
                        excel_writer.file_path,
                        MIS_S3_PATH,
                        get_tenant_id(),
                        self.get_default_expiration_time(),
                    )
                )
        self._send_email_communication(
            request,
            debtor,
            signed_url_of_mis_reports,
            invoice_archive_file_meta,
            net_payable,
            total_open_balance,
            total_unmapped_amount,
            mis_report_file_path,
        )
        self.audit_trail_service.create_debtor_communication_audit_trail(
            self.email_identifier,
            debtor,
            AuditType.DEBTOR_COMMUNICATION,
            request.user_data,
        )

    @staticmethod
    def _generate_report_filename(name, from_date, to_date):

        safe_debtor_name = "".join(
            c if c.isalnum() or c in (" ", "_", "-") else "_" for c in name
        )
        from_date_str = dateutils.ymd_str_to_date(from_date).strftime("%d %b %Y")
        to_date_str = dateutils.ymd_str_to_date(to_date).strftime("%d %b %Y")
        report_filename = (
            f"Statement - {safe_debtor_name}_{from_date_str}_{to_date_str}.xlsx"
        )
        return report_filename

    @staticmethod
    def get_default_expiration_time():
        return 2592000

    @staticmethod
    def _prepare_and_upload_invoice_archive(
        files_to_process: List[InputFileMetaForInvoiceArchiver],
    ) -> FileMeta:
        invoice_archiver = InvoiceArchiver()
        with ThreadPoolExecutor(
            max_workers=int(app.config["MAX_THREAD_FOR_PDF_GENERATION"])
        ) as executor:
            executor.map(
                invoice_archiver.download_file,
                files_to_process,
            )
        return invoice_archiver.prepare_archive_and_upload()

    def _send_email_communication(
        self,
        request: DebtorSummaryRequestData,
        debtor,
        mis_report_url,
        invoice_file_meta: FileMeta,
        net_payable,
        total_open_balance,
        total_unmapped_amount,
        mis_report_file_path,
    ):
        subject = (
            f"{debtor.debtor_code} - {debtor.debtor_name} - MIS - "
            f"{dateutils.ymd_str_to_date(request.from_date).strftime('%d %B %Y')} - "
            f"{dateutils.ymd_str_to_date(request.to_date).strftime('%d %B %Y')}"
        )
        context_data = dict(
            customer_name=debtor.debtor_name,
            from_date=request.from_date,
            to_date=request.to_date,
            report_date=str(datetime.datetime.today()),
            net_payable=net_payable,
            total_open_balance=total_open_balance,
            total_unmapped_amount=total_unmapped_amount,
        )
        attachments = [
            dict(url=mis_report_url, filename=Path(mis_report_file_path).name)
        ]
        if invoice_file_meta:
            context_data["invoice_archive_url"] = invoice_file_meta.signed_url
        self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=request.to_recipients,
            cc=request.cc_recipients,
            subject=subject,
            attachments=attachments,
        )

    @staticmethod
    def _should_attach_invoice_archive_file_or_not(invoice_file_meta: FileMeta):
        return invoice_file_meta.file_size < MAX_FILE_SIZ_FOR_ATTACHMENT
