from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>teger, String, Table
from sqlalchemy.orm import backref, relationship
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)

user_debtors = Table(
    "user_debtors",
    Base.metadata,
    Column("id", Integer, primary_key=True),
    Column("user_id", String, ForeignKey("user.user_id")),
    Column("debtor_id", String, ForeignKey("debtor.debtor_id")),
)


class UserModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "user"

    user_id = Column(String, primary_key=True)
    email = Column(String)
    phone_number = Column(String)
    first_name = Column(String)
    last_name = Column(String)

    debtors = relationship(
        "DebtorModel", secondary=user_debtors, backref=backref("users", lazy="dynamic")
    )
