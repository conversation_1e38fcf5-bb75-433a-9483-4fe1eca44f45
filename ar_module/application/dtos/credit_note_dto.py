from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from ar_module.common.tenant_utils import TenantMixin


class CreditNoteDto(TenantMixin):
    def __init__(
        self,
        debtor_id,
        credit_note_id,
        credit_note_number,
        credit_note_date,
        credit_note_amount: Money,
        line_items,
        vendor_id,
        tenant_id=None,
    ):
        super().__init__(tenant_id)
        self.debtor_id = debtor_id
        self.credit_note_id = credit_note_id
        self.credit_note_number = credit_note_number
        self.credit_note_date = credit_note_date
        self.credit_note_amount = credit_note_amount
        self.line_items = line_items
        self.vendor_id = vendor_id

    @property
    def invoice_wise_amounts(self):
        invoice_wise_amounts = dict()
        for line_item in self.line_items:
            amount = Money(line_item.get("posttax_amount"), CurrencyType.INR)
            if line_item.get("invoice_id") in invoice_wise_amounts:
                invoice_wise_amounts[line_item.get("invoice_id")] += amount
            else:
                invoice_wise_amounts[line_item.get("invoice_id")] = amount
        return invoice_wise_amounts
