import logging

import click
from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from ar_module.application.consumers.decorator import consumer_middleware
from ar_module.async_job.handlers.register import register_job_handlers
from ar_module.async_job.job.aggregates.job_aggregate import JobAggregate
from ar_module.async_job.job.job_constants import JobStatus
from ar_module.async_job.job.repositories.job_repository import JobRepository
from ar_module.async_job.job_registry import JobRegistry
from ar_module.async_job.job_result_dto import JobResultDto
from ar_module.async_job.job_scheduler_service import JobSchedulerService
from ar_module.core.threadlocal import consumer_context
from ar_module.infrastructure.consumers.base_consumer import BaseRMQConsumer
from ar_module.infrastructure.consumers.consumer_config import ARAsyncJobConsumerConfig
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("async_job_executor")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    job_registry=JobRegistry,
    job_repository=JobRepository,
    job_scheduler=JobSchedulerService,
)
def async_job_executor(
    job_registry: JobRegistry,
    job_repository: JobRepository,
    job_scheduler: JobSchedulerService,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = AsyncJobConsumer(job_registry, job_repository, job_scheduler, tenant_id)
    consumer.start_consumer()


class AsyncJobConsumer(BaseRMQConsumer):
    def __init__(
        self,
        job_registry: JobRegistry,
        job_repository: JobRepository,
        job_scheduler: JobSchedulerService,
        tenant_id=TenantClient.get_default_tenant(),
    ):
        super().__init__(ARAsyncJobConsumerConfig(tenant_id))
        self.tenant_id = tenant_id
        self.job_registry = job_registry
        self.job_repository = job_repository
        self.job_scheduler = job_scheduler
        register_job_handlers(job_registry)

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        logger.info("Received Async Job event, Ack")

        with current_app.test_request_context():
            try:
                body = self.parse_body(body)
                job_id = body.get("data", {}).get("job_id")
                if job_id is None:
                    return
                logger.info(f"Fetching job for execution: job id: {job_id}")
                job_aggregate = self.job_repository.get_job_for_execution(job_id)
                if not job_aggregate or not job_aggregate.job_entity:
                    logger.info("Eligible jobs not found..")
                    return
                logger.info(
                    "Executing job_name: %s, id: %s",
                    job_aggregate.job_entity.job_name,
                    job_aggregate.job_entity.job_id,
                )
                process_job(
                    self.job_registry,
                    job_aggregate,
                    self.job_repository,
                    self.job_scheduler,
                )
            except Exception as e:
                logger.exception("Error while executing job")

        logger.info("Completed processing Async Job event")


@unit_of_work
def process_job(
    job_registry,
    job_aggregate: JobAggregate,
    job_repository: JobRepository,
    job_scheduler: JobSchedulerService,
):
    job_entity = job_aggregate.job_entity
    handler_function = job_registry.get_executor(job_entity.job_name)
    job_result_dto: JobResultDto = handler_function(job_entity)
    update_job(job_aggregate, job_repository, job_result_dto, job_scheduler)


@unit_of_work
def update_job(job_aggregate, job_repository, job_result_dto, job_scheduler):
    if job_result_dto:
        logger.info(
            "Job execution status for job id: %s -> %s",
            job_aggregate.job_entity.job_id,
            job_result_dto.run_successful,
        )
        if not job_result_dto.run_successful:
            if job_result_dto.should_retry:
                job_aggregate.reschedule(
                    job_result_dto.retry_at_eta, reason=job_result_dto.remarks
                )
            else:
                job_aggregate.failed(job_result_dto.remarks)
        else:
            job_aggregate.update_status(success=True)
    else:
        logger.info(
            "Job execution status for job id: %s -> %s",
            job_aggregate.job_entity.job_id,
            True,
        )
        job_aggregate.update_status(success=True)
    job_repository.update_job(job_aggregate)
    if job_aggregate.job_entity.status == JobStatus.RESCHEDULED:
        job_scheduler.publish_job_for_execution(job_aggregate)
