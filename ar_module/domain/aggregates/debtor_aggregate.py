from ar_module.domain.entities.debtor import Debtor


class DebtorAggregate(object):
    def __init__(self, debtor: Debtor):
        self.debtor = debtor

    @property
    def debtor_id(self):
        return self.debtor.debtor_id

    @property
    def debtor_name(self):
        return self.debtor.debtor_name

    @property
    def debtor_code(self):
        return self.debtor.debtor_code

    @property
    def debtor_type(self):
        return self.debtor.debtor_type

    @property
    def hotel_id(self):
        return self.debtor.hotel_id

    def is_linked_to_hotel(self):
        return self.debtor.hotel_id is not None

    @property
    def credit_period(self):
        return self.debtor.credit_period

    @property
    def credit_limit(self):
        return self.debtor.credit_limit
