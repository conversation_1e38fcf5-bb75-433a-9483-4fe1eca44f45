-- revision: '20231120231108_add_due_date_col_debit_table'
-- down_revision: '20231128162400_async_job_table'

-- upgrade
ALTER TABLE debit add column due_date DATE;
ALTER TABLE debit add column remarks VARCHAR;
ALTER TABLE debit add column auto_settled_via_credit boolean DEFAULT FALSE;
ALTER TABLE credit add column used_to_auto_settle_debit boolean DEFAULT FALSE;
DROP INDEX uidx_debit_reference_number_non_cancelled;

-- downgrade
ALTER TABLE debit DROP COLUMN due_date;
ALTER TABLE debit DROP COLUMN remarks;
ALTER TABLE debit DROP COLUMN auto_settled_via_credit;
ALTER TABLE credit DROP COLUMN used_to_auto_settle_debit;
CREATE UNIQUE INDEX uidx_debit_reference_number_non_cancelled ON debit (reference_number) WHERE (settlement_status != 'cancelled');