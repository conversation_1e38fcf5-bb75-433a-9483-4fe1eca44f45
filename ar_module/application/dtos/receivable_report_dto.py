from typing import List


class SummarisedReceivableReportDto(object):
    def __init__(
        self,
        debtors_count=None,
        total_unapplied_payments=None,
        total_gross_receivables=None,
        total_net_receivables=None,
        days_of_sales_os=None,
        age_of_receivables=None,
    ):
        self.total_gross_receivables = total_gross_receivables
        self.total_net_receivables = total_net_receivables
        self.total_unapplied_payments = total_unapplied_payments
        self.debtors_count = debtors_count
        self.days_of_sales_os = days_of_sales_os
        self.age_of_receivables = age_of_receivables

    def add(self, debtor_id, unapplied_payment, gross_receivables, net_receivables):
        if not self.debtors:
            self.debtors = [debtor_id]
            self.total_unapplied_payments = unapplied_payment
            self.total_gross_receivables = gross_receivables
            self.total_net_receivables = net_receivables
        else:
            self.debtors.append(debtor_id)
            self.total_unapplied_payments += unapplied_payment
            self.total_gross_receivables += gross_receivables
            self.total_net_receivables += net_receivables


class DetailedReceivableReportDto(object):
    def __init__(
        self,
        debtor_code,
        debtor_name,
        debtor_id,
        total_credits,
        total_provisional_credits,
        unapplied_payment,
        gross_receivables,
        net_receivables,
        oldest_due,
        debtor_type=None,
    ):
        self.debtor_id = debtor_id
        self.debtor_code = debtor_code
        self.debtor_name = debtor_name
        self.debtor_type = debtor_type
        self.total_credits = total_credits
        self.total_provisional_credits = total_provisional_credits
        self.unapplied_payment = unapplied_payment
        self.gross_receivables = gross_receivables
        self.net_receivables = net_receivables
        self.oldest_due = oldest_due


class ReceivableReportDto(object):
    def __init__(self, detailed_report_dtos: List[DetailedReceivableReportDto]):
        self.detailed_report_dtos = detailed_report_dtos
        self.summarised_report_dtos = SummarisedReceivableReportDto()
        for detailed_report_dto in detailed_report_dtos:
            self.summarised_report_dtos.add(
                detailed_report_dto.debtor_id,
                detailed_report_dto.unapplied_payment,
                detailed_report_dto.gross_receivables,
                detailed_report_dto.net_receivables,
            )

    @property
    def detailed_report(self):
        return self.detailed_report_dtos

    @property
    def summarised_report(self):
        return self.summarised_report_dtos


class MonthWiseReceivableReportDto(object):
    def __init__(self, start_date, end_date, receivable_report: ReceivableReportDto):
        self.start_date = start_date
        self.end_date = end_date
        self.receivable_report = receivable_report


class ReceivableReportAndURLDto(object):
    def __init__(
        self,
        month_wise_summary,
        detailed_report,
        summarised_report_download_url,
        detailed_report_download_url,
    ):
        self.month_wise_summary = month_wise_summary
        self.detailed_report = detailed_report
        self.summarised_report_download_url = summarised_report_download_url
        self.detailed_report_download_url = detailed_report_download_url


class MonthWiseSummaryReportDto(object):
    def __init__(self, start_date, end_date, summary: SummarisedReceivableReportDto):
        self.start_date = start_date
        self.end_date = end_date
        self.summary = summary

    @property
    def total_gross_receivables(self):
        return self.summary.total_gross_receivables

    @property
    def total_net_receivables(self):
        return self.summary.total_net_receivables

    @property
    def total_unapplied_payments(self):
        return self.summary.total_unapplied_payments

    @property
    def debtors_count(self):
        return self.summary.debtors_count

    @property
    def days_of_sales_os(self):
        return self.summary.days_of_sales_os

    @property
    def aor(self):
        aor_age_to_receivables_mapping = (
            {aor["age"]: aor["receivables"] for aor in self.summary.age_of_receivables}
            if self.summary.age_of_receivables
            else {}
        )
        return {
            "0-30": aor_age_to_receivables_mapping.get("0-30", 0),
            "30-60": aor_age_to_receivables_mapping.get("30-60", 0),
            "60-90": aor_age_to_receivables_mapping.get("60-90", 0),
            "90-180": aor_age_to_receivables_mapping.get("90-180", 0),
            "180-365": aor_age_to_receivables_mapping.get("180-365", 0),
            "greater_than_365": aor_age_to_receivables_mapping.get(
                "greater_than_365", 0
            ),
        }


class ReceivableReportDtoV2(object):
    def __init__(
        self,
        detailed_report,
        summarised_report,
        detailed_report_download_url,
        summarised_report_download_url,
    ):
        self.detailed_report = detailed_report
        self.summarised_report = summarised_report
        self.detailed_report_download_url = detailed_report_download_url
        self.summarised_report_download_url = summarised_report_download_url
