from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.constants import Modules


class DebitDto(TenantMixin):
    def __init__(
        self,
        debtor_id,
        debit_type,
        debit_date,
        due_date,
        debit_amount,
        reference_number,
        reference_id,
        recorded_via=Modules.AR_MODULE,
        tenant_id=None,
    ):
        super().__init__(tenant_id)
        self.debtor_id = debtor_id
        self.debit_type = debit_type
        self.debit_date = debit_date
        self.due_date = due_date
        self.debit_amount = debit_amount
        self.reference_number = reference_number
        self.reference_id = reference_id
        self.recorded_via = recorded_via
