import json

from ar_module.integration_tests.config.common_config import (
    CREDIT_CANCELLED,
    CREDIT_CREATED,
    SETTLEMENT_CANCELLED,
    SETTLEMENT_CREATED,
    SUPER_ADMIN,
)
from ar_module.integration_tests.utilities.common_utils import assert_


class BaseValidations(object):
    def validate_audit_data(
        self,
        client,
        test_data,
        audit_type,
        audit_trail_request,
        credit_request,
        debit_request=None,
    ):
        audit_trail_response = audit_trail_request.get_audit_trail(
            client, 200, SUPER_ADMIN
        )
        expected_data = json.loads(test_data[f"expected_{audit_type}_audit_trail"])
        actual_data = []
        for audit_trail in audit_trail_response["data"]["audit_trails"]:
            if audit_trail["audit_type"] == audit_type:
                actual_data.append(audit_trail)

        if audit_type in [SETTLEMENT_CREATED, SETTLEMENT_CANCELLED]:
            actual_data = sorted(
                actual_data,
                key=lambda i: (
                    i["audit_payload"]["settlement_details"]["applied_amount"],
                    i["created_at"],
                ),
            )
            expected_data = sorted(
                expected_data,
                key=lambda i: (
                    i["audit_payload"]["settlement_details"]["applied_amount"],
                    i["created_at"],
                ),
            )

        for actual_audit_trail, expected_audit_trail in zip(actual_data, expected_data):
            assert_(
                actual_audit_trail["audit_type"], expected_audit_trail["audit_type"]
            )
            assert_(actual_audit_trail["user_type"], expected_audit_trail["user_type"])
            assert_(
                actual_audit_trail["audit_payload"]["credit_id"],
                credit_request.credit_id,
            )
            assert_(
                actual_audit_trail["audit_payload"]["debtor_code"],
                expected_audit_trail["audit_payload"]["debtor_code"],
            )
            assert_(
                actual_audit_trail["audit_payload"]["debtor_name"],
                expected_audit_trail["audit_payload"]["debtor_name"],
            )
            assert_(
                actual_audit_trail["audit_payload"]["hotel_id"],
                expected_audit_trail["audit_payload"]["hotel_id"],
            )
            assert_(
                actual_audit_trail["audit_payload"]["payment_reference_number"],
                credit_request.credit_reference_number,
            )

            if audit_type in [CREDIT_CREATED, CREDIT_CANCELLED]:
                assert_(
                    actual_audit_trail["audit_payload"]["credit_amount"],
                    expected_audit_trail["audit_payload"]["credit_amount"],
                )
                assert_(
                    actual_audit_trail["audit_payload"]["credit_currency"],
                    expected_audit_trail["audit_payload"]["credit_currency"],
                )
                assert_(
                    actual_audit_trail["audit_payload"]["credit_mode"],
                    expected_audit_trail["audit_payload"]["credit_mode"],
                )

                if actual_audit_trail["audit_payload"]["mapped_invoices"]:
                    actual_mapped_invoices = sorted(
                        actual_audit_trail["audit_payload"]["mapped_invoices"].split(
                            ","
                        )
                    )
                    expected_mapped_invoices = sorted(
                        expected_audit_trail["audit_payload"]["mapped_invoices"].split(
                            ","
                        )
                    )
                    if assert_(
                        len(actual_mapped_invoices), len(expected_mapped_invoices)
                    ):
                        for actual_invoice, expected_invoice in zip(
                            actual_mapped_invoices, expected_mapped_invoices
                        ):
                            assert_(actual_invoice, expected_invoice)

            else:
                assert_(
                    actual_audit_trail["audit_payload"]["mode_of_credit"],
                    expected_audit_trail["audit_payload"]["mode_of_credit"],
                )
                assert_(
                    actual_audit_trail["audit_payload"]["settlement_details"][
                        "applied_amount"
                    ],
                    expected_audit_trail["audit_payload"]["settlement_details"][
                        "applied_amount"
                    ],
                )
                assert (
                    actual_audit_trail["audit_payload"]["settlement_details"][
                        "debit_id"
                    ]
                    in debit_request.debit_id
                )
                assert (
                    actual_audit_trail["audit_payload"]["settlement_details"][
                        "reference_number"
                    ]
                    in debit_request.debit_reference_numbers
                )
