import logging
from collections import defaultdict

from marshmallow import ValidationError

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.core.common.globals import global_context
from ar_module.domain.constants import SettlementStatus, UserType
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.debtor_factory import DebtorFactory
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.facts.create_new_debtor_facts import CreateNewDebtorFacts
from ar_module.domain.policy.facts.view_debtors_facts import ViewDebtorsFacts
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DebtorRepository,
        DebitRepository,
        CreditRepository,
        UserRepository,
        TenantSettings,
    ]
)
class DebtorService(object):
    def __init__(
        self,
        debtor_repo: DebtorRepository,
        debit_repo: DebitRepository,
        credit_repo: CreditRepository,
        user_repo: UserRepository,
        tenant_settings: TenantSettings,
    ):
        self.debtor_repo = debtor_repo
        self.user_repo = user_repo
        self.debit_repo = debit_repo
        self.credit_repo = credit_repo
        self.tenant_settings = tenant_settings

    def search_debtors(self, **kwargs):
        RuleEngine.action_allowed(
            action="view_debtors",
            facts=ViewDebtorsFacts(
                user_type=kwargs.get("user_data").user_type
                if kwargs.get("user_data")
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        # If AR Module is configured to function at chain level, then we'll have to search for debtors
        # without hotel_id else we search with hotel_id provided.
        if not hotel_level_accounts_receivable:
            kwargs["hotel_id"] = None
        else:
            if not kwargs.get("hotel_id"):
                raise ValidationError(
                    "Hotel id is mandatory if ar is configured on hotel level"
                )
        return self.debtor_repo.search_debtors(DebtorSearchQuery(**kwargs))

    def get_debtors(
        self,
        debtor_search_query,
        get_total_count=False,
        user_data=None,
        hotel_level_accounts_receivable=None,
    ):
        RuleEngine.action_allowed(
            action="view_debtors",
            facts=ViewDebtorsFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        debtor_aggregates, total_debtors = [], 0
        if user_data.user_auth_id and user_data.user_type == UserType.FINANCE_POC:
            debtors = self.user_repo.get_debtors_associated_with_user(
                user_data.user_auth_id
            )
            if not debtors:
                return debtor_aggregates, total_debtors
            user_accessible_debtors = [debtor.debtor_id for debtor in debtors]
            debtor_search_query.user_accessible_debtors_id = user_accessible_debtors
        if hotel_level_accounts_receivable is None:
            hotel_level_accounts_receivable = (
                self.tenant_settings.is_hotel_level_accounts_receivable_configured()
            )
        # If AR Module is configured to function at chain level, then we'll have to search for debtors
        # without hotel_id else we search with hotel_id provided.
        if not hotel_level_accounts_receivable:
            debtor_search_query.hotel_id = None
        else:
            if not debtor_search_query.hotel_id:
                raise ValidationError(
                    "Hotel id is mandatory if ar is configured on hotel level"
                )
        debtor_aggregates = self.debtor_repo.search_debtors(debtor_search_query)
        total_debtors = (
            self.debtor_repo.count_debtors(debtor_search_query)
            if get_total_count
            else None
        )
        return debtor_aggregates, total_debtors

    @unit_of_work
    def create_new_debtor(self, debtor_data, user_data=None):
        RuleEngine.action_allowed(
            action="create_new_debtor",
            facts=CreateNewDebtorFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )

        hotel_id = debtor_data.get("hotel_id")
        if not hotel_level_accounts_receivable:
            hotel_id = None

        debtor_aggregate = DebtorFactory.create_new_debtor(
            debtor_code=debtor_data.get("debtor_code").strip(),
            debtor_name=debtor_data.get("debtor_name").strip(),
            hotel_id=hotel_id,
            debtor_type=debtor_data.get("debtor_type"),
            credit_limit=debtor_data.get("credit_limit"),
            credit_period=debtor_data.get("credit_period"),
            btc_enabled=debtor_data.get("btc_enabled"),
        )
        self.debtor_repo.save(debtor_aggregate)
        return debtor_aggregate

    def create_or_update_debtor_user_debtor_mapping_from_profile(
        self, debtor_dto, user_model
    ):
        debtor_model = (
            self.debtor_repo.query(DebtorModel)
            .filter(DebtorModel.debtor_code == debtor_dto.debtor_code)
            .first()
        )
        if debtor_model:
            debtor_model.debtor_name = debtor_dto.debtor_name
            debtor_model.credit_limit = debtor_dto.credit_limit
            debtor_model.credit_period = debtor_dto.credit_period
            debtor_model.btc_enabled = debtor_dto.btc_enabled
            debtor_model.profile_status = debtor_dto.profile_status
            debtor_model.pocs = debtor_dto.pocs
            debtor_model.registered_address = debtor_dto.registered_address
            self.debtor_repo.update_debtor_and_user_debtor_mapping(
                debtor_model, user_model
            )
        else:
            debtor_aggregate = DebtorFactory.create_new_debtor_from_debtor_dto(
                debtor_dto
            )
            logger.info(
                "Created/Updated new debtor with debtor_code: %s and debtor_name: %s",
                debtor_aggregate.debtor.debtor_code,
                debtor_aggregate.debtor.debtor_name,
            )
            self.debtor_repo.save_debtor_and_user_debtor_mapping(
                debtor_aggregate, user_model
            )
