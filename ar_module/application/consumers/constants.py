class InvoiceBillToType(object):
    COMPANY = "company"
    GUEST = "guest"


class InvoiceChargeType(object):
    CREDIT = "credit"


class BookKeepingType(object):
    CREDIT = "credit"
    DEBIT = "debit"
    PROVISIONAL_CREDIT = "provisional_credit"


class DebtorTypes(object):
    B2B = "b2b"
    B2C = "b2c"


class CompanyProfileEventType(object):
    PARENT_ENTITY_CREATED = "ParentEntityCreated"
    PARENT_ENTITY_UPDATED = "ParentEntityUpdated"
    SUB_ENTITY_CREATED = "SubEntityCreated"
    SUB_ENTITY_UPDATED = "SubEntityUpdated"


class POCsDesignation(object):
    SALES_POC = "Sales POC"
    BOOKING_POC = "Booking POC"
    FINANCE_POC = "Hotel Finance POC"
    FINANCE_ADMIN = "Finance POC"
