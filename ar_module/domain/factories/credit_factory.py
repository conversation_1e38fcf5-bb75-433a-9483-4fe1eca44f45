from treebo_commons.utils import dateutils

from ar_module.application.dtos.credit_dto import CreditDto
from ar_module.application.dtos.credit_note_dto import CreditNoteDto
from ar_module.common.id_generator_utils import random_id_generator
from ar_module.domain.aggregates.credit_aggregate import CreditAggregate
from ar_module.domain.constants import CreditType
from ar_module.domain.entities.credit import Credit


class CreditFactory:
    @staticmethod
    def create_new_credit(credit_dto: CreditDto):
        credit_id = random_id_generator(prefix="CDT")
        credit = Credit(
            credit_id=credit_id,
            debtor_id=credit_dto.debtor_id,
            credit_type=credit_dto.credit_type,
            date=dateutils.to_date(credit_dto.date),
            amount_in_base_currency=credit_dto.amount_in_base_currency,
            reference_number=credit_dto.reference_number,
            reference_id=credit_dto.reference_id,
            mode_of_credit=credit_dto.mode_of_credit,
            amount_in_credit_currency=credit_dto.amount_in_credit_currency,
            used_to_auto_settle_debit=credit_dto.used_to_auto_settle_debit,
            approval_document=credit_dto.approval_document,
            recorded_via=credit_dto.recorded_via,
            tenant_id=credit_dto.tenant_id,
        )
        credit_aggregate = CreditAggregate(credit=credit)
        return credit_aggregate

    @staticmethod
    def create_from_credit_note(credit_note_dto: CreditNoteDto):
        credit_id = random_id_generator(prefix="CDT")
        credit = Credit(
            credit_id=credit_id,
            debtor_id=credit_note_dto.debtor_id,
            credit_type=CreditType.CREDIT_NOTE,
            date=credit_note_dto.credit_note_date,
            amount_in_base_currency=credit_note_dto.credit_note_amount,
            reference_number=credit_note_dto.credit_note_number,
            reference_id=credit_note_dto.credit_note_id,
            mode_of_credit=None,
            amount_in_credit_currency=credit_note_dto.credit_note_amount,
            tenant_id=credit_note_dto.tenant_id,
        )
        credit_aggregate = CreditAggregate(credit=credit)
        return credit_aggregate
