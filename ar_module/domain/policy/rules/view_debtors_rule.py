from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.view_debtors_facts import ViewDebtorsFacts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class ViewDebtorsRule(BaseRule):
    def allow(self, facts: ViewDebtorsFacts, privileges=None):
        if PrivilegeCode.VIEW_DEBTORS not in privileges:
            raise PolicyAuthException(error=PolicyError.VIEW_DEBTORS_NOT_ALLOWED)
        return True
