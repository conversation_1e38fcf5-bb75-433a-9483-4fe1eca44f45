from marshmallow import ValidationError
from treebo_commons.money import Money

from ar_module.application.dtos.credit_dto import CreditReversalDto
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.domain.constants import AuditType, CreditStatus, CreditType, Modules
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.credit.credit_reversal_repository import (
    CreditReversalRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.settlement.settlement_repository import (
    SettlementRepository,
)
from object_registry import register_instance


@register_instance(
    dependencies=[
        CreditRepository,
        CreditReversalRepository,
        DebitRepository,
        SettlementRepository,
        AuditTrailService,
    ]
)
class CreditReversalService:
    def __init__(
        self,
        credit_repo: CreditRepository,
        credit_reversal_repo: CreditReversalRepository,
        debit_repo: DebitRepository,
        settlement_repo: SettlementRepository,
        audit_trail_service: AuditTrailService,
    ):
        self.credit_repo = credit_repo
        self.credit_reversal_repo = credit_reversal_repo
        self.debit_repo = debit_repo
        self.settlement_repo = settlement_repo
        self.audit_trail_service = audit_trail_service

    def validate_credit_reversals(self, credit_reversal_dto: CreditReversalDto):
        """
        Validates the credit reversals in the provided DTO.
        Ensures all credits belong to the same debtor, have the correct type and status,
        and are not tied to auto settlements or linked debits.
        """
        credit_ids = [crd.credit_id for crd in credit_reversal_dto.credit_reversals]

        credit_search_query = CreditSearchQuery(credit_ids=credit_ids)
        credit_details = self.credit_repo.load_credits(credit_search_query)

        debtor_ids = {cd.debtor_id for cd in credit_details}
        if len(debtor_ids) != 1:
            raise ValidationError("All credits must belong to the same debtor.")
        if credit_reversal_dto.debtor_id not in debtor_ids:
            raise ValidationError(
                "Credit reversal debtor ID does not match the credit's debtor ID."
            )

        total_base_currency_reversed = 0
        total_credit_currency_reversed = 0

        for credit_detail in credit_details:
            credit = credit_detail.credit
            if credit.recorded_via != Modules.AR_MODULE:
                raise ValidationError(
                    f"Credit ID {credit.credit_id} is not recorded in AR Module."
                )
            if credit.credit_type != CreditType.PAYMENT:
                raise ValidationError(
                    f"Credit ID {credit.credit_id} is not of type PAYMENT."
                )
            if credit.status != CreditStatus.CREATED:
                raise ValidationError(
                    f"Credit ID {credit.credit_id} is not in CREATED status."
                )
            if credit.used_to_auto_settle_debit:
                raise ValidationError(
                    f"Credit ID {credit.credit_id} is marked as used for auto settling debits."
                )
            if credit.linked_debit_id:
                raise ValidationError(
                    f"Credit ID {credit.credit_id} is linked to a debit."
                )

        credit_map = {cd.credit_id: cd.credit for cd in credit_details}

        for credit_reversal in credit_reversal_dto.credit_reversals:
            credit_id = credit_reversal.credit_id
            credit = credit_map.get(credit_id)

            if not credit:
                continue

            available_amount = credit.amount_in_base_currency - credit.refunded_amount
            if credit_reversal.amount_in_base_currency > available_amount:
                raise ValidationError(
                    "Credit reversal amount exceeds available credit amount."
                )

            total_base_currency_reversed += (
                credit_reversal.amount_in_base_currency.amount
            )
            total_credit_currency_reversed += (
                credit_reversal.amount_in_credit_currency.amount
            )

        if (
            credit_reversal_dto.amount_in_base_currency.amount
            != total_base_currency_reversed
        ):
            raise ValidationError("Amount mismatch of base currency")

        if (
            credit_reversal_dto.amount_in_credit_currency.amount
            != total_credit_currency_reversed
        ):
            raise ValidationError("Amount mismatch of credit currency")

        return True

    def reset_settlements_and_unpaid_amount(
        self, credit_reversals, debtor_aggregate, user_data
    ):
        """
        Resets settlements for the provided credit reversals and updates unpaid amounts.
        """
        credit_ids = [credit_reversal.credit_id for credit_reversal in credit_reversals]
        credit_aggregates = self.credit_repo.load_all_for_update(credit_ids)
        credit_aggregate_map = {
            credit_agg.credit.credit_id: credit_agg for credit_agg in credit_aggregates
        }

        all_related_settlements = self.settlement_repo.load_settlements_with_credit_ids(
            credit_ids
        )
        settlements_map = {}
        debit_ids = set()

        for settlement in all_related_settlements:
            settlements_map.setdefault(settlement.credit_id, []).append(settlement)
            debit_ids.add(settlement.debit_id)

        debit_aggregates = self.debit_repo.load_all_for_update(list(debit_ids))
        debit_aggregate_map = {debit.debit_id: debit for debit in debit_aggregates}

        for credit_reversal in credit_reversals:
            credit_id = credit_reversal.credit_id
            credit_aggregate = credit_aggregate_map.get(credit_id)

            if not credit_aggregate:
                continue

            related_settlements = settlements_map.get(credit_id, [])
            settlement_amount = (
                Money(
                    sum(rs.amount.amount for rs in related_settlements),
                    credit_aggregate.credit.amount_in_base_currency.currency,
                )
                if related_settlements
                else Money(0, credit_aggregate.credit.amount_in_base_currency.currency)
            )
            for settlement in related_settlements:
                debit_aggregate = debit_aggregate_map.get(settlement.debit_id)
                if debit_aggregate:
                    reversed_settlement = debit_aggregate.reverse_settlement_associated_with_given_payment(
                        credit_id
                    )

                    self.audit_trail_service.create_settlement_audit_trail(
                        debtor_aggregate=debtor_aggregate,
                        debit_aggregate=debit_aggregate,
                        settlement=reversed_settlement,
                        credit_aggregate=credit_aggregate,
                        hotel_id=debtor_aggregate.hotel_id,
                        audit_type=AuditType.SETTLEMENT_CANCELLED,
                        user_data=user_data,
                        is_settlement_cancelled_due_to_refund=True,
                    )

            self.debit_repo.update_all(debit_aggregates)
            refund_amount = credit_reversal.amount_in_base_currency
            credit_aggregate.update_refunded_amount(refund_amount)
            credit_aggregate.reverse_settlement_amount(
                settlement_amount - refund_amount
            )
        self.credit_repo.update_all(credit_aggregates)

    def fetch_and_populate_credit_transaction_details(self, credit_aggregates):
        credit_ids = [agg.credit_id for agg in credit_aggregates]
        all_reversals = self.credit_reversal_repo.get_credit_reversal_details(
            credit_ids
        )

        credit_reversals_map = {}
        for reversal in all_reversals:
            for key in (reversal.refund_credit_id, reversal.payment_credit_id):
                credit_reversals_map.setdefault(key, []).append(reversal)

        all_credit_ids = {r.payment_credit_id for r in all_reversals} | {
            r.refund_credit_id for r in all_reversals
        }
        credit_map = {
            credit.credit_id: credit
            for credit in self.credit_repo.load_all(list(all_credit_ids))
        }

        for credit_aggregate in credit_aggregates:
            credit = credit_aggregate.credit
            credit.credit_reversals = credit_reversals_map.get(credit.credit_id, [])

            for transaction in credit.credit_reversals:
                credit_id = (
                    transaction.refund_credit_id
                    if credit.credit_type == CreditType.PAYMENT
                    else transaction.payment_credit_id
                )
                credit_details = credit_map.get(credit_id)

                if credit_details:
                    credit_details = credit_details.credit
                    transaction.populate_payment_credit_info(
                        {
                            "mode_of_credit": credit_details.mode_of_credit,
                            "credit_reference_number": credit_details.reference_number,
                            "date": credit_details.date,
                            "amount_in_base_currency": credit_details.amount_in_base_currency,
                            "amount_in_credit_currency": credit_details.amount_in_credit_currency,
                            "unused_credit_amount": credit_details.unused_credit_amount,
                            "refunded_amount": credit_details.refunded_amount,
                        }
                    )
        return credit_aggregates

    def update_unused_and_refunded_amount(self, credit_aggregate):
        if not credit_aggregate.credit.credit_reversals:
            raise ValueError(
                "No credit reversals found for the given credit aggregate."
            )
        payment_credit_aggregates = set()
        for credit_reversal in credit_aggregate.credit.credit_reversals:
            payment_credit_aggregate = self.credit_repo.load(
                credit_reversal.payment_credit_id
            )
            payment_credit_aggregate.reverse_settlement_amount(
                credit_reversal.amount_in_base_currency
            )
            payment_credit_aggregate.update_refunded_amount(
                -credit_reversal.amount_in_base_currency
            )
            payment_credit_aggregates.add(payment_credit_aggregate)

        return payment_credit_aggregates
