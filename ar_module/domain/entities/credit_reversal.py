from ths_common.utils import dateutils
from treebo_commons.money import Money

from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.constants import CreditStatus
from ar_module.domain.entities.base_entity import EntityChangeTracker


class CreditReversal(EntityChangeTracker, TenantMixin):
    def __init__(
        self,
        payment_credit_id,
        refund_credit_id,
        amount_in_base_currency: Money,
        amount_in_credit_currency: Money,
        status=CreditStatus.CREATED,
        id=None,
        remarks=None,
        cancellation_date=None,
        created_at=None,
        modified_at=None,
        deleted=False,
        dirty=True,
        new=True,
        # Additional fields for payment_credit details
        credit_payment_mode=None,
        credit_reference_number=None,
        credit_date=None,
        credit_amount_in_base_currency=None,
        credit_amount_in_credit_currency=None,
        credit_unused_amount=None,
        credit_refunded_amount=None,
    ):
        super().__init__(dirty=dirty, new=new)
        self.id = id
        self.payment_credit_id = payment_credit_id
        self.refund_credit_id = refund_credit_id
        self.amount_in_base_currency = amount_in_base_currency
        self.amount_in_credit_currency = amount_in_credit_currency
        self.remarks = remarks
        self.cancellation_date = cancellation_date
        self.created_at = created_at
        self.modified_at = modified_at
        self.deleted = deleted
        self.status = status

        # Fields for payment_credit details
        self.credit_payment_mode = credit_payment_mode
        self.credit_date = credit_date
        self.credit_reference_number = credit_reference_number
        self.credit_amount_in_base_currency = credit_amount_in_base_currency
        self.credit_amount_in_credit_currency = credit_amount_in_credit_currency
        self.credit_unused_amount = credit_unused_amount
        self.credit_refunded_amount = credit_refunded_amount

    def cancel_reversal(self):
        if self.status == CreditStatus.CANCELLED:
            return
        self.status = CreditStatus.CANCELLED
        self.cancellation_date = dateutils.current_date()
        self.mark_dirty()

    def populate_payment_credit_info(self, payment_credit_details):
        """
        Adds payment credit details to the credit reversal entity.
        """
        self.credit_payment_mode = payment_credit_details.get("mode_of_credit")
        self.credit_date = payment_credit_details.get("date")
        self.credit_reference_number = payment_credit_details.get(
            "credit_reference_number"
        )
        self.credit_amount_in_base_currency = payment_credit_details.get(
            "amount_in_base_currency"
        )
        self.credit_amount_in_credit_currency = payment_credit_details.get(
            "amount_in_credit_currency"
        )
        self.credit_unused_amount = payment_credit_details.get("unused_credit_amount")
        self.credit_refunded_amount = payment_credit_details.get("refunded_amount")
