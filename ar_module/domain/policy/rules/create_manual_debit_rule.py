from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_manual_debit_facts import (
    CreateManualDebitFacts,
)
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CreateManualDebitRule(BaseRule):
    def allow(self, facts: CreateManualDebitFacts, privileges=None):
        if PrivilegeCode.CREATE_MANUAL_DEBIT not in privileges:
            raise PolicyAuthException(error=PolicyError.CREATE_MANUAL_DEBIT_NOT_ALLOWED)
        return True
