from treebo_commons.utils import dateutils


class ERPCreditDetailsDTO:
    def __init__(self, erp_credit_details):
        self.credit_date = dateutils.date_to_ymd_str(erp_credit_details.credit_date)
        self.amount = erp_credit_details.amount_in_base_currency
        self.mode_of_credit = erp_credit_details.mode_of_credit
        self.reference_number = erp_credit_details.reference_number
        self.debtor_code = erp_credit_details.debtor_code
        self.debtor_type = (
            erp_credit_details.debtor_type if erp_credit_details.debtor_type else "b2b"
        )
