from ar_module.integration_tests.utilities import excel_utils
from ar_module.integration_tests.utilities.common_utils import (
    return_date,
    sanitize_blank,
)


class CreateDebitBuilder:
    def __init__(self, sheet_name, test_case_id, debtor_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = DebitData(test_data, debtor_id, test_case_id).__dict__


class DebitData:
    def __init__(self, test_data, debtor_id, test_case_id):
        if test_case_id != "CreateDebit_08":
            self.debit_amount = DebitAmount(test_data).__dict__
        self.debit_date = (
            str(return_date(int(test_data["debit_date"])))
            if test_data["debit_date"]
            else None
        )
        if test_case_id == "CreateDebit_07":
            self.debtor_id = None
        elif test_case_id == "CreateDebit_17":
            self.debtor_id = "invalid_debator_id"
        else:
            self.debtor_id = debtor_id
        self.reference_number = sanitize_blank(test_data["reference_number"])


class DebitAmount:
    def __init__(self, test_data):
        self.posttax_amount = (
            sanitize_blank(test_data["posttax_amount"])
            if test_data["posttax_amount"]
            else None
        )
        self.pretax_amount = (
            sanitize_blank(test_data["pretax_amount"])
            if test_data["pretax_amount"]
            else None
        )
        self.tax_amount = (
            sanitize_blank(test_data["tax_amount"]) if test_data["tax_amount"] else None
        )
