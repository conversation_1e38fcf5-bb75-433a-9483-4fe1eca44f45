import enum


class ErrorMessage(enum.Enum):
    CreateDebtor_03 = ["[Debtor Code] -> Field may not be null."]
    CreateDebtor_04 = ["[Debtor Name] -> Field may not be null."]
    CreateDebtor_12 = ["[Debtor Type] -> Not a valid choice."]
    CreateDebit_02 = [""]
    CreateDebit_03 = ["[Debit Date] -> Field may not be null."]
    CreateDebit_05 = [
        "Debit date should be less than or equal to current business date"
    ]
    CreateDebit_06 = ["[Reference Number] -> Field may not be null."]
    CreateDebit_07 = ["[Debtor Id] -> Field may not be null."]
    CreateDebit_08 = [
        "[Tax Amount] -> Missing data for required field.",
        "[Pretax Amount] -> Missing data for required field.",
        "[Posttax Amount] -> Missing data for required field.",
    ]
    CreateDebit_09 = ["[Posttax Amount] -> Field may not be null."]
    CreateDebit_10 = ["[Pretax Amount] -> Field may not be null."]
    CreateDebit_11 = ["[Tax Amount] -> Field may not be null."]
    CreateDebit_12 = ["[Posttax Amount] -> Field value should be greater than 0"]
    CreateDebit_13 = ["[Pretax Amount] -> Field value should be greater than 0"]
    CreateDebit_14 = ["[Tax Amount] -> Field value should be greater than 0"]
    CreateDebit_15 = [
        "[Pretax Amount] -> Pretax amount cannot be greater than posttax amount"
    ]
    CreateDebit_17 = [
        "Don't have access to debtor invalid_debator_id",
        "You are not authorized to create manual debits",
    ]
    CreateDebit_20 = ["Comparing different currencies"]
    CreateCredit_02 = ["[Amount In Base Currency] -> Field may not be null."]
    CreateCredit_03 = ["[Amount In Credit Currency] -> Field may not be null."]
    CreateCredit_04 = [
        "[Amount In Base Currency] -> Field may not be null.",
        "[Amount In Credit Currency] -> Field may not be null.",
    ]
    CreateCredit_05 = ["[Date] -> Field may not be null."]
    CreateCredit_06 = ["[Debtor Id] -> Field may not be null."]
    CreateCredit_07 = ["Aggregate: DebtorModel with id: IncorrectDebtorId missing."]
    CreateCredit_10 = ["[Credit Type] -> Not a valid choice."]
    CreateCredit_11 = ["[Credit Type] -> Not a valid choice."]
    CreateCredit_12 = ["[Amount] -> Field may not be null."]
    CreateCredit_17 = ["Settlement amount is greater than remaining unsettled amount"]
    CreateCredit_19 = [
        "[Amount In Base Currency] -> Field value should be greater than 0"
    ]
    CreateCredit_20 = [
        "[Amount In Credit Currency] -> Field value should be greater than 0"
    ]
    CreateCredit_21 = [
        "[Mode Of Credit] -> Field may not be null.",
        "[ Schema] -> Invalid Payment Mode",
    ]
    CreateCredit_22 = ["[Amount] -> Field value should be greater than 0"]
    CreateCredit_25 = ["Credit date of future is not allowed"]
    CreateCredit_37 = ["You are not authorized to create credit"]
    CreateCredit_41 = ["Payment mode write_off not supported for user super-admin"]
    CreateCredit_46 = ["Payment mode razorpay_api can't be used for debtor txdp737f"]
    CreateSettlement_07 = [
        "Settlement amount is greater than remaining unsettled amount"
    ]
    CreateSettlement_08 = ["[Amount] -> Field may not be null."]
    CreateSettlement_09 = ["[Amount] -> Field value should be greater than 0"]
    CreateSettlement_10 = [
        "[Tds Settlement Amount] -> Field value should be greater than 0"
    ]
    CreateSettlement_11 = ["You are not authorized to create/update settlements"]
    CreateSettlement_12 = ["Payment mode write_off not supported for user super-admin"]
    EditSettlement_07 = ["[Amount] -> Field value should be greater than 0"]
    EditSettlement_08 = ["Settlement amount is greater than remaining unsettled amount"]
    EditSettlement_09 = ["You are not authorized to create/update settlements"]
    EditSettlement_10 = ["Payment mode write_off not supported for user super-admin"]
    CancelCredit_07 = [
        "[Cancellation Reason] ->  is not a valid value. Must be one of: ['wrong_receipt_recorded_rectification', 'moving_to_another_debtor']"
    ]
    CancelCredit_08 = [
        "[Cancellation Reason] -> not_valid_reason is not a valid value. Must be one of: ['wrong_receipt_recorded_rectification', 'moving_to_another_debtor']"
    ]
    CancelCredit_09 = ["Credit is already cancelled"]
    CancelCredit_10 = ["You are not authorized to cancel credit"]
    CancelCredit_11 = ["Invalid Credit_id"]
    CancelCredit_12 = ["Payment mode write_off not supported for user super-admin"]
    CancelCredit_14 = [
        "Debit Associated with this credit has settlement in corresponding debtor ledger"
    ]
    GetDebtor_05 = ["You are not authorized to view debtors"]
    PreviewDebtorSummary_06 = ["You are not authorized to view debtor summary"]
    PreviewDebtorSummary_07 = [
        "Not authorised to view given debtor",
        "You are not authorized to view debtor summary",
    ]
