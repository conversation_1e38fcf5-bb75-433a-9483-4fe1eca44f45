from treebo_commons.money import Money

from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.constants import Modules, SettlementStatus
from ar_module.domain.entities.base_entity import EntityChangeTracker
from ar_module.domain.value_objects.amount import Amount
from ar_module.domain.value_objects.invoice_meta import InvoiceMeta
from ar_module.exceptions import InvalidSettlementException


class Debit(EntityChangeTracker, TenantMixin):
    def __init__(
        self,
        debit_id,
        debtor_id,
        debit_date,
        debit_amount: Amount,
        reference_number,
        debit_type,
        created_at=None,
        due_date=None,
        settlement_status=None,
        reference_id=None,
        unsettled_amount=None,
        debit_template_url=None,
        booking_reference_number=None,
        hotel_id=None,
        bill_to_debtor_code=None,
        dirty=True,
        new=True,
        remarks=None,
        auto_settled_via_credit=False,
        recorded_via=Modules.AR_MODULE,
        invoice_meta: InvoiceMeta = None,
        tenant_id=None,
    ):
        super().__init__(dirty=dirty, new=new)
        TenantMixin.__init__(self, tenant_id)

        self.debit_id = debit_id
        self.debtor_id = debtor_id
        self.debit_date = debit_date
        self.debit_amount = debit_amount
        self.reference_number = reference_number
        self.debit_type = debit_type
        self.reference_id = reference_id
        self.settlement_status = settlement_status or SettlementStatus.UNSETTLED
        self.created_at = created_at
        self.due_date = due_date
        self.remarks = remarks
        self.auto_settled_via_credit = auto_settled_via_credit
        self.debit_template_url = debit_template_url
        self.booking_reference_number = booking_reference_number
        self.hotel_id = hotel_id
        self.bill_to_debtor_code = bill_to_debtor_code
        self.recorded_via = recorded_via
        self.invoice_meta = invoice_meta
        if unsettled_amount is not None:
            self.unsettled_amount = unsettled_amount
        else:
            self.unsettled_amount = debit_amount.posttax_amount

    def knock_off_settlement_amount(self, amount: Money):
        if amount > self.unsettled_amount:
            raise InvalidSettlementException(
                message="Settlement amount is greater than remaining unsettled amount"
            )
        self.unsettled_amount -= amount
        self.mark_dirty()

    def reverse_settlement_amount(self, amount: Money):
        self.unsettled_amount += amount
        self.mark_dirty()

    def can_settle_amount(self, amount: Money):
        return amount < self.unsettled_amount
