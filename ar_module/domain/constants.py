import re

from ths_common.constants.billing_constants import PaymentTypes

regex = r"^[A-Z][A-Z_]*[A-Z]$"


class CreditType(object):
    PAYMENT = "payment"
    CREDIT_NOTE = "credit_note"
    TDS = "tds"
    CREDIT_REVERSAL = "credit_reversal"

    @staticmethod
    def all():
        return [kv[1] for kv in CreditType.__dict__.items() if re.match(regex, kv[0])]


class SettlementStatus(object):
    SETTLED = "settled"
    CANCELLED = "cancelled"
    PARTIALLY_SETTLED = "partially_settled"
    PROVISIONALLY_SETTLED = "provisionally_settled"
    UNSETTLED = "unsettled"

    @staticmethod
    def all():
        return [
            kv[1] for kv in SettlementStatus.__dict__.items() if re.match(regex, kv[0])
        ]


class InvoiceStatusToConsume(object):
    GENERATED = "generated"
    LOCKED = "locked"
    CANCELLED = "cancelled"


class ModeOfCredit(object):
    OTHERS = "others"


class PrivilegeCode:
    VIEW_CREDIT = "VIEW_CREDIT"
    VIEW_DEBTORS = "VIEW_DEBTORS"
    CREATE_NEW_DEBTOR = "CREATE_NEW_DEBTOR"
    CREATE_MANUAL_DEBIT = "CREATE_MANUAL_DEBIT"
    CREATE_CREDIT = "CREATE_CREDIT"
    CREATE_SETTLEMENTS = "CREATE_SETTLEMENTS"
    CANCEL_CREDIT = "CANCEL_CREDIT"
    VIEW_DEBTOR_SUMMARY = "VIEW_DEBTOR_SUMMARY"
    CREATE_CREDIT_REVERSAL = "CREATE_CREDIT_REVERSAL"
    CANCEL_CREDIT_REVERSAL = "CANCEL_CREDIT_REVERSAL"


class UserType(object):
    FINANCE_POC = "finance-poc"
    BACKEND_SYSTEM = "backend-system"


class CreditStatus(object):
    CREATED = "created"
    CANCELLED = "cancelled"


class SortOrders(object):
    ASC = "asc"
    DESC = "desc"

    @classmethod
    def all(cls):
        return [cls.ASC, cls.DESC]


class AuditType(object):
    CREDIT_CREATED = "credit_created"
    CREDIT_CANCELLED = "credit_cancelled"
    DEBIT_CREATED = "debit_created"
    DEBIT_AUTO_SETTLED = "debit_auto_settled"
    SETTLEMENT_CREATED = "settlement_created"
    SETTLEMENT_CANCELLED = "settlement_cancelled"
    DEBTOR_COMMUNICATION = "debtor_communication"
    CREDIT_REVERSAL_CREATED = "credit_reversal_created"
    CREDIT_REVERSAL_CANCELLED = "credit_reversal_cancelled"
    CREDIT_REVERSAL_MAPPED = "credit_reversal_mapped"
    CREDIT_REVERSAL_UNMAPPED = "credit_reversal_unmapped"

    @staticmethod
    def all():
        return [kv[1] for kv in AuditType.__dict__.items() if re.match(regex, kv[0])]


class ARPaymentMode(object):
    BANK_TRANSFER_TO_TREEBO = "bank_transfer_to_treebo"
    RAZOR_PAY = "razorpay_api"
    TDS_RECONCILED = "tds_reconciled"
    AUTO_TRANSFER = "auto_transfer"
    WRITE_OFF = "write_off"
    TDS_RECEIVABLE_24_25 = "tds_receivable_fy_24-25"

    @staticmethod
    def all():
        return [
            kv[1] for kv in ARPaymentMode.__dict__.items() if re.match(regex, kv[0])
        ]


class SettlementUploaderAction(object):
    MAPPING = "mapping"
    UNMAPPING = "unmapping"

    @staticmethod
    def all():
        return [
            kv[1]
            for kv in SettlementUploaderAction.__dict__.items()
            if re.match(regex, kv[0])
        ]


class DebitType(object):
    PAYMENT = "payment"
    CREDIT_INVOICE = "credit_invoice"
    SPOT_CREDIT_INVOICE = "spot_credit_invoice"

    @staticmethod
    def all():
        return [kv[1] for kv in DebitType.__dict__.items() if re.match(regex, kv[0])]


class SettlementFrequency(object):
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"

    @staticmethod
    def all():
        return [
            kv[1]
            for kv in SettlementFrequency.__dict__.items()
            if re.match(regex, kv[0])
        ]


class ARModuleConfigs:
    CHAIN_BASE_CURRENCY = "ar_module.chain_base_currency"
    HOTEL_LEVEL_ACCOUNTS_RECEIVABLE = "ar_module.hotel_level_accounts_receivable"
    ACCEPTED_PAYMENT_METHODS = "ar_module.accepted_payment_methods"
    ACCEPTED_REFUND_METHODS = "ar_module.accepted_refund_methods"
    CREDIT_SETTINGS = "ar_module.credit_settings"
    AUTO_SETTLED_FEATURED_ENABLED = "ar_module.auto_settle_feature_enabled"
    SUB_ENTITY_LEVEL_DEBIT_CREATION = "ar_module.sub_entity_level_debtor_creation"
    PARENT_ENTITY_LEVEL_DEBIT_CREATION = "ar_module.parent_entity_level_debtor_creation"
    DEBTOR_CONFIG = "ar_module.debtor_config"
    CASHIERING_ENABLED = "cashiering_enabled"
    PAYMENT_DATA_PUSH_CONFIG = "nav_payment_data_push_config"
    PAYMENT_CANCELLATION_ENUM = "ar_payment_cancellation_reasons"


class Modules:
    AR_MODULE = "ar_module"
    CRS_MODULE = "crs_module"


class CreditCancellationReason(object):
    MOVE_ANOTHER_DEBTOR = "moving_to_another_debtor"
    RECTIFICATION = "wrong_receipt_recorded_rectification"


DEFAULT_CREDIT_SETTINGS = {"credit_period": 7, "credit_limit": 1000000}


class CommissionTypes(object):
    OTA_COMMISSION = "ota_commission"
    COMMISSION_TAX = "commission_tax"
    COMMISSION_TDS = "commission_tds"
    COMMISSION_TCS = "commission_tcs"
    RCM_COMMISSION = "rcm_commission"


class PGPaymentTypes(object):
    PG_CHARGE = "pg_charge"
    PG_TAX = "pg_tax"


CREDIT_PAYMENT_TYPES = [
    PaymentTypes.REFUND.value,
    CommissionTypes.COMMISSION_TAX,
    CommissionTypes.COMMISSION_TCS,
    CommissionTypes.COMMISSION_TDS,
    CommissionTypes.RCM_COMMISSION,
    CommissionTypes.OTA_COMMISSION,
    PGPaymentTypes.PG_TAX,
    PGPaymentTypes.PG_CHARGE,
]
