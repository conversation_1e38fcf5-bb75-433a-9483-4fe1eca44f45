import logging

from ar_module.domain.factories.user_factory import UserFactory
from ar_module.infrastructure.database.models.user_model import UserModel
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[UserRepository])
class UserService(object):
    def __init__(self, user_repo: UserRepository):
        self.user_repo = user_repo

    def create_or_update_user(self, poc_user):
        user_auth_id = poc_user.auth_id
        user_model = self.user_repo.search_user_by_id(
            user_id=user_auth_id, return_user_model=True
        )
        if user_model:
            user_model.phone_number = poc_user.phone_number
            user_model.first_name = poc_user.first_name
            user_model.last_name = poc_user.last_name
            self.user_repo.update_user_model(user_model)
        else:
            user_aggregate = UserFactory.create_new_user(
                user_id=poc_user.auth_id,
                email=poc_user.email,
                phone_number=poc_user.phone_number,
                first_name=poc_user.first_name,
                last_name=poc_user.last_name,
            )
            user_model = self.user_repo.save(user_aggregate)
            logger.info(f"Created new user with user_id: {poc_user.auth_id}")
        return user_model
