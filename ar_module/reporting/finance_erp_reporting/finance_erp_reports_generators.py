from treebo_commons.utils import dateutils

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.domain.constants import ARModuleConfigs
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.reporting.finance_erp_reporting.finance_report_aggrgators import (
    PaymentReportAggregate,
)
from object_registry import locate_instance


class PaymentDataReportGenerator(object):
    def __init__(self, date=None, reference_numbers=None):
        self.date = date
        self.reference_numbers = reference_numbers
        self.credit_repository = locate_instance(CreditRepository)
        self.debtor_repository = locate_instance(DebtorRepository)
        self.tenant_settings = locate_instance(TenantSettings)

    def generate(self):
        payment_report_aggregates = []
        payment_push_config = self.tenant_settings.get_setting_value(
            ARModuleConfigs.PAYMENT_DATA_PUSH_CONFIG
        )
        if self.reference_numbers:
            credits_data = (
                self.credit_repository.load_credits_for_given_reference_numbers(
                    self.reference_numbers
                )
            )
        else:
            credits_and_reversals_added = (
                self.credit_repository.load_credits_and_reversal_added_on_given_date(
                    self.date
                )
            )
            credits_and_reversals_cancelled = self.credit_repository.load_credits_and_reversal_cancelled_on_given_date(
                self.date
            )
            credits_and_reversals_cancelled = (
                self._filter_out_credits_cancelled_on_same_day_of_creation(
                    credits_and_reversals_cancelled
                )
            )
            credits_data = credits_and_reversals_added + credits_and_reversals_cancelled

        associated_debtors_data = self._fetch_debtors_for_credits(credits_data)
        debtor_id_to_debtor_mapping = {
            debtor.debtor_id: debtor for debtor in associated_debtors_data
        }
        for credit_data in credits_data:
            payment_report_aggregates.append(
                PaymentReportAggregate(
                    payment_push_config, credit_data, debtor_id_to_debtor_mapping
                )
            )
        return payment_report_aggregates

    @staticmethod
    def _filter_out_credits_cancelled_on_same_day_of_creation(cancelled_credits):
        filtered_credits = []
        for credit in cancelled_credits:
            if dateutils.to_date(credit.created_at) != credit.cancellation_date:
                filtered_credits.append(credit)
        return filtered_credits

    def _fetch_debtors_for_credits(self, credit_data):
        debtor_ids = [credit.debtor_id for credit in credit_data]
        return self.debtor_repository.load_all(debtor_ids=debtor_ids)
