from marshmallow import Schema, fields
from marshmallow.validate import OneOf
from ths_common.constants.billing_constants import PaymentModes

from ar_module.domain.constants import ARPaymentMode


class BulkFileUploadRequestSchema(Schema):
    file_urls = fields.List(fields.URL(), required=True)


class BulkIngestFinancialDataRequestSchema(Schema):
    posted_date = fields.Date()
    payment_amount = fields.Decimal(required=True)
    payment_reference_number = fields.String(required=True)
    reference_id = fields.String(required=True)
    ta_ref_id = fields.String(allow_none=True)
    booking_reference_number = fields.String()
    payment_type = fields.String()
    payment_mode = fields.String(
        validate=[
            OneOf(
                [
                    PaymentModes.RAZORPAY_API,
                    PaymentModes.AIR_PAY,
                    ARPaymentMode.TDS_RECEIVABLE_24_25,
                    PaymentModes.PAID_AT_OTA,
                ]
            )
        ],
        required=True,
    )

    # below fields are not used for now
    hotel_code = fields.String()
    payment_date = fields.Date()
