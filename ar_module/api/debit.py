from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas import SettlementResponseSchema
from ar_module.api.schemas.request.debit import DebitRequestSchema, DebitSearchSchema
from ar_module.api.schemas.response.debit import DebitResponseSchema, DebitSchema
from ar_module.application.services.debit_service import DebitService
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/debits", methods=["POST"])
@schema_wrapper_parser(DebitRequestSchema)
@inject(debit_service=DebitService)
def create_new_debit(debit_service, parsed_request):
    """
    Create new Debit (Invoice) entry.
    ---
    operationId: create_debit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates new Debit (Invoice) entry
        tags:
            - Debit
        parameters:
            - in: body
              name: body
              description: The Debit which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/DebitRequestSchema"
        responses:
            200:
                description: Newly created Debit
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                debit:
                                    $ref: "#/definitions/DebitResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    debit_aggregate = debit_service.create_new_debit(
        parsed_request, user_data=user_data
    )
    debit_response_schema = DebitSchema()
    response = debit_response_schema.dump(debit_aggregate.debit)
    return ApiResponse.build(data=dict(debit=response.data), status_code=200)


@ar_bp.route("/debits/<string:debit_id>/settlements", methods=["GET"])
@inject(debit_service=DebitService)
def get_settlements(debit_service, debit_id):
    """
    Returns all the settlements for given debit id.
    ---
    operationId: get_settlements
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: debit_id
              in: path
              required: true
        description: Fetch all settlements for a given debit
        tags:
            - Settlement
        responses:
            200:
                description: List of Settlements for a debit id
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                settlements:
                                    type: array
                                    items:
                                        $ref: "#/definitions/SettlementResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    settlement_aggregates = debit_service.get_settlements(debit_id, user_data=user_data)
    settlement_response_schema = SettlementResponseSchema(many=True)
    response = settlement_response_schema.dump(settlement_aggregates)
    return ApiResponse.build(data=dict(settlements=response.data), status_code=200)


@ar_bp.route("/debits", methods=["GET"])
@schema_wrapper_parser(DebitSearchSchema, param_type=RequestTypes.ARGS)
@inject(debit_service=DebitService)
def get_debits(debit_service, parsed_request):
    """
    Get all debits for the given debtor, and with applied filter criteria.
    ---
    operationId: get_debtor_debits
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema: DebitSearchSchema
        description: Fetch debits for given debtor
        tags:
            - Debit
        responses:
            200:
                description: List of Debits for the given filter criteria
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                debits:
                                    type: array
                                    items:
                                        $ref: "#/definitions/DebitResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    get_total_count = (
        True
        if parsed_request.limit is not None and parsed_request.offset is not None
        else False
    )
    debit_aggregates, total_debits = debit_service.search_debits(
        parsed_request, get_total_count=get_total_count, user_data=user_data
    )

    debit_response_schema = DebitResponseSchema()
    response = debit_response_schema.dump(
        dict(
            debits=[aggregate.debit for aggregate in debit_aggregates],
            limit=parsed_request.limit,
            offset=parsed_request.offset,
            total=total_debits,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)
