import json
import logging

from ths_common.utils.collectionutils import chunks

from ar_module.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from ar_module.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from ar_module.reporting.finance_erp_reporting.serializers.finance_erp_reporting import (
    PaymentsPushSchema,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class FinanceERPServiceClient(BaseExternalClient):
    def __init__(self):
        super().__init__(timeout=4000)

    page_map = {
        "json_payment_gateway": dict(
            type=BaseExternalClient.CallTypes.POST,
            url_regex="/erp/api/v1/payment-data/ingest-async",
        ),
    }

    def get_domain(self):
        return ServiceRegistryClient.get_finance_portal_service_url()

    def push_payments_data(self, payment_aggregates):
        json_key = "json_payment_gateway"
        data_to_push = PaymentsPushSchema().dump(payment_aggregates, many=True).data
        self.push_to_finance_portal(data_to_push, json_key=json_key)

    def push_to_finance_portal(self, data_to_push, json_key):
        if not data_to_push:
            return True

        for chunked_data_index, chunked_data in enumerate(chunks(data_to_push, 1000)):
            logger.info(
                "Data to be sent for %s to Finance portal: %s",
                json_key,
                json.dumps(chunked_data),
            )
            response = self.make_call(json_key, chunked_data)
            logger.info(
                "Finance portal response for %s with data %s",
                json_key,
                response.json_response,
            )
            if not response.is_success():
                raise Exception(
                    "{0} Push Error. Status Code: {1}, Errors: {2}".format(
                        json_key, response.response_code, response.json_response
                    )
                )
