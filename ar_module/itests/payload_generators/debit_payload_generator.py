def create_new_debit_payload(
    debit_date,
    posttax_amount="INR 1000",
    pretax_amount="INR 900",
    tax_amount="INR 100",
):
    payload = {
        "debit_amount": {
            "posttax_amount": posttax_amount,
            "pretax_amount": pretax_amount,
            "tax_amount": tax_amount,
        },
        "debit_date": str(debit_date),
        "debtor_id": "string",
        "reference_number": "testP010321234",
    }
    return payload
