from sqlalchemy import <PERSON><PERSON><PERSON>, NUMERIC, <PERSON><PERSON>an, Column, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)


class DebtorModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "debtor"

    debtor_id = Column(String, primary_key=True)
    debtor_code = Column(String)
    debtor_name = Column(String)
    user_profile_id = Column(String)
    debtor_type = Column(String)
    credit_period = Column(NUMERIC)
    credit_limit = Column(NUMERIC)
    btc_enabled = Column(Boolean)
    hotel_id = Column(String)
    profile_status = Column(String)
    pocs = Column(JSON)
    registered_address = Column(JSON)
