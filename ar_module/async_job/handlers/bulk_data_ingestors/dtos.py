class FileUploadDto(object):
    def __init__(self, file_url, ack_email, user_data):
        self.file_url = file_url
        self.ack_email = ack_email
        self.user_data = user_data

    def serialize(self):
        user_data = dict()
        if self.user_data:
            user_data = dict(
                user_type=self.user_data.user_type,
                user=self.user_data.user,
                hotel_id=self.user_data.hotel_id,
                user_auth_id=self.user_data.user_auth_id,
            )
        return dict(
            file_url=self.file_url, ack_email=self.ack_email, user_data=user_data
        )
