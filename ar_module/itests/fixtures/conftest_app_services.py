import pytest

from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.credit_service import CreditService
from ar_module.application.services.debit_service import DebitService
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.settlement_service import SettlementService
from ar_module.application.services.user_service import UserService
from object_registry import locate_instance


@pytest.fixture(scope="session")
def account_receivable_application_service():
    return locate_instance(AccountReceivableApplicationService)


@pytest.fixture(scope="session")
def audit_trail_service():
    return locate_instance(AuditTrailService)


@pytest.fixture(scope="session")
def credit_service():
    return locate_instance(CreditService)


@pytest.fixture(scope="session")
def debit_service():
    return locate_instance(DebitService)


@pytest.fixture(scope="session")
def debtor_service():
    return locate_instance(DebtorService)


@pytest.fixture(scope="session")
def settlement_service():
    return locate_instance(SettlementService)


@pytest.fixture(scope="session")
def user_service():
    return locate_instance(UserService)
