import pytest

from ar_module.integration_tests.config.common_config import (
    AR_MANAGER,
    CANCELLED_CREDIT,
    CREATE_CREDIT,
    CREATE_CREDIT_WITH_MULTIPLE_DEBTORS,
    CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
    CREATE_CREDIT_WITH_SETTLEMENT,
    CREATE_WRITE_OFF_CREDIT,
    ERROR_CODES,
    FDM,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.resources.db_queries import GET_LINKED_DEBIT_ID
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.credit.validations.validation_cancel_credit import (
    ValidateCancelCredit,
)
from ar_module.integration_tests.utilities.common_utils import query_execute


class TestCancelCredit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message",
        [
            (
                "CancelCredit_01",
                CREATE_CREDIT,
                "Cancel a credit with wrong receipt recorded rectification as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_02",
                CREATE_CREDIT,
                "Cancel a credit with moving to another debtor as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_03",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Cancel a credit with settlement with wrong receipt recorded rectification as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_04",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Cancel a credit with settlement with moving to another debtor as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_05",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Cancel a credit having multiple settlements with wrong receipt recorded rectification as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_06",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Cancel a credit having multiple settlements with moving to another debtor as a cancellation reason",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_07",
                CREATE_CREDIT,
                "Cancel a credit with no cancellation reason",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_08",
                CREATE_CREDIT,
                "Cancel a credit with invalid cancellation reason",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_09",
                CANCELLED_CREDIT,
                "Cancel a already cancelled credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_10",
                CREATE_CREDIT,
                "Cancel a credit as an FDM",
                403,
                FDM,
                None,
            ),
            (
                "CancelCredit_11",
                CREATE_CREDIT,
                "Cancel a credit with invalid credit_id",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_12",
                CREATE_WRITE_OFF_CREDIT,
                "Cancel a write off credit as super-admin who is not allowed to cancel this credit",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "CancelCredit_13",
                CREATE_WRITE_OFF_CREDIT,
                "Cancel a write off credit as ar-manager who is allowed to cancel this credit",
                200,
                AR_MANAGER,
                None,
            ),
            (
                "CancelCredit_14",
                CREATE_CREDIT_WITH_MULTIPLE_DEBTORS,
                "Cancel a credit which creates a debit in tds debtors and then that debit is mapped with a payment",
                400,
                SUPER_ADMIN,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_cancel_credit(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[0]
        self.debit_request.debit_id.clear()
        self.debtor_request.debtor_ids.clear()
        self.credit_request.credit_ids.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if test_case_id == "CancelCredit_14":
            credit_action = [
                {
                    "id": "CreateCredit_45",
                    "type": "create_credit",
                    "debtor_id": f"{self.debtor_request.debtor_ids[0]}",
                }
            ]
            self.common_request_caller(client_, credit_action)
            linked_debit_id = query_execute(GET_LINKED_DEBIT_ID).fetchall()
            settlement_action = [
                {
                    "id": "CreateSettlement_01",
                    "type": "create_settlement",
                    "debit_id": f"{linked_debit_id[0][0]}",
                    "credit_id": f"{self.credit_request.credit_ids[1]}",
                }
            ]
            self.common_request_caller(client_, settlement_action)

        credit_id = (
            "123abc"
            if test_case_id == "CancelCredit_11"
            else self.credit_request.credit_ids[0]
        )

        response = self.credit_request.cancel_credit(
            client_,
            credit_id,
            status_code,
            hotel_id,
            user_type,
            self.credit_request.pay_mode,
            test_case_id,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                test_case_id,
                self.credit_request,
                self.audit_trail_request,
                self.debtor_request.debtor_id,
                hotel_id,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_, test_case_id, credit_request, audit_trail_request, debtor_id, hotel_id
    ):
        validation = ValidateCancelCredit(
            client_,
            test_case_id,
            credit_request,
            audit_trail_request,
            debtor_id,
            hotel_id,
        )
        validation.validate_response()
        validation.validate_audit_trail()
