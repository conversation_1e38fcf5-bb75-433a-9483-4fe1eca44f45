from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField


class SettlementResponseSchema(Schema):
    debit_id = fields.String(required=True)
    settlement_id = fields.Integer(required=True)
    settled_via = fields.String()
    payment_reference_number = fields.String()
    invoice_reference_number = fields.String()
    amount = MoneyField(required=True)
    credit_id = fields.String()
    mode_of_credit = fields.String()
    settlement_date = fields.Date()
    remarks = fields.String()
