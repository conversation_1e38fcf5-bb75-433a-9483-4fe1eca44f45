from ar_module.integration_tests.config.common_config import SUPER_ADMIN
from ar_module.integration_tests.config.sheet_names import CREATE_DEBTOR_SHEET_NAME
from ar_module.integration_tests.utilities.common_utils import (
    assert_,
    sanitize_test_data,
)
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateDebtor:
    def __init__(self, client, test_case_id, response, debtor_request, hotel_id):
        self.test_data = get_test_case_data(CREATE_DEBTOR_SHEET_NAME, test_case_id)[0]
        self.client = client
        self.response = response["data"]["debtor"]
        self.debtor_request = debtor_request
        self.hotel_id = hotel_id

    def validate_response(self):
        assert_(
            sanitize_test_data(self.response["debtor_code"]),
            sanitize_test_data(self.test_data["debtor_code"]),
        )
        assert_(
            sanitize_test_data(self.response["debtor_name"]),
            sanitize_test_data(self.test_data["debtor_name"]),
        )
        assert_(
            sanitize_test_data(self.response["hotel_id"]),
            sanitize_test_data(self.test_data["hotel_id"]),
        )

    def validate_get_debtors(self):
        get_debtors_response = self.debtor_request.get_debtors_request(
            self.client, 200, self.hotel_id, SUPER_ADMIN
        )
        for debtor in get_debtors_response["data"]["debtors"]:
            if debtor["debtor_id"] == self.response["debtor_id"]:
                assert_(self.response["debtor_code"], debtor["debtor_code"])
                assert_(self.response["debtor_name"], debtor["debtor_name"])
                assert_(self.response["hotel_id"], debtor["hotel_id"])
                assert_(self.response["debtor_type"], debtor["debtor_type"])
                assert_(self.response["user_profile_id"], debtor["user_profile_id"])
