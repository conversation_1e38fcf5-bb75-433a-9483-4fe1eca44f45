from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.value_objects.amount import Amount
from ar_module.domain.value_objects.invoice_meta import InvoiceMeta


class InvoiceDto(TenantMixin):
    def __init__(
        self,
        debtor_id,
        invoice_id,
        invoice_number,
        invoice_date,
        invoice_amount: Amount,
        vendor_id,
        invoice_url,
        status=None,
        due_date=None,
        remarks=None,
        auto_settled_via_credit=False,
        booking_reference_number=None,
        bill_to_debtor_code=None,
        is_spot_credit=False,
        invoice_meta: InvoiceMeta = None,
        tenant_id=None,
    ):
        super().__init__(tenant_id)
        self.debtor_id = debtor_id
        self.invoice_id = invoice_id
        self.invoice_number = invoice_number
        self.invoice_date = invoice_date
        self.invoice_amount = invoice_amount
        self.vendor_id = vendor_id
        self.invoice_url = invoice_url
        self.status = status
        self.due_date = due_date
        self.remarks = remarks
        self.auto_settled_via_credit = auto_settled_via_credit
        self.booking_reference_number = booking_reference_number
        self.bill_to_debtor_code = bill_to_debtor_code
        self.is_spot_credit = is_spot_credit
        self.invoice_meta = invoice_meta


class InvoiceUpdateData(object):
    def __init__(
        self,
        debit_aggregate,
        invoice_url,
    ):
        self.debit_aggregate = debit_aggregate
        self.invoice_url = invoice_url
