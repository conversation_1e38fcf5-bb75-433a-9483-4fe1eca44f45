from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas.request.common import BulkFileUploadRequestSchema
from ar_module.api.schemas.request.credit import (
    CreditCancelRequestSchema,
    CreditRequestSchema,
    CreditReversalRequestSchema,
    CreditSearchSchema,
)
from ar_module.api.schemas.response.common import FileUploadResponseSchema
from ar_module.api.schemas.response.credit import CreditResponseSchema, CreditSchema
from ar_module.application.api_handlers.bulk_cancel_credit_reversals import (
    BulkUploadCreditReversalCancellationHandler,
)
from ar_module.application.api_handlers.bulk_cancel_credits import (
    BulkUploadCreditCancellationHandler,
)
from ar_module.application.api_handlers.bulk_upload_credit_reversals import (
    BulkUploadCreditsReversalHandler,
)
from ar_module.application.api_handlers.bulk_upload_credits import (
    BulkUploadCreditsHandler,
)
from ar_module.application.dtos.credit_dto import (
    CreditReversalDetailsDto,
    CreditReversalDto,
)
from ar_module.application.services.credit_service import CreditService
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/credits", methods=["POST"])
@schema_wrapper_parser(CreditRequestSchema)
@inject(credit_service=CreditService)
def create_new_credit(credit_service, parsed_request):
    """
    Creates new Credit (Payment) entry, along with optional Settlements
    ---
    operationId: create_credit
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates new Credit (Payment) entry, along with optional Settlements.
          Credit can have credit_type of `credit_note`, `payment` or `tds`. Through this API, credits of only `payment`
          credit_type should be created. While using Settlement, this API can optionally create one or more `tds`
          credits, if requested.

        tags:
            - Credit
        parameters:
            - in: body
              name: body
              description: The Credit object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/CreditRequestSchema"
        responses:
            200:
                description: Newly created Credit
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                credit:
                                    $ref: "#/definitions/CreditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    credit_aggregate = credit_service.create_new_credit(
        parsed_request, user_data=user_data
    )
    credit_response_schema = CreditSchema()
    response = credit_response_schema.dump(credit_aggregate.credit)
    return ApiResponse.build(data=dict(credit=response.data), status_code=200)


@ar_bp.route("/credits", methods=["GET"])
@schema_wrapper_parser(CreditSearchSchema, param_type=RequestTypes.ARGS)
@inject(credit_service=CreditService)
def get_debtor_credits(credit_service, parsed_request):
    """
    Returns all the credits for the given debtor_id (in query param).
    ---
    operationId: get_debtor_credits
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema: CreditSearchSchema
        description: Fetch credits for the given debtor_id as provided in query parameter. Apply any extra filter
            passed to the result
        tags:
            - Credit
        responses:
            200:
                description: List of existing Credits
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                credits:
                                    type: array
                                    items:
                                        $ref: "#/definitions/CreditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    get_total_count = (
        True
        if parsed_request.get("limit") is not None
        and parsed_request.get("offset") is not None
        else False
    )
    credit_search_query = CreditSearchQuery(**parsed_request)
    credit_aggregates, total_credits = credit_service.search_credits(
        credit_search_query, get_total_count=get_total_count, user_data=user_data
    )
    credit_response_schema = CreditResponseSchema()
    response = credit_response_schema.dump(
        dict(
            credits=[aggregate.credit for aggregate in credit_aggregates],
            limit=parsed_request.get("limit"),
            offset=parsed_request.get("offset"),
            total=total_credits,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/credits/bulk-upload", methods=["POST"])
@schema_wrapper_parser(BulkFileUploadRequestSchema)
@inject(handler=BulkUploadCreditsHandler)
def bulk_upload_credits(handler: BulkUploadCreditsHandler, parsed_request):
    """
    Bulk process credit in async
    ---
    operationId: create_credits_async
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates credits in async by processing file input.
        tags:
            - Credit-Upload
        parameters:
            - in: body
              name: body
              description: The object contains list of file urls to be processed
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/BulkFileUploadRequestSchema"
        responses:
            200:
                description: List of async jobs created to process files
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/FileUploadResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    response_dto = handler.handle(parsed_request.get("file_urls"), user_data=user_data)
    response = FileUploadResponseSchema(many=True).dump(response_dto).data
    return ApiResponse.build(data=response, status_code=200)


@ar_bp.route("/credit/bulk-cancel", methods=["POST"])
@schema_wrapper_parser(BulkFileUploadRequestSchema)
@inject(handler=BulkUploadCreditCancellationHandler)
def bulk_upload_cancel_credits(
    handler: BulkUploadCreditCancellationHandler, parsed_request
):
    """
    Bulk Process credit cancellation in async
    ---
    operationId: bulk_upload_cancel_credits
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Cancel credits in async by processing file input.
        tags:
            - Cancel-Credit-Upload
        parameters:
            - in: body
              name: body
              description: The object contains list of file urls to be processed
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/BulkFileUploadRequestSchema"
        responses:
            200:
                description: List of async jobs created to process files
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/FileUploadResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    response_dto = handler.handle(parsed_request.get("file_urls"), user_data=user_data)
    response = FileUploadResponseSchema(many=True).dump(response_dto).data
    return ApiResponse.build(data=response, status_code=200)


@ar_bp.route("/credit/<string:credit_id>", methods=["POST"])
@schema_wrapper_parser(CreditCancelRequestSchema)
@inject(credit_service=CreditService)
def cancel_credit(credit_service, credit_id, parsed_request):
    """
    Cancel credit with given credit id (in query param).
    ---
    operationId: cancel_credit
    parameters:
        - in: path
          name: credit_id
          description: The credit id of the payment
          required: True
          type: string
    delete:
        description: Cancel credit for given credit id
        tags:
            - Credit
        responses:
            200:
                description: Credit cancelled successfully
    """
    user_data = read_user_data_from_request_header()
    credit_service.cancel_credit(
        credit_id,
        cancellation_reason=parsed_request.get("cancellation_reason"),
        user_data=user_data,
    )
    return ApiResponse.build(status_code=200)


@ar_bp.route("/credit_reversal", methods=["POST"])
@schema_wrapper_parser(CreditReversalRequestSchema)
@inject(credit_service=CreditService)
def create_credit_reversal(credit_service, parsed_request):
    """
    Creates new Credit Reversal entry.
    ---
    operationId: create_credit_reversal
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates new Credit Reversal entry.
        tags:
            - Credit Reversal
        parameters:
            - in: body
              name: body
              description: The Credit Reversal object which needs to be created
              required: True
              schema:
                type: object
                $ref: "#/definitions/CreditReversalRequestSchema"
        responses:
            200:
                description: Newly created Credit Reversal
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/CreditSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    credit_reversal_request_dto = CreditReversalDto(
        debtor_id=parsed_request.get("debtor_id"),
        credit_type=parsed_request.get("credit_type"),
        date=parsed_request.get("date"),
        amount_in_base_currency=parsed_request.get("amount_in_base_currency"),
        amount_in_credit_currency=parsed_request.get("amount_in_credit_currency"),
        mode_of_credit=parsed_request.get("mode_of_credit"),
        reference_number=parsed_request.get("reference_number"),
        credit_reversals=[
            CreditReversalDetailsDto(**reversal)
            for reversal in parsed_request.get("credit_reversals", [])
        ],
    )
    credit_aggregate = credit_service.create_new_credit_reversal(
        credit_reversal_request_dto, user_data=user_data
    )
    credit_response_schema = CreditSchema()
    response = credit_response_schema.dump(credit_aggregate.credit)
    return ApiResponse.build(data=dict(credit=response.data), status_code=200)


@ar_bp.route("/credit_reversal/<string:credit_id>", methods=["DELETE"])
@inject(credit_service=CreditService)
def cancel_credit_reversal(credit_service, credit_id):
    """
    Cancel credit reversals with given credit id (in query param).
    ---
    operationId: cancel_credit_reversal
    parameters:
        - in: path
          name: credit_id
          description: The credit id of the refund
          required: True
          type: string
    delete:
        description: Cancel credit reversals for given credit id
        tags:
            - Credit Reversal
        responses:
            200:
                description: Credit Reversals cancelled successfully
    """
    user_data = read_user_data_from_request_header()
    credit_service.cancel_credit_reversal(credit_id, user_data=user_data)
    return ApiResponse.build(status_code=200)


@ar_bp.route("/credit-reversal/bulk-cancel", methods=["POST"])
@schema_wrapper_parser(BulkFileUploadRequestSchema)
@inject(handler=BulkUploadCreditReversalCancellationHandler)
def bulk_upload_cancel_credit_reversals(
    handler: BulkUploadCreditReversalCancellationHandler, parsed_request
):
    """
    Bulk Process credit reversal cancellation in async
    ---
    operationId: bulk_upload_cred
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Cancel credits reversals in async by processing file input.
        tags:
            - Cancel-Credit-Upload
        parameters:
            - in: body
              name: body
              description: The object contains list of file urls to be processed
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/BulkFileUploadRequestSchema"
        responses:
            200:
                description: List of async jobs created to process files
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/FileUploadResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    response_dto = handler.handle(parsed_request.get("file_urls"), user_data=user_data)
    response = FileUploadResponseSchema(many=True).dump(response_dto).data
    return ApiResponse.build(data=response, status_code=200)


@ar_bp.route("/credit-reversal/bulk-upload", methods=["POST"])
@schema_wrapper_parser(BulkFileUploadRequestSchema)
@inject(handler=BulkUploadCreditsReversalHandler)
def bulk_upload_credit_reversals(
    handler: BulkUploadCreditsReversalHandler, parsed_request
):
    """
    Bulk process credit reversals request in async
    ---
    operationId: create_credit_reversals_async
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates credits reversals in async by processing file input.
        tags:
            - Credit-Reversals-Upload
        parameters:
            - in: body
              name: body
              description: The object contains list of file urls to be processed
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/BulkFileUploadRequestSchema"
        responses:
            200:
                description: List of async jobs created to process files
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/FileUploadResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    response_dto = handler.handle(parsed_request.get("file_urls"), user_data=user_data)
    response = FileUploadResponseSchema(many=True).dump(response_dto).data
    return ApiResponse.build(data=response, status_code=200)
