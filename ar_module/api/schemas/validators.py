from marshmallow import ValidationError
from marshmallow.validate import Valida<PERSON>

from ar_module.application.services.user_defined_enums import UserDefinedEnums
from object_registry import locate_instance


def validate_positive_integer(value):
    if value <= 0:
        raise ValidationError("Field value should be greater than 0")


def validate_date_range(start_date, end_date):
    if start_date and end_date and start_date > end_date:
        raise ValidationError("Start date should be less than End date")


class UserDefinedEnumValidator(Validator):
    """
    :param enum_name: Enum Name of UserDefinedEnum against which the value should be validated.
    """

    message_enum_undefined = "Enum: {enum_name} not defined."
    message_invalid_value = (
        "{input} is not a valid value. Must be one of: {enum_values}"
    )

    def __init__(self, enum_name=None):
        self.enum_name = enum_name

    def _repr_args(self):
        return "enum_name={0!r}".format(self.enum_name)

    def _format_error(self, value, message, enum_values=None):
        return message.format(
            input=value, enum_name=self.enum_name, enum_values=enum_values
        )

    def __call__(self, value):
        if self.enum_name is not None:

            enum_values = locate_instance(UserDefinedEnums).get_enum(self.enum_name)

            if not enum_values:
                message = self.message_enum_undefined
                raise ValidationError(self._format_error(value, message))

            if value not in enum_values:
                message = self.message_invalid_value
                raise ValidationError(
                    self._format_error(value, message, enum_values=enum_values)
                )
        return value
