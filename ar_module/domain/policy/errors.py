from ar_module.exceptions import ARError


class PolicyError(ARError):
    # Use error code sequence starting from "21"
    VIEW_CREDIT_NOT_ALLOWED = (
        "2101",
        "You are not authorized to view credits",
    )
    VIEW_DEBTORS_NOT_ALLOWED = (
        "2102",
        "You are not authorized to view debtors",
    )
    CREATE_NEW_DEBTOR_NOT_ALLOWED = (
        "2103",
        "You are not authorized to create debtors",
    )

    CREATE_MANUAL_DEBIT_NOT_ALLOWED = (
        "2104",
        "You are not authorized to create manual debits",
    )

    CREATE_CREDIT_NOT_ALLOWED = (
        "2105",
        "You are not authorized to create credit",
    )

    CREATE_SETTLEMENTS_NOT_ALLOWED = (
        "2106",
        "You are not authorized to create/update settlements",
    )

    CANCEL_CREDIT_NOT_ALLOWED = (
        "2107",
        "You are not authorized to cancel credit",
    )

    VIEW_DEBIT_NOT_ALLOWED = (
        "2108",
        "You are not authorized to view debits",
    )

    VIEW_SETTLEMENTS_NOT_ALLOWED = (
        "2109",
        "You are not authorized to view settlements",
    )

    DEBTOR_SUMMARY_VIEW_NOT_ALLOWED = (
        "2110",
        "You are not authorized to view debtor summary",
    )

    CREATE_CREDIT_REVERSAL_NOT_ALLOWED = (
        "2111",
        "You are not authorized to create refund",
    )

    CANCEL_CREDIT_REVERSAL_NOT_ALLOWED = (
        "2112",
        "You are not authorized to cancel refund",
    )
