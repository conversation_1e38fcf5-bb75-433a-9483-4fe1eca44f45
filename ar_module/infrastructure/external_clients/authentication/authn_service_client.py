import logging

from ths_common.exceptions import DownstreamSystemFailure

from ar_module.infrastructure.external_clients.authentication.user_data_dto import (
    UserDataDTO,
)
from ar_module.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from ar_module.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class AuthNClient(BaseExternalClient):
    page_map = {
        "get_user_by_email": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/treeboauth/profile/v1/user/validation/email/?email={email}",
        ),
        "get_user_by_id": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/treeboauth/profile/v1/user/detail/{user_id}",
        ),
    }

    def __init__(self):
        super().__init__()

    def get_domain(self):
        return ServiceRegistryClient.get_authn_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        return headers

    def get_user_by_email(self, email):

        page_name = "get_user_by_email"
        url_params = dict(email=email)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            logger.error("Authn API failed: %s", response)
            raise DownstreamSystemFailure(
                message="AuthN API Error",
                description="AuthN API Error Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                ),
                extra_payload=url_params,
            )
        api_response = UserDataDTO.from_json(response.data) if response.data else None
        return api_response

    def get_user_by_id(self, user_id):
        page_name = "get_user_by_id"
        url_params = dict(user_id=user_id)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            logger.error("Authn API failed: %s", response)
            raise DownstreamSystemFailure(
                message="AuthN API Error",
                description="AuthN API Error Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                ),
                extra_payload=url_params,
            )
        return UserDataDTO.from_json(response.data)
