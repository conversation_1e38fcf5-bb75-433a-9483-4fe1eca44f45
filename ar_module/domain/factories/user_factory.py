from ar_module.domain.aggregates.user_aggregate import UserAggregate
from ar_module.domain.entities.user import User


class UserFactory(object):
    @staticmethod
    def create_new_user(
        user_id, email, phone_number=None, first_name=None, last_name=None
    ):
        user = User(
            user_id=user_id,
            email=email,
            phone_number=phone_number,
            first_name=first_name,
            last_name=last_name,
        )
        user_aggregate = UserAggregate(user=user)
        return user_aggregate
