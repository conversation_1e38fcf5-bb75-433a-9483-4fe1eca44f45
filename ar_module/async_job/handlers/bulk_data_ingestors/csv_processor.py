import copy
import csv
import datetime
import os
import uuid

import requests
from ths_common.value_objects import EmailAttachment

from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.notification_service_client import (
    EmailConfigDto,
    NotificationEmailIds,
    NotificationSenderName,
)


class ProcessingStatus(object):
    FAILED = "failed"
    SUCCESS = "success"


class BaseCsvProcessor:
    def __init__(
        self,
        notification_service_client,
        input_schema,
        s3_ack_file_path,
        process_name,
    ):
        self.input_schema = input_schema
        self.process_name = process_name
        self.s3_ack_file_path = s3_ack_file_path
        self.notification_service_client = notification_service_client

    @staticmethod
    def read_file(file_url):
        signed_file_url = AwsServiceClient.get_presigned_url_from_s3_url(file_url, 3600)
        return requests.request("GET", signed_file_url)

    def process_file(self, file_url, ack_email_config=None, **kwargs):
        response = self.read_file(file_url)
        records = csv.DictReader(response.text.splitlines())
        ack_data = []
        for record in records:
            schema = self.input_schema(context=kwargs)
            errors = schema.validate(record)
            ack_record = copy.copy(record)
            if errors:
                self.capture_failure(ack_record, errors)
            else:
                validated_data, _ = schema.load(record)
                data, errors = self.ingest_data(validated_data, ack_record, **kwargs)
                if errors:
                    self.capture_failure(ack_record, errors)
                else:
                    self.capture_success(ack_record)
            ack_data.append(ack_record)
        if ack_data:
            return self._prepare_upload_and_email_acknowledgement_file(
                ack_data, ack_email_config
            )
        return self._send_invalid_file_acknowledgement(ack_email_config)

    def _prepare_upload_and_email_acknowledgement_file(
        self, ack_data_list, ack_email_config: EmailConfigDto
    ):
        pre_signed_url, file_name = self._generate_csv_upload_results_url(ack_data_list)
        if ack_email_config:
            body = f"""Hi <br><br>Bulk upload of {self.process_name} is completed. Please check attached csv to 
        view the status<br><br> """
            self._send_email_acknowledgement(
                ack_email_config, body, pre_signed_url, file_name
            )

    @staticmethod
    def capture_failure(ack_record, errors):
        ack_record.update(
            {
                "status": ProcessingStatus.FAILED,
                "remark": str(errors) if errors else None,
            }
        )

    @staticmethod
    def capture_success(ack_record):
        ack_record.update({"status": ProcessingStatus.SUCCESS, "remark": "Uploaded"})

    def ingest_data(self, validated_dto, ack_record, **kwargs):
        raise NotImplementedError()

    def _generate_csv_upload_results_url(self, data_list):
        column_values = data_list[0].keys()
        file_path, file_name = self._generate_file_name()
        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(data_list, column_values)
            pre_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                self.s3_ack_file_path,
                csv_writer.file_path,
                self.get_default_expiration_time(),
            )

        return pre_signed_url, file_name

    def _generate_file_name(self, extension="csv"):
        identifier = str(uuid.uuid4())
        file_name = f"{datetime.datetime.now().strftime('%Y-%m-%d')}-{self.process_name}-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/") + file_name
        ), file_name

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def _send_email_acknowledgement(
        self,
        ack_email_config: EmailConfigDto,
        body,
        pre_signed_url=None,
        file_name=None,
    ):
        email_attachments = None
        if pre_signed_url and file_name:
            email_attachments = [
                EmailAttachment(url=pre_signed_url, filename=file_name)
            ]
        self.notification_service_client.email(
            body_html=body,
            subject=ack_email_config.subject,
            sender=NotificationEmailIds.NOREPLY.value,
            recievers=ack_email_config.receivers,
            attachments=email_attachments,
            sender_name=NotificationSenderName.TREEBO_HOTELS.value,
            raise_on_failure=False,
            cc_list=ack_email_config.cc_list,
        )

    def _send_invalid_file_acknowledgement(self, ack_email_config: EmailConfigDto):
        body = f"""Hi <br><br>Bulk upload of {self.process_name} has failed. Please upload a valid file<br><br> """
        self._send_email_acknowledgement(ack_email_config, body)
