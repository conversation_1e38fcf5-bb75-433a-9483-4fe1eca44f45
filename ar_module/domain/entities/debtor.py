from ar_module.application.consumers.constants import DebtorTypes


class Debtor(object):
    def __init__(
        self,
        debtor_id,
        debtor_code,
        debtor_name,
        user_profile_id=None,
        debtor_type=DebtorTypes.B2B,
        credit_period=None,
        credit_limit=None,
        btc_enabled=None,
        hotel_id=None,
        profile_status=None,
        pocs=None,
        registered_address=None,
    ):
        self.debtor_id = debtor_id
        self.debtor_code = debtor_code
        self.debtor_name = debtor_name
        self.user_profile_id = user_profile_id
        self.debtor_type = debtor_type
        self.credit_period = credit_period
        self.credit_limit = credit_limit
        self.btc_enabled = btc_enabled
        self.hotel_id = hotel_id
        self.profile_status = profile_status
        self.pocs = pocs
        self.registered_address = registered_address
