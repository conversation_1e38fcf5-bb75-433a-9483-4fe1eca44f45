import logging
from collections import defaultdict

from ar_module.domain.dtos.debtor import B2CDebtorCode
from ar_module.domain.reports.dtos.erp_events_details_dto import (
    CreditDetails,
    ERPArData,
    FolioDetails,
    HotelDetails,
)
from ar_module.domain.reports.erp_event_publisher import ERPEvent, ERPEventPublisher
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from ar_module.infrastructure.external_clients.core.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ar_module.infrastructure.external_clients.crs_client import CrsClient

logger = logging.getLogger(__name__)


class ERPService:
    def prepare_and_publish_erp_details(
        self, hotel_id, business_date, event_id, batch_id
    ):
        logger.info(
            f"Processing Event for event_id: {event_id} for hotel_id: {hotel_id} and business_date: {business_date}"
        )
        b2b_debtor_code, crs_booking_data, folio_details, payments = (
            set(),
            defaultdict(dict),
            list(),
            list(),
        )
        credit_details = CreditRepository().get_credit_data_for_erp_reports(
            hotel_id, business_date
        )
        for cd in credit_details:
            if cd.debtor_type == "b2b":
                b2b_debtor_code.add(cd.debtor_code)
                payments.append(CreditDetails(cd).to_json())
            else:
                payments.append(CreditDetails(cd).to_json())
                debtor_code = B2CDebtorCode.decode_from_str_repr(cd.debtor_code)
                crs_booking_data[debtor_code.tenant_id][
                    debtor_code.booking_reference_id
                ] = debtor_code.customer_id

        tenant_ids = crs_booking_data.keys()
        for tenant_id in tenant_ids:
            booking_ids = crs_booking_data[tenant_id].keys()
            b2c_booking_details = CrsClient.get_bookings(
                reference_numbers=",".join(booking_ids),
                tenant_id=tenant_id,
            )
            for bd in b2c_booking_details.bookings:
                customer_id = crs_booking_data[tenant_id][bd.reference_number]
                folio_detail = bd.customers[int(customer_id) - 1]
                folio_name = self.get_folio_name(folio_detail)
                folio_address = (
                    self.get_folio_address(folio_detail.address).replace(",", " ")
                    if folio_detail.address
                    else None
                )
                folio_details.append(
                    FolioDetails(
                        bd.reference_number + "-" + customer_id,
                        folio_name,
                        folio_address,
                        "b2c",
                    ).to_json()
                )

        if b2b_debtor_code:
            b2b_company_details = (
                CompanyProfileServiceClient().get_company_profile_on_company_code(
                    hotel_id, ",".join(b2b_debtor_code)
                )
            )
            if not b2b_company_details:
                for bdc in b2b_debtor_code:
                    folio_details.append(FolioDetails(bdc, "", "", "b2b").to_json())
            for cd in b2b_company_details:
                if cd.get("superhero_company_code") in b2b_debtor_code:
                    folio_address = (
                        self.get_folio_address_for_company(
                            cd.get("registered_address")
                        ).replace(",", " ")
                        if cd.get("registered_address")
                        else None
                    )
                    folio_details.append(
                        FolioDetails(
                            cd.get("superhero_company_code"),
                            cd.get("legal_entity_name"),
                            folio_address,
                            "b2b",
                        ).to_json()
                    )
                else:
                    folio_details.append(
                        FolioDetails(
                            cd.get("superhero_company_code"), "", "", "b2b"
                        ).to_json()
                    )
        hotel_details = HotelDetails(
            hotel_id, CatalogServiceClient().get_hotel_state(hotel_id)
        ).to_json()
        ar_erp_data = ERPArData(payments, folio_details, hotel_details).to_json()
        erp_event = ERPEvent(
            body=dict(erp_data=ar_erp_data),
            routing_key="armodule.erp.interface_exchange",
            event_type="erp-armodule",
            event_id=event_id,
            batch_id=batch_id,
            source="armodule",
        )
        ERPEventPublisher().publish(erp_event)
        logger.info(f"Event Published with paylooad: {erp_event}")

    @staticmethod
    def get_folio_name(customer_details):
        folio_name = ""
        if customer_details.first_name:
            folio_name += customer_details.first_name
        if customer_details.last_name:
            folio_name += " " + customer_details.last_name
        return folio_name

    @staticmethod
    def get_folio_address(address_details):
        folio_address = ""
        if address_details.field1:
            folio_address += address_details.field1
        if address_details.field2:
            folio_address += " " + address_details.field2
        if address_details.city:
            folio_address += " " + address_details.city
        if address_details.state:
            folio_address += " " + address_details.state
        if address_details.country:
            folio_address += " " + address_details.country
        if address_details.pincode:
            folio_address += " " + address_details.pincode
        return folio_address

    @staticmethod
    def get_folio_address_for_company(address_details):
        folio_address = ""
        if address_details.get("address_line_1"):
            folio_address += address_details.get("address_line_1")
        if address_details.get("address_line_2"):
            folio_address += " " + address_details.get("address_line_2")
        if address_details.get("city"):
            folio_address += " " + address_details.get("city")
        if address_details.get("state"):
            folio_address += " " + address_details.get("state")
        if address_details.get("country"):
            folio_address += " " + address_details.get("country")
        if address_details.get("pincode"):
            folio_address += " " + address_details.get("pincode")
        return folio_address
