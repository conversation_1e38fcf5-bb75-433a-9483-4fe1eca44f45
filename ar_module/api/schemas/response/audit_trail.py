from marshmallow import Schema, fields


class AuditTrailSchema(Schema):
    created_at = fields.DateTime()
    user = fields.String()
    user_type = fields.String()
    application = fields.String()
    audit_type = fields.String()
    audit_payload = fields.Dict()


class AuditTrailPaginatedResponseSchema(Schema):
    """
    DNR search response schema
    """

    audit_trails = fields.Nested(AuditTrailSchema, many=True)
    limit = fields.Integer(description="Number of results")
    offset = fields.Integer(description="Offset from which the results are fetched")
    total = fields.Integer(description="Total number of credits for the query")
