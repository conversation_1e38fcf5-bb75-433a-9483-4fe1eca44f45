from treebo_commons.money import Money

from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.constants import CreditStatus, Modules
from ar_module.domain.entities.base_entity import EntityChangeTracker
from ar_module.exceptions import InvalidSettlementException


class Credit(EntityChangeTracker, TenantMixin):
    def __init__(
        self,
        credit_id,
        debtor_id,
        credit_type,
        date,
        amount_in_base_currency: Money,
        amount_in_credit_currency: Money,
        reference_number,
        created_at=None,
        reference_id=None,
        mode_of_credit=None,
        dirty=True,
        status=CreditStatus.CREATED,
        new=True,
        unused_credit_amount=None,
        refunded_amount=None,
        credit_reversals=None,
        deleted=False,
        used_to_auto_settle_debit=False,
        linked_debit_id=None,
        approval_document=None,
        cancellation_date=None,
        recorded_via=Modules.AR_MODULE,
        tenant_id=None,
        cancellation_reason=None,
    ):
        super().__init__(dirty=dirty, new=new)
        TenantMixin.__init__(self, tenant_id)
        self.credit_id = credit_id
        self.debtor_id = debtor_id
        self.credit_type = credit_type
        self.date = date
        # Payment amount can be in different currency than base currency
        self.amount_in_credit_currency = amount_in_credit_currency
        # This amount will always be in base currency of hotel
        self.amount_in_base_currency = amount_in_base_currency
        self.reference_number = reference_number
        self.reference_id = reference_id
        self.mode_of_credit = mode_of_credit
        self.created_at = created_at
        # Unused credit amount will be in base currency only
        if unused_credit_amount is not None:
            self.unused_credit_amount = unused_credit_amount
        else:
            self.unused_credit_amount = amount_in_base_currency
        if refunded_amount is not None:
            self.refunded_amount = refunded_amount
        else:
            self.refunded_amount = Money(0, amount_in_base_currency.currency)
        self.credit_reversals = credit_reversals or []
        self.deleted = deleted
        self.used_to_auto_settle_debit = used_to_auto_settle_debit
        self.status = status
        self.linked_debit_id = linked_debit_id
        self.approval_document = approval_document
        self.cancellation_date = cancellation_date
        self.recorded_via = recorded_via
        if cancellation_reason is not None:
            self.cancellation_reason = cancellation_reason
        else:
            self.cancellation_reason = None

    def knock_off_used_amount(self, amount: Money):
        if amount > self.unused_credit_amount:
            raise InvalidSettlementException(
                message="Settlement amount is greater than remaining Unused credit amount"
            )
        self.unused_credit_amount -= amount
        self.mark_dirty()

    def reverse_settlement_amount(self, amount: Money):
        self.unused_credit_amount += amount
        self.mark_dirty()

    def link_debit(self, debit_id):
        if self.linked_debit_id == debit_id:
            return
        self.linked_debit_id = debit_id
        self.mark_dirty()

    def update_refunded_amount(self, amount: Money):
        self.refunded_amount += amount
        self.mark_dirty()
