from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_credit_facts import CreateCreditFacts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CreateCreditRule(BaseRule):
    def allow(self, facts: CreateCreditFacts, privileges=None):
        if PrivilegeCode.CREATE_CREDIT not in privileges:
            raise PolicyAuthException(error=PolicyError.CREATE_CREDIT_NOT_ALLOWED)
        return True
