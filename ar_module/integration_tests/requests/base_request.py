import logging

from flask import json

from ar_module.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.debtor_id = None
        self.debtor_ids = []
        self.debit_id = []
        self.credit_id = None
        self.credit_reference_number = None
        self.hotel_id = None
        self.debit_reference_numbers = []
        self.pay_mode = None
        self.credit_ids = []

    def request_processor(
        self,
        client_,
        request_type,
        url,
        status_code,
        request_json=None,
        parameters=None,
        user_type=None,
        hotel_id=None,
    ):
        headers = {"Content-Type": "application/json"}
        if user_type:
            headers["X-User-Type"] = user_type
        if hotel_id:
            headers["X-Hotel-Id"] = hotel_id

        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "GET": client_.get,
            "DELETE": client_.delete,
        }

        print("\n\n" + "#" * 25 + "REQUEST" + "#" * 25)
        print(
            "REQUEST URL: "
            + url
            + "\nREQUEST TYPE: "
            + request_type
            + "\nHEADERS: "
            + str(headers)
            + "\nREQUEST JSON: "
            + str(request_json)
            + "\nREQUEST PARAMS: "
            + str(parameters)
        )
        response = client_type.get(request_type)(
            url, data=request_json, headers=headers
        )
        print("#" * 25 + "RESPONSE" + "#" * 25)
        print(
            "RESPONSE CODE: "
            + str(response.status_code)
            + "\nRESPONSE DATA: "
            + json.dumps(response.json)
        )
        assert_(response.status_code, status_code, "Status code is not matching")
        return response.json
