import json

from ar_module.integration_tests.mockers import mock_get_roles, mock_get_tenant_configs


def create_debit(client_, create_debit_payload, debtor_id, config_value=True):
    url = "/ar/v1/debits"
    _payload = dict(json.loads(create_debit_payload))
    _payload["debtor_id"] = debtor_id
    payload = json.dumps({"data": _payload})
    with mock_get_tenant_configs(config_value):
        with mock_get_roles():
            response = client_.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )

    assert response.status_code == 200
    debit_response = json.loads(response.data.decode("utf-8"))
    assert len(debit_response["data"]["debit"]) > 0
    debit_id = debit_response["data"]["debit"]["debit_id"]

    return debit_id
