import json
from datetime import date, datetime, timedelta

from _decimal import Decimal
from treebo_commons.multitenancy.sqlalchemy import db_engine


# returns only the date for the given day
def return_date(days):
    return date.today() + timedelta(days=int(days))


# todo: with time completely replace sanitize_blank method with sanitize_test_data
# sanitize the values for blank fields from excel
def sanitize_blank(value):
    if (
        value == ""
        or value == "null"
        or value == {}
        or value == "BLANK"
        or value == "NULL"
    ):
        return None
    else:
        return value


# interpret the values for NULL/EMPTY/blank fields from excel
def sanitize_test_data(data):
    if data == "" or data == "null":
        return None
    elif data == "NULL":
        return "NULL_KEY"
    elif data == "EMPTY":
        return ""
    elif isinstance(data, dict):
        if all(value is None for value in data.values()):
            return None
        elif all(value == "NULL_KEY" or value is None for value in data.values()):
            return "NULL_KEY"
        else:
            return data
    else:
        return data


# for deleting the null values and empty dict from object passed
def del_none(dict_object):
    for key, value in list(dict_object.items()):
        if isinstance(value, dict):
            del_none(value)
        elif isinstance(value, list):
            for val in value:
                if isinstance(val, dict):
                    del_none(val)
        if value is None:
            dict_object[key] = None

    return dict_object


def assert_(actual_value, expected_value, failure_message=None):
    assert sanitize_test_data(actual_value) == sanitize_test_data(expected_value), (
        str(failure_message)
        + ". ACTUAL: "
        + str(actual_value)
        + " EXPECTED: "
        + str(expected_value)
    )


def query_execute(query, commit_required=True):
    result = db_engine.get_scoped_session(None).execute(query)
    if commit_required:
        db_engine.get_scoped_session(None).commit()
    return result


def custom_serializer(obj):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return str(obj)
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


def query_execute_and_convert_to_json(query, commit_required=True):
    result = query_execute(query, commit_required)
    columns = result.keys()
    result_data = result.fetchall()
    data = []
    for row in result_data:
        data.append(dict(zip(columns, row)))
    # Convert the data to JSON using the custom serialization function
    json_data = json.loads(json.dumps(data, default=custom_serializer))
    return json_data
