import logging
import traceback
from datetime import datetime

from ths_common.exceptions import DatabaseLockError
from treebo_commons.multitenancy.sqlalchemy import db_engine

from ar_module.async_job.job.entities.job_entity import JobEntity
from ar_module.async_job.job_result_dto import JobResultDto
from object_registry import register_instance

MAX_RETRY = 3

logger = logging.getLogger(__name__)


@register_instance(dependencies=[])
class BaseJobHandler:
    def handle(self, job: JobEntity):
        try:
            job_data, job_id = job.data, job.job_id
            self.execute_job(job_id, job_data)
            return JobResultDto()
        except Exception as e:
            db_engine.get_session().rollback()
            logger.exception("Job failed")
            should_retry = job.total_tries < MAX_RETRY
            retry_at_eta = datetime.now()
            failure = f"Failed {str(traceback.format_exc())}"
            if isinstance(e, DatabaseLockError):
                should_retry = (
                    True  # if critical section is locked then no matter we retry
                )
                retry_at_eta = datetime.now()
            return JobResultDto(
                run_successful=False,
                should_retry=should_retry,
                retry_at_eta=retry_at_eta,
                remarks=failure,
            )

    def execute_job(self, job_id, job_data):
        raise NotImplementedError()
