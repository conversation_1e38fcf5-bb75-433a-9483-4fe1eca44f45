import datetime
import logging
import os
from typing import List

import xlsxwriter
from ths_common.exceptions import InvalidOperationError

logger = logging.getLogger(__name__)


class ExcelWriter(object):
    def __init__(self, file_path):
        self.file_path = file_path
        self.workbook = xlsxwriter.Workbook(self.file_path)
        self._write_header_row = True

    def __enter__(self):
        return self

    def __exit__(self, type, value, traceback):
        self.tear_down()

    def save_file(self):
        self.workbook.close()

    def write_aggregates(self, aggregates: List, attributes_map, sheet_name=None):

        sheet = self.workbook.get_worksheet_by_name(sheet_name)
        if not sheet:
            sheet = self.workbook.add_worksheet(sheet_name)

        attribute_keys = attributes_map.keys()
        attribute_header = attributes_map.values()

        if not aggregates:
            raise InvalidOperationError(
                description="Empty aggregate list to generate Excel file"
            )

        row = 0
        if self._write_header_row:
            for (index, header_name) in enumerate(attribute_header):
                sheet.write(row, index, header_name)
            row += 1

        for aggregate in aggregates:
            for (index, item) in enumerate(
                self._get_data_rows(attribute_keys, aggregate)
            ):
                if isinstance(item, datetime.date) or isinstance(
                    item, datetime.datetime
                ):
                    sheet.write(row, index, str(item))
                else:
                    sheet.write(row, index, item)
            row += 1
        return self

    @staticmethod
    def _get_data_rows(attributes, aggregate):
        if isinstance(aggregate, dict):
            data_rows = [aggregate.get(attr) for attr in attributes]
        else:
            data_rows = [getattr(aggregate, attr, None) for attr in attributes]
        return data_rows

    def tear_down(self):
        try:
            os.remove(self.file_path)
        except FileNotFoundError:
            logger.info(f"No file found to tear down")
        except OSError as e:
            logger.exception(f"Unable to tear down file due to {e}")
