from treebo_commons.utils import dateutils

from ar_module.async_job.job.entities.job_entity import JobEntity
from ar_module.async_job.job.job_constants import JobStatus


class JobAggregate:
    def __init__(self, job_entity: JobEntity):
        self.job_entity = job_entity

    @property
    def status(self):
        return self.job_entity.status

    @property
    def job_id(self):
        return self.job_entity.job_id

    def update_status(self, success, failure_message=None):
        status = JobStatus.FINISHED if success else JobStatus.FAILED
        self.job_entity.status = status
        self.job_entity.failure_message = failure_message
        self.job_entity.total_tries += 1

    def failed(self, failure_message):
        self.job_entity.status = JobStatus.FAILED
        self.job_entity.total_tries += 1
        self.job_entity.failure_message = failure_message

    def completed(self):
        self.job_entity.status = JobStatus.FINISHED
        self.job_entity.failure_message = None

    def is_completed(self):
        return self.job_entity.status == JobStatus.FINISHED

    def is_failed(self):
        return self.job_entity.status == JobStatus.FAILED

    def reschedule(self, eta, reason=None):
        self.job_entity.eta = eta
        self.job_entity.status = JobStatus.RESCHEDULED
        self.job_entity.total_tries += 1

        if reason:
            self.job_entity.failure_message = reason
