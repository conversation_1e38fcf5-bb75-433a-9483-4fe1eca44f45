import datetime

from dateutil.relativedelta import relativedelta
from treebo_commons.utils import dateutils

from ar_module.domain.constants import SettlementFrequency


def get_month_wise_date_range_breakup(start_date, end_date):
    date_range_breakup = []
    while start_date < end_date:
        month_last_date = dateutils.last_date_of_month(start_date)
        if month_last_date.month == end_date.month:
            month_last_date = end_date
        date_range_breakup.append(
            dict(month_start_date=start_date, month_end_date=month_last_date)
        )
        start_date += relativedelta(months=1)
    return date_range_breakup


def calculate_due_date_based_on_settlement_frequency(date, settlement_freq):
    if settlement_freq == SettlementFrequency.QUARTERLY:
        return get_last_date_of_quarter(date)
    return get_last_date_of_month(date)


def get_last_date_of_month(date):
    if isinstance(date, datetime.date):
        date = dateutils.date_to_ymd_str(date)
    date_time = datetime.datetime.strptime(date, "%Y-%m-%d")
    return dateutils.last_date_of_month(date_time)


def get_last_date_of_quarter(date):
    if isinstance(date, datetime.date):
        date = dateutils.date_to_ymd_str(date)
    date_time = datetime.datetime.strptime(date, "%Y-%m-%d")
    last_month_of_quarter = (date_time.month - 1) // 3 * 3 + 3
    return dateutils.last_date_of_month(
        date_time.replace(month=last_month_of_quarter, day=1)
        + relativedelta(months=1, days=-1)
    )


def to_dmy_str(date):
    return date.strftime("%d-%m-%Y")
