from ar_module.integration_tests.utilities import excel_utils
from ar_module.integration_tests.utilities.common_utils import sanitize_blank


class CreateDebtorBuilder:
    def __init__(self, sheet_name, test_case_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = DebtorData(test_data).__dict__


class DebtorData:
    def __init__(self, test_data):
        self.debtor_code = sanitize_blank(test_data["debtor_code"])
        self.debtor_name = sanitize_blank(test_data["debtor_name"])
        self.hotel_id = sanitize_blank(test_data["hotel_id"])
        if sanitize_blank(test_data["debtor_type"]):
            self.debtor_type = sanitize_blank(test_data["debtor_type"])
