from ar_module.application.consumers.constants import DebtorTypes
from ar_module.domain.entities.debtor import Debtor
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.debtor_model import DebtorModel


class DebtorAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Debtor, **kwargs):
        # noinspection PyArgumentList
        return DebtorModel(
            debtor_id=domain_entity.debtor_id,
            debtor_code=domain_entity.debtor_code,
            debtor_name=domain_entity.debtor_name,
            user_profile_id=domain_entity.user_profile_id,
            debtor_type=domain_entity.debtor_type,
            credit_period=domain_entity.credit_period,
            credit_limit=domain_entity.credit_limit,
            btc_enabled=domain_entity.btc_enabled,
            hotel_id=domain_entity.hotel_id,
            profile_status=domain_entity.profile_status,
            pocs=domain_entity.pocs,
            registered_address=domain_entity.registered_address,
        )

    def to_domain_entity(self, db_entity: DebtorModel, **kwargs):
        return Debtor(
            debtor_id=db_entity.debtor_id,
            debtor_code=db_entity.debtor_code,
            debtor_name=db_entity.debtor_name,
            user_profile_id=db_entity.user_profile_id,
            debtor_type=db_entity.debtor_type
            if db_entity.debtor_type
            else DebtorTypes.B2B,
            credit_period=db_entity.credit_period,
            credit_limit=db_entity.credit_limit,
            btc_enabled=db_entity.btc_enabled,
            hotel_id=db_entity.hotel_id,
            profile_status=db_entity.profile_status,
            pocs=db_entity.pocs,
            registered_address=db_entity.registered_address,
        )
