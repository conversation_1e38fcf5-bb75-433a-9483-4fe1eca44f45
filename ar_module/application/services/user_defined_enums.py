from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[CatalogServiceClient])
class UserDefinedEnums(object):
    def __init__(self, catalog_service_client: CatalogServiceClient):
        self.catalog_service_client = catalog_service_client

    def get_enum(self, enum_names):
        enums = self.catalog_service_client.get_enums(enum_names=enum_names)
        enum_list = enums[0].get("enum_values")
        return [enum.get("value") for enum in enum_list]
