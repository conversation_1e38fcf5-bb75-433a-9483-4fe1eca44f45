import json

from ar_module.integration_tests.builders import create_debit_builder
from ar_module.integration_tests.config.common_config import SUCCESS_CODES
from ar_module.integration_tests.config.request_uris import (
    CREATE_DEBIT_URI,
    GET_DEBIT_URI,
)
from ar_module.integration_tests.config.sheet_names import CREATE_DEBIT_SHEET_NAME
from ar_module.integration_tests.mockers import (
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
)
from ar_module.integration_tests.requests.base_request import BaseRequest
from ar_module.integration_tests.utilities.common_utils import del_none


class DebitRequest(BaseRequest):
    def create_new_debit_request(
        self,
        client,
        test_case_id,
        debtor_id,
        status_code,
        user_type,
        hotel_id,
        config_value=False,
    ):
        request_json = json.dumps(
            del_none(
                create_debit_builder.CreateDebitBuilder(
                    CREATE_DEBIT_SHEET_NAME, test_case_id, debtor_id
                ).__dict__
            )
        )
        with mock_get_tenant_configs(
            config_value
        ), mock_is_hotel_level_accounts_receivable_configured(config_value):
            with mock_get_roles():
                response = self.request_processor(
                    client,
                    "POST",
                    CREATE_DEBIT_URI,
                    status_code,
                    request_json,
                    user_type=user_type,
                    hotel_id=hotel_id,
                )
        if status_code in SUCCESS_CODES:
            self.debit_id.append(response["data"]["debit"]["debit_id"])
            self.debit_reference_numbers.append(
                response["data"]["debit"]["reference_number"]
            )
        return response

    def get_debit_request(self, client, status_code, user_type, config_value=False):
        with mock_is_hotel_level_accounts_receivable_configured(config_value):
            response = self.request_processor(
                client, "GET", GET_DEBIT_URI, status_code, user_type=user_type
            )
        return response
