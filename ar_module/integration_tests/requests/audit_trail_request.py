from ar_module.integration_tests.config.request_uris import AUDIT_TRAIL_URI
from ar_module.integration_tests.mockers import (
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
)
from ar_module.integration_tests.requests.base_request import BaseRequest


class AuditTrailRequest(BaseRequest):
    def get_audit_trail(self, client, status_code, user_type, config_value=False):
        uri = AUDIT_TRAIL_URI
        with mock_is_hotel_level_accounts_receivable_configured(config_value):
            response = self.request_processor(
                client, "GET", uri, status_code, user_type=user_type
            )
        return response
