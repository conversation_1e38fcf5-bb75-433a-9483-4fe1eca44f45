import logging

import pytz
from ths_common.constants.billing_constants import (
    CashierPaymentStatus,
    CashierPaymentTypes,
)
from thsc.crs.entities.cashiering import (
    CashierPayment,
    CashierSession,
    CashierSessionSearchQuery,
    CashRegister,
    CashRegisterSearchQuery,
)
from treebo_commons.request_tracing.context import get_current_request_id
from treebo_commons.utils import dateutils

from ar_module.infrastructure.external_clients.exceptions import (
    MissingCashierSessionError,
    MissingCashRegisterError,
    RecordPaymentToCashierError,
)
from ar_module.request_parsers import get_current_user, get_current_user_auth_id
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CashierClient(object):
    @staticmethod
    def setup_thsc_context():
        from thsc.crs import context

        context.application = "AR Module"
        context.request_id = get_current_request_id()
        context.user = get_current_user()
        context.auth_id = get_current_user_auth_id()

    @classmethod
    def add_payment_to_cashier(cls, hotel_id, credit):
        cls.setup_thsc_context()

        cash_registers = CashRegister.search(
            CashRegisterSearchQuery(vendor_id=hotel_id)
        ).cash_registers
        if not cash_registers:
            raise MissingCashRegisterError(
                message="Cash Register not found for hotel",
                extra_payload=dict(hotel_id=hotel_id),
            )

        # For now there is only 1 cash register per vendor
        cash_register = cash_registers[0]

        cashier_session_query = CashierSessionSearchQuery.create_instance(
            sort_by="-created_datetime", limit=1, vendor_id=hotel_id
        )
        cashier_sessions = CashierSession.search(
            cash_register.cash_register_id, cashier_session_query
        ).cashier_sessions
        if not cashier_sessions:
            raise MissingCashierSessionError(
                message="No open cashier session found for cash register against hotel",
                extra_payload=dict(hotel_id=hotel_id),
            )

        cashier_session = cashier_sessions[0].cashier_session
        try:
            cashier_session.add_cashier_payment(
                CashierPayment.create_instance(
                    date_of_payment=dateutils.datetime_at_given_time(
                        credit.date,
                        dateutils.current_time(timezone=pytz.UTC),
                        timezone=pytz.UTC,
                    ),
                    payment_mode=credit.mode_of_credit,
                    payment_type=CashierPaymentTypes.INFLOW,
                    payment_details=dict(
                        payment_reference_number=credit.reference_number
                    ),
                    status=CashierPaymentStatus.DONE,
                    paid_to="AR Module",
                    comment="Payment added from AR Module",
                    amount=credit.amount_in_base_currency,
                    amount_in_payment_currency=credit.amount_in_credit_currency,
                    payment_mode_sub_type="",
                )
            )
        except Exception as exc:
            logger.exception(
                "Exception occurred while adding payment to current cashier session"
            )
            raise RecordPaymentToCashierError(
                message="Unable to add payment to cashier session",
                extra_payload=dict(exception=str(exc)),
            )
