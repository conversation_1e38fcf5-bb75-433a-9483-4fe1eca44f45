import logging

from ths_common.value_objects import UserData
from treebo_commons.request_tracing.context import request_context

from ar_module.application.dtos.credit_dto import CreditReversalCancellationDto
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.credit_service import CreditService
from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from ar_module.async_job.handlers.bulk_data_ingestors.credit_reversals.credit_reversal_schema import (
    CreditReversalCancellationUploadSchema,
)
from ar_module.async_job.handlers.bulk_data_ingestors.csv_processor import (
    BaseCsvProcessor,
)
from ar_module.domain.constants import UserType
from ar_module.infrastructure.external_clients.notification_service_client import (
    EmailConfigDto,
    NotificationServiceClient,
)
from object_registry import register_instance

CREDIT_REVERSAL_UPLOAD_RESULT_S3_FOLDER_NAME = (
    "ar-credit-reversal-cancellation-upload-results/"
)
PROCESS_NAME = "Bulk Credit Reversal Cancellation"

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditService,
        NotificationServiceClient,
        TenantSettings,
    ]
)
class CreditReversalCancellationUploadHandler(BaseCsvProcessor, BaseJobHandler):
    def __init__(
        self,
        credit_service: CreditService,
        notification_service_client: NotificationServiceClient,
        tenant_settings: TenantSettings,
    ):
        BaseCsvProcessor.__init__(
            self,
            notification_service_client,
            CreditReversalCancellationUploadSchema,
            CREDIT_REVERSAL_UPLOAD_RESULT_S3_FOLDER_NAME,
            PROCESS_NAME,
        )
        self.credit_service = credit_service
        self.tenant_settings = tenant_settings

    def execute_job(self, job_id, job_data):
        file_url = job_data.get("file_url")
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        user_data = job_data.get("user_data", {})
        user_data = UserData(
            user_type=user_data.get("user_type") or UserType.BACKEND_SYSTEM,
            user=user_data.get("user") or UserType.BACKEND_SYSTEM,
            hotel_id=user_data.get("hotel_id"),
            user_auth_id=user_data.get("user_auth_id"),
        )
        if is_hotel_level_ar_configured:
            request_context.hotel_id = user_data.hotel_id
        base_currency = self.tenant_settings.get_chain_base_currency()
        self.process_file(
            file_url,
            is_hotel_level_ar_configured=is_hotel_level_ar_configured,
            user_data=user_data,
            ack_email_config=self.get_email_config_for_ack(job_data),
            base_currency=base_currency,
        )
        request_context.hotel_id = None

    @staticmethod
    def get_email_config_for_ack(job_data):
        if not job_data.get("ack_email"):
            return None
        return EmailConfigDto(
            subject=f"{PROCESS_NAME} - Status Report Attached",
            receivers=[job_data.get("ack_email")],
        )

    def ingest_data(
        self,
        credit_reversal_cancellation_dto: CreditReversalCancellationDto,
        ack_record,
        is_hotel_level_ar_configured=False,
        user_data=None,
        **kwargs,
    ):
        try:
            self.credit_service.cancel_credit_reversal(
                credit_reversal_cancellation_dto.credit_id,
                user_data,
            )
            return None, None
        except Exception as e:
            logger.exception(e)
            return None, str(e)
