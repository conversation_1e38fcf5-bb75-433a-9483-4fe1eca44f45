from flask import request
from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas.validators import validate_date_range
from ar_module.application.dtos.search_report_dto import (
    DateRangeDto,
    SearchCollectionReportDto,
    SearchLedgerReportDto,
    SearchMultipleDateRangeCollectionReportSchema,
    SearchReceivableReportDto,
)
from ar_module.application.services.reporting.constants import (
    DebtorSummaryReports,
    FinanceReports,
)
from ar_module.application.services.reporting.dtos import (
    DebtorSummaryRequestData,
    DebtorSummaryRequestDto,
)


class SearchReportRequestSchema(Schema):
    start_date = fields.Date(required=True)
    end_date = fields.Date(required=True)
    level = fields.String(required=False)
    hotel_id = fields.String(required=False)

    @validates_schema
    def validate_hotel_id(self, data):
        if (
            request.headers.get("X-Hotel-Id")
            and data.get("hotel_id")
            and request.headers["X-Hotel-Id"] != data["hotel_id"]
        ):
            raise ValidationError("Hotel id is different than the one sent in headers")


class SearchReceivableReportRequestSchema(SearchReportRequestSchema):
    debtor_code = fields.String()
    debtor_type = fields.String()
    reference_date = fields.Date()
    min_balance = MoneyField()
    max_balance = MoneyField()
    min_oldest_due_age = fields.Integer()
    max_oldest_due_age = fields.Integer()

    @post_load
    def create_object(self, data):
        return SearchReceivableReportDto(
            start_date=data.get("start_date"),
            end_date=data.get("end_date"),
            include_detail=data.get("level") == "detailed",
            hotel_id=data.get("hotel_id"),
            debtor_code=data.get("debtor_code"),
            min_balance=data.get("min_balance"),
            max_balance=data.get("max_balance"),
            min_oldest_due_age=data.get("min_oldest_due_age"),
            max_oldest_due_age=data.get("max_oldest_due_age"),
            reference_date=data.get("reference_date"),
            debtor_type=data.get("debtor_type"),
        )


class DateRangeSchema(Schema):
    start_date = fields.Date(required=True)
    end_date = fields.Date(required=True)


class MultipleDateRangeSearchReportRequestSchema(Schema):
    hotel_id = fields.String(required=False)
    date_ranges = fields.Nested(DateRangeSchema, many=True)

    @post_load
    def create_object(self, data):
        date_ranges = []
        if data.get("date_ranges"):
            for date in data.get("date_ranges"):
                validate_date_range(
                    start_date=date.get("start_date"), end_date=date.get("end_date")
                )
                date_ranges.append(
                    DateRangeDto(
                        start_date=date.get("start_date"), end_date=date.get("end_date")
                    )
                )
        return SearchMultipleDateRangeCollectionReportSchema(
            hotel_id=data.get("hotel_id"), date_ranges=date_ranges
        )

    @validates_schema
    def validate_hotel_id(self, data):
        if (
            request.headers.get("X-Hotel-Id")
            and data.get("hotel_id")
            and request.headers["X-Hotel-Id"] != data["hotel_id"]
        ):
            raise ValidationError("Hotel id is different than the one sent in headers")


class SearchCollectionReportRequestSchema(SearchReportRequestSchema):
    debtor_code = fields.String()
    debtor_type = fields.String()
    min_paid_amount = MoneyField()
    max_paid_amount = MoneyField()

    @post_load
    def create_object(self, data):
        return SearchCollectionReportDto(
            start_date=data.get("start_date"),
            end_date=data.get("end_date"),
            include_detail=data.get("level") == "detailed",
            hotel_id=data.get("hotel_id"),
            debtor_code=data.get("debtor_code"),
            min_paid_amount=data.get("min_paid_amount"),
            max_paid_amount=data.get("max_paid_amount"),
            debtor_type=data.get("debtor_type"),
        )


class SearchLedgerReportRequestSchema(SearchReportRequestSchema):
    debtor_code = fields.String()
    debtor_type = fields.String()
    min_amount = MoneyField()
    max_amount = MoneyField()
    entry_types = fields.String()
    settlement_status = fields.String()
    settlement_date = fields.Date()
    generate_download_urls = fields.Boolean()
    limit = fields.Integer()
    offset = fields.Integer()
    debtor_id = fields.String()

    @post_load
    def create_object(self, data):
        entry_types = None
        if data.get("entry_types"):
            entry_types = [
                entry_type.strip().lower()
                for entry_type in data.get("entry_types").split(",")
            ]

        settlement_status = []
        if data.get("settlement_status"):
            settlement_status = [
                settlement_status.strip().lower()
                for settlement_status in data.get("settlement_status").split(",")
            ]

        return SearchLedgerReportDto(
            start_date=data.get("start_date"),
            end_date=data.get("end_date"),
            include_detail=data.get("level") == "detailed",
            hotel_id=data.get("hotel_id"),
            debtor_code=data.get("debtor_code"),
            min_amount=data.get("min_amount"),
            max_amount=data.get("max_amount"),
            settlement_date=data.get("settlement_date"),
            settlement_status=settlement_status,
            entry_types=entry_types,
            debtor_type=data.get("debtor_type"),
            generate_download_urls=data["generate_download_urls"]
            if data.get("generate_download_urls") is not None
            else True,
            limit=data.get("limit"),
            offset=data.get("offset"),
            debtor_id=data.get("debtor_id"),
        )


class DebtorSummaryReportData(Schema):

    debtor_id = fields.String(required=True)
    from_date = fields.Date()
    to_date = fields.Date()
    include_mapped_records = fields.Boolean(default=False)
    additional_to_recipients = fields.String()
    additional_cc_recipients = fields.String()


class DebtorSummaryReportRequestSchema(Schema):

    max_date_range_for_report = {
        DebtorSummaryReports.MIS_REPORT: 366,
        DebtorSummaryReports.PAYMENT_ADVICE_REPORT: 366,
    }

    report_name = fields.String(
        required=True,
        validate=[
            OneOf(
                [
                    DebtorSummaryReports.MIS_REPORT,
                    DebtorSummaryReports.OVERDUE_REPORT,
                    DebtorSummaryReports.PAYMENT_ADVICE_REPORT,
                    DebtorSummaryReports.OUTSTANDING_DUE_REPORT,
                ]
            )
        ],
    )
    data = fields.Nested(DebtorSummaryReportData(), required=True, allow_none=False)

    @validates_schema
    def validate_data(self, payload):
        data = payload.get("data")
        report_name = payload.get("report_name")

        if (
            report_name == DebtorSummaryReports.OVERDUE_REPORT
            or report_name == DebtorSummaryReports.OUTSTANDING_DUE_REPORT
        ):
            return data

        from_date = data.get("from_date")
        to_date = data.get("to_date")

        if not (from_date and to_date):
            raise ValidationError(
                f"from_date and to_date are mandatory for {report_name}"
            )

        max_date_range = self.max_date_range_for_report[report_name]

        if to_date < from_date:
            raise ValidationError("To date can't be less than from date")

        if (to_date - from_date).days > max_date_range:
            raise ValidationError(
                f"Report can only be generated for maximum of {max_date_range} days"
            )

        return data

    @post_load
    def create_object(self, input_data):
        return DebtorSummaryRequestDto(
            report_name=input_data["report_name"],
            report_request_data=DebtorSummaryRequestData(**input_data["data"]),
        )


class PushPaymentReportSchema(Schema):
    date = fields.Date()


class ResourceDataSchema(Schema):
    resource_id = fields.String(required=True)
    resource_unique_id = fields.String(required=True)


class PullPaymentDataSchema(Schema):
    report_name = fields.String(required=True)
    resource_data = fields.Nested(ResourceDataSchema, many=True)

    @validates_schema
    def validate_data(self, data):
        if data.get("report_name"):
            if data["report_name"] != FinanceReports.AR_PAYMENT_REPORT:
                raise ValidationError("Not a valid report name")
