from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from ar_module.application.dtos.credit_dto import CreditReversalDto
from ar_module.common.id_generator_utils import random_id_generator
from ar_module.domain.aggregates.credit_aggregate import CreditAggregate
from ar_module.domain.entities.credit import Credit


class CreditReversalFactory:
    @staticmethod
    def create_new_credit_reversal(credit_reversal_dto: CreditReversalDto):
        credit_id = random_id_generator(prefix="CDT")
        credit = Credit(
            credit_id=credit_id,
            debtor_id=credit_reversal_dto.debtor_id,
            credit_type=credit_reversal_dto.credit_type,
            date=dateutils.to_date(credit_reversal_dto.date),
            amount_in_base_currency=credit_reversal_dto.amount_in_base_currency,
            reference_number=credit_reversal_dto.reference_number,
            reference_id=credit_reversal_dto.reference_id,
            mode_of_credit=credit_reversal_dto.mode_of_credit,
            amount_in_credit_currency=credit_reversal_dto.amount_in_credit_currency,
            used_to_auto_settle_debit=credit_reversal_dto.used_to_auto_settle_debit,
            approval_document=credit_reversal_dto.approval_document,
            unused_credit_amount=Money(
                0, credit_reversal_dto.amount_in_base_currency.currency
            ),
            recorded_via=credit_reversal_dto.recorded_via,
            tenant_id=credit_reversal_dto.tenant_id,
        )
        credit_aggregate = CreditAggregate(credit=credit)
        credit_aggregate.add_credit_reversals(credit_reversal_dto.credit_reversals)
        return credit_aggregate
