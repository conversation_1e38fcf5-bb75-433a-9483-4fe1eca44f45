from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas.request.debtor import DebtorRequestSchema, DebtorSearchSchema
from ar_module.api.schemas.response.debtor import (
    DebtorResponseSchema,
    DebtorSearchResponseSchema,
)
from ar_module.application.services.debtor_service import DebtorService
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/debtors", methods=["GET"])
@schema_wrapper_parser(DebtorSearchSchema, param_type=RequestTypes.ARGS)
@inject(debtor_service=DebtorService)
def search_debtors(debtor_service, parsed_request):
    """
    Fetch list of Debtors for a given hotel, or debtor_code or debtor_name
    ---
    operationId: search_debtors
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: false
              schema: DebtorSearchSchema
        description: Fetch list of debtors for a given hotel, or debtor_code or debtor_name.

            If AR Module is configured to run at tenant level, then hotel_id parameter is ignored, since debtor_code
            is unique across all hotels.
        tags:
            - Debtor
        responses:
            200:
                description: List of existing Debtors
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/DebtorSearchResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    only_fields = parsed_request.get("only_fields")
    get_total_count = (
        True
        if parsed_request.get("limit") is not None
        and parsed_request.get("offset") is not None
        else False
    )
    if parsed_request.get("debtor_ids"):
        parsed_request["debtor_ids"] = parsed_request.get("debtor_ids").split(",")
    debtor_search_query = DebtorSearchQuery(**parsed_request)

    debtor_aggregates, debtors_count = debtor_service.get_debtors(
        debtor_search_query,
        user_data=user_data,
        get_total_count=get_total_count,
    )

    debtor_response_schema = DebtorSearchResponseSchema()
    response = debtor_response_schema.dump(
        dict(
            debtors=[aggregate.debtor for aggregate in debtor_aggregates],
            limit=parsed_request.get("limit"),
            offset=parsed_request.get("offset"),
            total=debtors_count,
        ),
        only_fields=only_fields,
    )

    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/debtors", methods=["POST"])
@schema_wrapper_parser(DebtorRequestSchema)
@inject(debtor_service=DebtorService)
def create_new_debtor(debtor_service, parsed_request):
    """
    Create new Debtor for given debtor_code, debtor_name and hotel_id.
    ---
    operationId: create_debtor
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create new `Debtor` for given `debtor_code`, `debtor_name` and `hotel_id`.

            If AR Module is configured to run at tenant level, then `hotel_id` field is ignored, and corresponding
            value for `hotel_id` in returned `Debtor` entity will be null
        tags:
            - Debtor
        parameters:
            - in: body
              name: body
              description: The `Debtor` object which needs to be created
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/DebtorRequestSchema"
        responses:
            200:
                description: Newly created Debtor
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                debtor:
                                    $ref: "#/definitions/DebtorResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    debtor_aggregate = debtor_service.create_new_debtor(
        parsed_request, user_data=user_data
    )
    debtor_response_schema = DebtorResponseSchema()
    response = debtor_response_schema.dump(debtor_aggregate.debtor)
    return ApiResponse.build(data=dict(debtor=response.data), status_code=200)
