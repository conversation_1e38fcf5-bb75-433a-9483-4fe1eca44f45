import json

import pytest
from treebo_commons.utils import dateutils

from ar_module.itests.payload_generators.credit_payload_generators import (
    create_new_credit_payload,
)
from ar_module.itests.payload_generators.settlement_payload_generator import (
    create_new_settlement_payload,
)


@pytest.fixture
def create_credit_payload():
    date_of_credit = dateutils.current_datetime()
    payload = create_new_credit_payload(
        date=date_of_credit,
    )

    return json.dumps(payload)


@pytest.fixture
def create_credit_payload_with_settlements():
    date_of_credit = dateutils.current_datetime()
    settlements = create_new_settlement_payload()
    payload = create_new_credit_payload(date=date_of_credit, settlements=settlements)

    return json.dumps(payload)
