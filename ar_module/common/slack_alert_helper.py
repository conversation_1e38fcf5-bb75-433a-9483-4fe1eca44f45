import json
import logging
import os

import requests
from flask import current_app

logger = logging.getLogger(__name__)


class SlackAlert:
    @staticmethod
    def send_alert(text, tenant_id=None, slack_webhook_url=None, *args, **kwargs):
        slack_url = (
            slack_webhook_url
            if slack_webhook_url
            else current_app.config["SLACK_WEBHOOK_URL"]
        )
        try:
            app_env = os.environ.get("APP_ENV", "local")
            if app_env not in ["production", "prod"]:
                return

            final_text = text
            payload = {
                "text": "`(Tenant: {0}) AR Module ({1})`: ```{2}```".format(
                    tenant_id, app_env.upper(), final_text
                ),
                "username": "AR Module",
            }
            json_string = json.dumps(payload, default=lambda o: o.__dict__)
            headers = {"content-type": "application/json"}
            response = requests.post(slack_url, data=json_string, headers=headers)
            if response.status_code != 200:
                raise Exception(
                    "Received response status: {status}".format(
                        status=response.status_code
                    )
                )
        except Exception as ex:
            logger.exception(
                "Error while sending slack error on channel. {ex}".format(ex=ex)
            )
