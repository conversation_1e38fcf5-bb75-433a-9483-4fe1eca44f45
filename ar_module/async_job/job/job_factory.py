import datetime

from ths_common.utils.id_generator_utils import random_id_generator

from ar_module.async_job.handlers.bulk_data_ingestors.dtos import FileUploadDto
from ar_module.async_job.job.aggregates.job_aggregate import JobAggregate
from ar_module.async_job.job.dto.job_dto import Async<PERSON><PERSON>D<PERSON>, ScheduledJobDTO
from ar_module.async_job.job.entities.job_entity import JobEntity


class JobFactory:
    @staticmethod
    def create_job(job_dto: AsyncJobDTO):
        entity = JobEntity(
            job_dto.job_name,
            job_dto.hotel_id,
            job_dto.data.serialize()
            if hasattr(job_dto.data, "serialize")
            else job_dto.data,
            None,
            job_id=random_id_generator("JOB"),
            generated_at=datetime.datetime.now(),
        )
        if isinstance(job_dto, ScheduledJobDTO):
            entity.eta = job_dto.eta
        elif isinstance(job_dto, AsyncJobDTO):
            entity.eta = datetime.datetime.now()
        else:
            raise TypeError("Unknown job type")
        return JobAggregate(entity)
