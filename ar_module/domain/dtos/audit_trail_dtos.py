from treebo_commons.utils import dateutils


class CreditAuditTrailDTO(object):
    def __init__(self, debtor_aggregate, credit_aggregate, mapped_invoices):
        self.hotel_id = debtor_aggregate.hotel_id
        self.debtor_name = debtor_aggregate.debtor_name
        self.debtor_code = debtor_aggregate.debtor_code
        self.credit_id = credit_aggregate.credit.credit_id
        self.credit_mode = credit_aggregate.credit.mode_of_credit
        self.credit_currency = (
            credit_aggregate.credit.amount_in_credit_currency.currency.value
        )
        self.credit_amount = credit_aggregate.credit.amount_in_base_currency.amount
        self.credit_date = credit_aggregate.credit.date
        self.payment_reference_number = credit_aggregate.credit.reference_number
        self.mapped_invoices = mapped_invoices

    def to_json(self):
        return dict(
            hotel_id=self.hotel_id,
            debtor_name=self.debtor_name,
            debtor_code=self.debtor_code,
            credit_id=self.credit_id,
            credit_mode=self.credit_mode,
            credit_currency=self.credit_currency,
            credit_amount=int(self.credit_amount),
            credit_date=dateutils.date_to_ymd_str(self.credit_date),
            payment_reference_number=self.payment_reference_number,
            mapped_invoices=self.mapped_invoices,
        )


class DebitAuditTrailDTO(object):
    def __init__(
        self,
        debit_aggregate,
        debtor_aggregate_billed_le,
        debtor_aggregate_booker_le=None,
        booking_id=None,
    ):
        self.debit_id = debit_aggregate.debit.debit_id
        self.hotel_id = debtor_aggregate_billed_le.hotel_id
        self.original_debtor_name = debtor_aggregate_billed_le.debtor_name
        self.original_debtor_code = debtor_aggregate_billed_le.debtor_code
        self.destination_debtor_name = (
            debtor_aggregate_booker_le.debtor_name
            if debtor_aggregate_booker_le
            else None
        )
        self.destination_debtor_code = (
            debtor_aggregate_booker_le.debtor_code
            if debtor_aggregate_booker_le
            else None
        )
        self.booking_id = booking_id
        self.reference_number = debit_aggregate.debit.reference_number
        self.pretax_amount = debit_aggregate.debit.debit_amount.pretax_amount.amount
        self.tax_amount = debit_aggregate.debit.debit_amount.tax_amount.amount
        self.debit_date = debit_aggregate.debit.debit_date

    def to_json(self):
        return dict(
            debit_id=self.debit_id,
            hotel_id=self.hotel_id,
            original_debtor_name=self.original_debtor_name,
            original_debtor_code=self.original_debtor_code,
            destination_debtor_name=self.destination_debtor_name,
            destination_debtor_code=self.destination_debtor_code,
            booking_id=self.booking_id,
            reference_number=self.reference_number,
            pretax_amount=int(self.pretax_amount),
            tax_amount=int(self.tax_amount),
            debit_date=dateutils.date_to_ymd_str(self.debit_date),
        )


class SettlementAuditTrailDTO(object):
    def __init__(
        self,
        debtor_aggregate,
        credit_aggregate,
        settlement,
        is_settlement_cancelled_due_to_refund,
    ):
        self.hotel_id = debtor_aggregate.hotel_id
        self.debtor_name = debtor_aggregate.debtor_name
        self.debtor_code = debtor_aggregate.debtor_code
        self.credit_id = credit_aggregate.credit.credit_id
        self.payment_reference_number = credit_aggregate.credit.reference_number
        self.settlement_details = settlement
        self.mode_of_credit = credit_aggregate.credit.mode_of_credit
        self.is_settlement_cancelled_due_to_refund = (
            is_settlement_cancelled_due_to_refund
        )

    def to_json(self):
        return dict(
            hotel_id=self.hotel_id,
            debtor_name=self.debtor_name,
            debtor_code=self.debtor_code,
            credit_id=self.credit_id,
            payment_reference_number=self.payment_reference_number,
            settlement_details=self.settlement_details,
            mode_of_credit=self.mode_of_credit,
            is_settlement_cancelled_due_to_refund=self.is_settlement_cancelled_due_to_refund,
        )


class DebtorCommunicationAuditTrailDTO(object):
    def __init__(self, communication_type, debtor_aggregate):
        self.hotel_id = debtor_aggregate.hotel_id
        self.debtor_name = debtor_aggregate.debtor_name
        self.debtor_code = debtor_aggregate.debtor_code
        self.communication_type = communication_type

    def to_json(self):
        return dict(
            hotel_id=self.hotel_id,
            debtor_name=self.debtor_name,
            debtor_code=self.debtor_code,
            communication_type=self.communication_type,
        )


class CreateCreditReversalAuditTrailDTO(object):
    def __init__(self, credit_aggregate, debtor_aggregate):
        self.debtor_name = debtor_aggregate.debtor_name
        self.debtor_code = debtor_aggregate.debtor_code
        self.refund_id = credit_aggregate.credit.reference_number
        self.amount = float(credit_aggregate.credit.amount_in_base_currency)
        self.payment_mode = credit_aggregate.credit.mode_of_credit
        self.refund_date = dateutils.date_to_ymd_str(credit_aggregate.credit.date)

        self.mapped_payment_reference_number = ""
        if credit_aggregate.credit.credit_reversals:
            self.mapped_payment_reference_number = ", ".join(
                credit_reversal.credit_reference_number
                for credit_reversal in credit_aggregate.credit.credit_reversals
            )

    def to_json(self):
        return dict(
            debtor_name=self.debtor_name,
            debtor_code=self.debtor_code,
            refund_id=self.refund_id,
            refund_date=self.refund_date,
            mapped_payment_reference_number=self.mapped_payment_reference_number,
            payment_mode=self.payment_mode,
            amount=self.amount,
        )


class CancelCreditReversalAuditTrailDTO(object):
    def __init__(self, credit_aggregate, debtor_aggregate):
        self.debtor_name = debtor_aggregate.debtor_name
        self.debtor_code = debtor_aggregate.debtor_code
        self.refund_id = credit_aggregate.credit.reference_number
        self.refund_date = dateutils.date_to_ymd_str(
            credit_aggregate.credit.cancellation_date
        )

    def to_json(self):
        return dict(
            debtor_name=self.debtor_name,
            debtor_code=self.debtor_code,
            refund_id=self.refund_id,
            refund_date=self.refund_date,
        )


class CreateCreditReversalMappedAuditTrailDTO(object):
    def __init__(self, credit_aggregate, debtor_aggregate):
        refund_id = credit_aggregate.credit.reference_number
        self.credit_reversal_mapped_details = [
            {
                "payment_id": credit_reversal.credit_reference_number,
                "payment_mode": credit_reversal.credit_payment_mode,
                "amount": float(credit_reversal.amount_in_credit_currency),
                "debtor_code": debtor_aggregate.debtor_code,
                "debtor_name": debtor_aggregate.debtor_name,
                "refund_id": refund_id,
            }
            for credit_reversal in credit_aggregate.credit.credit_reversals
        ]

    def to_json(self):
        return self.credit_reversal_mapped_details
