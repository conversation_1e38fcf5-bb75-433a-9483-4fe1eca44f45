#!/bin/bash

set -e
set -x

export ENV="$1"
export tenant_service_url="${6}"
export VERSION="$2"
export APP="$3"
export catalog_service_url="$4"
export TARGET="$5"
#export ENV_REPO_SERVICE_NAME="$6"
export CLUSTER_IDENTIFIER="$7"
export regions="$8"
export AWS_SECRET_PREFIX="$9"

echo "Using env : $ENV"

export HOME=/opt/$APP
export SERVICE=$APP

cd $HOME

TREEBO_TENANT_ID="treebo"
allTenants=()
arEnabledTenants=()

function loadActiveTenants {
  # Read Tenant Ids from TenantGateway
  echo "Loading active tenants"
  if [ "$ENV" == "staging" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")

  elif [ "$ENV" == "production" ]; then
      active_tenants=$(curl -X GET --header "Accept: application/json" "${tenant_service_url}/tenants/v1/tenants?status=active")
  fi

  allTenants=($(echo "$active_tenants" | jq -r '.data[].tenant_id'))
  echo $nonTreeboTenants
}

loadActiveTenants
echo "Tenants loaded: ${allTenants[@]}"

function loadArEnabledTenants {
   if [ "$ENV" == "staging" ]; then
       for tenant_id in ${allTenants[@]}; do
               tenant_configs=$(curl -X GET --header "Accept: application/json" --header "X-tenant-Id: $tenant_id" "${catalog_service_url}/cataloging-service/api/v1/tenant-configs")
               arTenant=$(echo "$tenant_configs" | jq -r '.[] | select(.config_name == "ar_module_enabled" and .config_value=="false")')
               if [ -z $arTenant ]; then
                       arEnabledTenants+=($tenant_id)
               fi
       done
   elif [ "$ENV" == "production" ]; then
       for tenant_id in ${allTenants[@]}; do
               tenant_configs=$(curl -X GET --header "Accept: application/json" --header "X-tenant-Id: $tenant_id" "${catalog_service_url}/cataloging-service/api/v1/tenant-configs")
               arTenant=$(echo "$tenant_configs" | jq -r '.[] | select(.config_name == "ar_module_enabled" and .config_value=="false")')
               if [ -z $arTenant ]; then
                       arEnabledTenants+=($tenant_id)
               fi
       done
   fi
}

loadArEnabledTenants
echo "Ar Tenants loaded: ${arEnabledTenants[@]}"

echo "Deploying $APP app on $ENV environment"

create_dir_if_doesnt_exist() {
        DIR_NAME=$1
        if [ ! -d $DIR_NAME ];
        then echo "creating directory at: $DIR_NAME";
        mkdir -pv $DIR_NAME;
        fi;
    }

if [ "$ENV" = "staging" ]; then
    export req_file=./requirements/stag.txt
    source /opt/$APP/docker/build_env/staging
    export ENV_FILE=/opt/$APP/docker/docker_env/staging.env
    create_dir_if_doesnt_exist $HOST_LOG_ROOT
    echo "Running app server container"
    docker ps | grep -i ar_ | docker stop `awk '{print $1}'`
    docker ps -a | grep -i ar_ | docker rm `awk '{print $1}'`
    docker-compose -f /opt/$APP/docker/compose/stag-compose.yml up -d

    echo "Running worker for each tenants"
    for tenant_id in ${arEnabledTenants[@]}; do
            export TENANT_ID=$tenant_id
            export HOST_PORT=$((HOST_PORT + 1))
            docker-compose -f /opt/$APP/docker/compose/worker-compose.yml -p accounts_receivable_${TENANT_ID} up -d
    done

elif [ "$ENV" = "production" ]; then
    export req_file=./requirements/prod.txt
    source /opt/$APP/docker/build_env/prod
    export ENV_FILE=/opt/$APP/docker/docker_env/prod.env
    create_dir_if_doesnt_exist $HOST_LOG_ROOT
    echo "Running app server container"
    #docker ps | grep -i ar_ | docker stop `awk '{print $1}'`
    #docker ps -a | grep -i ar_ | docker rm `awk '{print $1}'`
    if [ $TARGET = "app" ]; then
       docker-compose -f /opt/$APP/docker/compose/prod-compose.yml up -d
    elif [ $TARGET = "serialized-workers" ]; then
    echo "Running worker for each tenants"
    for tenant_id in ${arEnabledTenants[@]}; do
            export TENANT_ID=$tenant_id
            export HOST_PORT=$((HOST_PORT + 1))
            docker-compose -f /opt/$APP/docker/compose/prod-worker-compose.yml -p accounts_receivable_${TENANT_ID} up -d
    done
    fi
fi