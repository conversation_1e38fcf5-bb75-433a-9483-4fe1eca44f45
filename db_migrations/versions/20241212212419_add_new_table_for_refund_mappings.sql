-- revision: '20241212212419_add_new_table_for_refund_mappings'
-- down_revision: '20240825234853_add_more_profile_info_columns_in_debtor'

-- upgrade
   CREATE TABLE credit_reversal_mapping (
    id SERIAL PRIMARY KEY,
    refund_credit_id VARCHAR NOT NULL,
    payment_credit_id VARCHAR NOT NULL,
    amount_in_credit_currency NUMERIC(10, 2) NOT NULL,
    amount_in_base_currency NUMERIC(10, 2) NOT NULL,
    remarks VARCHAR,
    base_currency VARCHAR,
    credit_currency VARCHAR,
    status VARCHAR NOT NULL,
    cancellation_date DATE,
    deleted BOOL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    modified_at TIMESTAMP DEFAULT NOW() NOT NULL,
    CONSTRAINT fk_refund_credit FOREIGN KEY (refund_credit_id) REFERENCES credit (credit_id) ON DELETE CASCADE,
    CONSTRAINT fk_payment_credit FOREIGN KEY (payment_credit_id) REFERENCES credit (credit_id) ON DELETE CASCADE
    );
    CREATE INDEX ix_credit_reversal_mapping_cancellation_date ON credit_reversal_mapping USING btree (cancellation_date);
    CREATE INDEX ix_credit_reversal_mapping_created_at ON credit_reversal_mapping USING btree (created_at);
    CREATE INDEX ix_credit_reversal_mapping_refund_credit_id ON credit_reversal_mapping USING btree (refund_credit_id);
    CREATE INDEX ix_credit_reversal_mapping_payment_credit_id ON credit_reversal_mapping USING btree (payment_credit_id);
    CREATE INDEX ix_credit_reversal_mapping_credit_status ON credit_reversal_mapping USING btree (status);

    ALTER TABLE credit ADD COLUMN refunded_amount NUMERIC(10, 2);

-- downgrade
    DROP TABLE credit_reversal_mapping;
    DROP INDEX IF EXISTS ix_credit_reversal_mapping_cancellation_date;
    DROP INDEX IF EXISTS ix_credit_reversal_mapping_created_at;
    DROP INDEX IF EXISTS ix_credit_reversal_mapping_refund_credit_id;
    DROP INDEX IF EXISTS ix_credit_reversal_mapping_payment_credit_id;
    DROP INDEX IF EXISTS ix_credit_reversal_mapping_credit_status;

    ALTER TABLE credit DROP COLUMN refunded_amount;

