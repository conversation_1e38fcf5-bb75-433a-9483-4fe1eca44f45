import json

from ar_module.integration_tests.builders import create_credit_builder
from ar_module.integration_tests.config.common_config import SUCCESS_CODES
from ar_module.integration_tests.config.request_uris import (
    CANCEL_CREDIT_URI,
    CREATE_CREDIT_URI,
    GET_CREDITS_URI,
)
from ar_module.integration_tests.config.sheet_names import (
    CANCEL_CREDIT_SHEET_NAME,
    CREATE_CREDIT_SHEET_NAME,
)
from ar_module.integration_tests.mockers import (
    mock_applicable_roles_for_payment_mode,
    mock_catalog_service_client,
    mock_debtor_config,
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
    mock_payment_modes,
    mock_presigned_url_from_s3_url,
)
from ar_module.integration_tests.requests.base_request import BaseRequest
from ar_module.integration_tests.utilities.common_utils import del_none
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class CreditRequest(BaseRequest):
    def create_new_credit_request(
        self,
        client,
        test_case_id,
        debtor_id,
        debit_id,
        status_code,
        user_type,
        hotel_id,
        config_value=False,
    ):
        request_json = json.dumps(
            del_none(
                create_credit_builder.CreateCreditBuilder(
                    CREATE_CREDIT_SHEET_NAME, test_case_id, debtor_id, debit_id
                ).__dict__
            )
        )
        pay_mode = json.loads(request_json)["data"]["mode_of_credit"]
        with mock_get_tenant_configs(
            config_value
        ), mock_payment_modes(), mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ), mock_debtor_config():
            with mock_get_roles(), mock_applicable_roles_for_payment_mode(
                pay_mode
            ), mock_presigned_url_from_s3_url():
                response = self.request_processor(
                    client,
                    "POST",
                    CREATE_CREDIT_URI,
                    status_code,
                    request_json,
                    user_type=user_type,
                    hotel_id=hotel_id,
                )
        if status_code in SUCCESS_CODES:
            self.credit_id = response["data"]["credit"]["credit_id"]
            self.credit_reference_number = response["data"]["credit"][
                "reference_number"
            ]
            self.pay_mode = response["data"]["credit"]["mode_of_credit"]
            self.credit_ids.append(response["data"]["credit"]["credit_id"])
        return response

    def get_credits_request(
        self, client, status_code, debtor_id, hotel_id, user_type, config_value=False
    ):
        uri = GET_CREDITS_URI.format(
            debtor_id=debtor_id, should_send_approval_document_url=True
        )
        with mock_get_tenant_configs(
            config_value
        ), mock_payment_modes(), mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ), mock_get_roles(), mock_presigned_url_from_s3_url():
            response = self.request_processor(
                client, "GET", uri, status_code, user_type=user_type
            )
        return response

    def cancel_credit(
        self,
        client,
        credit_id,
        status_code,
        hotel_id,
        user_type,
        pay_mode,
        test_case_id,
        config_value=False,
    ):
        uri = CANCEL_CREDIT_URI.format(credit_id=credit_id)
        test_data = get_test_case_data(CANCEL_CREDIT_SHEET_NAME, test_case_id)[0]
        cancellation_reason = test_data["cancellation_reason"]
        request_json = json.dumps(
            {"data": {"cancellation_reason": cancellation_reason}}
        )
        with mock_get_tenant_configs(
            config_value
        ), mock_get_roles(), mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ), mock_applicable_roles_for_payment_mode(
            pay_mode
        ), mock_catalog_service_client():
            response = self.request_processor(
                client,
                "POST",
                uri,
                status_code,
                request_json,
                user_type=user_type,
            )
        return response
