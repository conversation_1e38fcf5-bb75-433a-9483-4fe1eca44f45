from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField


class TotalPaymentCreditsResponseSchema(Schema):
    amount = MoneyField()
    payment_mode = fields.String()


class CollectionReportSummaryResponseSchema(Schema):
    mtd_collections = MoneyField()
    total_payment_credits = fields.Nested(TotalPaymentCreditsResponseSchema, many=True)
    settled_debits = fields.Integer()
    debtor_count = fields.Integer()


class CollectionReportDetailedSchema(Schema):
    debtor_code = fields.String()
    debtor_name = fields.String()
    debtor_type = fields.String()
    total_payment_credits = MoneyField()
    settled_debits = fields.Integer()


class MonthWiseCollectionReportSummaryResponseSchema(Schema):
    start_date = fields.Date()
    end_date = fields.Date()
    summary = fields.Nested(CollectionReportSummaryResponseSchema)


class CollectionReportResponseSchema(Schema):
    month_wise_summary = fields.Nested(
        MonthWiseCollectionReportSummaryResponseSchema, many=True
    )
    detailed = fields.Nested(CollectionReportDetailedSchema, many=True)
    summarised_report_download_url = fields.String()
    detailed_report_download_url = fields.String()


class DebtorLedgerSummaryReportSchema(Schema):
    total_credits = MoneyField()
    total_debits = MoneyField()
    total_provisional_credits = MoneyField()
    unapplied_payment_balance = MoneyField()
    gross_receivables = MoneyField()
    net_receivables = MoneyField()


class DebtorLedgerDetailedReportSchema(Schema):
    debtor_code = fields.String()
    debtor_name = fields.String()
    debtor_type = fields.String()
    date = fields.Date()
    reference_number = fields.String()
    entry_type = fields.String()
    settlement_date = fields.Date()
    amount = MoneyField()
    debtor_net_balance = MoneyField()
    settlement_reference_number = fields.String()
    settlement_status = fields.String()
    sub_type = fields.String()


class DebtorLedgerReportResponseSchema(Schema):
    summary = fields.Nested(DebtorLedgerSummaryReportSchema)
    detailed = fields.Nested(DebtorLedgerDetailedReportSchema, many=True)
    summarised_report_download_url = fields.String()
    detailed_report_download_url = fields.String()
    limit = fields.Integer(required=False, allow_none=True)
    offset = fields.Integer(required=False, allow_none=True)
    total = fields.Integer(required=False, allow_none=True)


class AgeOfReceivablesSchema(Schema):
    age = fields.String()
    receivables = fields.Decimal(places=2)


class ReceivableSummaryResponseSchema(Schema):
    total_gross_receivables = MoneyField()
    total_net_receivables = MoneyField()
    total_unapplied_payments = MoneyField()
    debtors_count = fields.String()
    days_of_sales_os = fields.Decimal(places=2)
    age_of_receivables = fields.Nested(AgeOfReceivablesSchema, many=True)


class MonthWiseReceivableReportSummaryResponseSchema(Schema):
    start_date = fields.Date()
    end_date = fields.Date()
    summary = fields.Nested(ReceivableSummaryResponseSchema, required=True)


class ReceivableDetailResponseSchema(Schema):
    debtor_code = fields.String()
    debtor_name = fields.String()
    debtor_type = fields.String()
    total_credits = MoneyField()
    total_provisional_credits = MoneyField()
    unapplied_payment = MoneyField()
    gross_receivables = MoneyField()
    net_receivables = MoneyField()
    oldest_due = fields.String()


class ReceivableReportResponseSchema(Schema):
    month_wise_summary = fields.Nested(
        MonthWiseReceivableReportSummaryResponseSchema, many=True
    )
    detailed = fields.Nested(ReceivableDetailResponseSchema, many=True)
    summarised_report_download_url = fields.String()
    detailed_report_download_url = fields.String()


class CollectionReportCreditResponseSchema(Schema):
    payments = fields.Nested(TotalPaymentCreditsResponseSchema, many=True)


class DebtorLedgerPreview(Schema):
    total_credits = MoneyField()
    total_unsettled_debits = MoneyField()
    total_unused_credits = MoneyField()
    summary_email_recipients = fields.List(fields.Dict())
