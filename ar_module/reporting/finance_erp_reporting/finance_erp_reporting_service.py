import logging

from treebo_commons.utils import dateutils

from ar_module.common.slack_alert_helper import SlackAlert
from ar_module.core.common.globals import global_context
from ar_module.reporting.finance_erp_reporting.external_clients.finance_erp_service_client import (
    FinanceERPServiceClient,
)
from ar_module.reporting.finance_erp_reporting.finance_erp_reports_generators import (
    PaymentDataReportGenerator,
)
from ar_module.reporting.finance_erp_reporting.serializers.finance_erp_reporting import (
    PaymentsPushSchema,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        FinanceERPServiceClient,
    ]
)
class FinanceERPReportingService(object):
    def __init__(
        self,
        finance_service_client,
    ):
        self.finance_service_client = finance_service_client
        self.tenant_id = global_context.tenant_id

    def push_payment_reports(self, date):
        date = (
            dateutils.ymd_str_to_date(date)
            if date
            else dateutils.to_date(
                dateutils.subtract(dateutils.current_datetime(), days=1)
            )
        )
        payment_data_report_aggregates = []
        try:
            payment_data_report_aggregates = PaymentDataReportGenerator(date).generate()
        except Exception as e:
            msg = f"failed to push payments to finance erp for date: {date} due to exception: {str(e)}"
            logger.exception(msg)
            SlackAlert.send_alert(msg, tenant_id=self.tenant_id)
            raise e
        return self.finance_service_client.push_payments_data(
            payment_data_report_aggregates
        )

    @staticmethod
    def pull_payment_report(request_data):
        reference_numbers = {
            res_data["resource_id"] for res_data in request_data["resource_data"]
        }
        data_unique_ids = {
            res_data["resource_unique_id"] for res_data in request_data["resource_data"]
        }
        payment_data_report_aggregates = PaymentDataReportGenerator(
            reference_numbers=reference_numbers
        ).generate()
        payment_data_report_aggregates = [
            aggregate
            for aggregate in payment_data_report_aggregates
            if aggregate.uu_id in data_unique_ids
        ]
        data_to_send = (
            PaymentsPushSchema().dump(payment_data_report_aggregates, many=True).data
        )
        return data_to_send
