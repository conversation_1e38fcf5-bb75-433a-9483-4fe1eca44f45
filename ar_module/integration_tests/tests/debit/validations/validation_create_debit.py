from ar_module.integration_tests.config.sheet_names import CREATE_DEBIT_SHEET_NAME
from ar_module.integration_tests.utilities.common_utils import (
    assert_,
    sanitize_test_data,
)
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationCreateDebit:
    def __init__(self, test_case_id, response):
        self.test_data = get_test_case_data(CREATE_DEBIT_SHEET_NAME, test_case_id)[0]
        self.response = response

    def validate_response(self):
        assert_(
            sanitize_test_data(
                self.response["data"]["debit"]["debit_amount"]["posttax_amount"]
            ),
            sanitize_test_data(self.test_data["expected_posttax_amount"]),
        )
        assert_(
            sanitize_test_data(
                self.response["data"]["debit"]["debit_amount"]["pretax_amount"]
            ),
            sanitize_test_data(self.test_data["expected_pretax_amount"]),
        )
        assert_(
            sanitize_test_data(
                self.response["data"]["debit"]["debit_amount"]["tax_amount"]
            ),
            sanitize_test_data(self.test_data["expected_tax_amount"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["debit"]["reference_number"]),
            sanitize_test_data(self.test_data["reference_number"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["debit"]["settlement_status"]),
            sanitize_test_data(self.test_data["settlement_status"]),
        )
        assert_(
            sanitize_test_data(self.response["data"]["debit"]["unsettled_amount"]),
            sanitize_test_data(self.test_data["unsettled_amount"]),
        )
