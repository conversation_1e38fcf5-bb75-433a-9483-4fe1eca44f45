-- revision: '20231115195346_add_more_columns_to_debtor'
-- down_revision: '20220601101906_add_debtor_type_and_ups_id_in_debtor'

-- upgrade
alter table debtor add column credit_limit numeric(15,4);
alter table debtor add column credit_period numeric(15,4);
alter table debtor add column btc_enabled boolean DEFAULT false;

CREATE TABLE "user" (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted boolean,
    user_id varchar PRIMARY KEY,
    email varchar,
    first_name varchar,
    last_name varchar,
    phone_number varchar
);

CREATE TABLE user_debtors (
    id SERIAL PRIMARY KEY,
    user_id varchar,
    debtor_id varchar
);

ALTER TABLE user_debtors ADD CONSTRAINT fk_user_debtor_debtor FOREIGN KEY (debtor_id) REFERENCES debtor(debtor_id);
ALTER TABLE user_debtors ADD CONSTRAINT fk_user_debtor_user FOREIGN KEY (user_id) REFERENCES "user"(user_id);

-- downgrade
ALTER TABLE debtor DROP COLUMN credit_limit;
ALTER TABLE debtor DROP COLUMN credit_period;
ALTER TABLE debtor DROP COLUMN btc_enabled;
ALTER TABLE user_debtor DROP CONSTRAINT fk_user_debtor_debtor;
ALTER TABLE user_debtor DROP CONSTRAINT fk_user_debtor_user;
DROP TABLE "user";
DROP TABLE user_debtor;
