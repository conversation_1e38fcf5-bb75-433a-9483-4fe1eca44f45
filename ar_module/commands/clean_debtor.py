import logging

import click
from flask.cli import with_appcontext
from sqlalchemy.orm import aliased
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.credit_model import CreditModel
from ar_module.infrastructure.database.models.debit_model import DebitModel
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from ar_module.infrastructure.database.models.settlement_model import SettlementModel
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("clean_debtor")
@click.option("--debtor_codes", help="comma separated debtor code (SH Codes)")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject()
def clean_debtor(debtor_codes, tenant_id=TenantClient.get_default_tenant()):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    debtor_codes = debtor_codes.split(",")
    failed = []
    for debtor_code in debtor_codes:
        try:
            _clean_data(debtor_code)
            click.echo(f"Successfully cleaned for debtor {debtor_code}")
        except Exception as e:
            logger.exception(e)
            failed.append(debtor_code)
            click.echo(f"Failed to clean debtor {debtor_code}")
    click.echo(f"Failed for debtors {failed}")


@unit_of_work
def _clean_data(debtor_code):
    session = BaseRepository.session()

    debit_alias = aliased(DebitModel)
    credit_alias = aliased(CreditModel)
    debtor_alias = aliased(DebtorModel)

    query = (
        session.query(debit_alias.debit_id)
        .join(debtor_alias, debtor_alias.debtor_id == debit_alias.debtor_id)
        .filter(debtor_alias.debtor_code == debtor_code)
    )

    debit_ids = [result.debit_id for result in query.all()]

    query = (
        session.query(credit_alias.credit_id)
        .join(debtor_alias, debtor_alias.debtor_id == credit_alias.debtor_id)
        .filter(debtor_alias.debtor_code == debtor_code)
    )

    credit_ids = [result.credit_id for result in query.all()]

    if debit_ids:
        session.query(DebitModel).filter(DebitModel.debit_id.in_(debit_ids)).delete(
            synchronize_session=False
        )
        session.expire_all()
        session.query(SettlementModel).filter(
            SettlementModel.debit_id.in_(debit_ids)
        ).delete(synchronize_session=False)
        session.expire_all()
    if credit_ids:
        session.query(CreditModel).filter(CreditModel.credit_id.in_(credit_ids)).delete(
            synchronize_session=False
        )
        session.expire_all()
        session.query(SettlementModel).filter(
            SettlementModel.credit_id.in_(credit_ids)
        ).delete(synchronize_session=False)
        session.expire_all()
