import enum
import os

from treebo_commons.service_discovery.service_registry import ServiceRegistry


class ServiceEndPointNames(enum.Enum):
    CATALOG_SERVICE_URL = "catalog_service_url"
    NOTIFICATION_SERVICE_URL = "notification_service_url"
    ROLE_MANAGER_SERVICE_URL = "role_manager_service_url"
    COMPANY_PROFILE_SERVICE_URL = "company_profile_service_url"
    AUTHN_SERVICE_URL = "authn_service_url"
    COMMUNICATION_SERVICE_URL = "communication_service_url"
    FINANCE_PORTAL_SERVICE_URL = "finance_portal_service_url"


class ServiceRegistryClient:

    ALL_API_ENDPOINTS = ServiceRegistry.get_all_service_endpoints()

    @classmethod
    def get_catalog_service_url(cls):
        service_name = ServiceEndPointNames.CATALOG_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_company_profile_service_url(cls):
        service_name = ServiceEndPointNames.COMPANY_PROFILE_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_notification_service_url(cls):
        service_name = ServiceEndPointNames.NOTIFICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_role_manager_service_url(cls):
        service_name = ServiceEndPointNames.ROLE_MANAGER_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_communication_service_url(cls):
        service_name = ServiceEndPointNames.COMMUNICATION_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_authn_service_url(cls):
        service_name = ServiceEndPointNames.AUTHN_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))

    @classmethod
    def get_finance_portal_service_url(cls):
        service_name = ServiceEndPointNames.FINANCE_PORTAL_SERVICE_URL.value
        return cls.ALL_API_ENDPOINTS.get(service_name, os.environ.get(service_name))
