from _decimal import Decimal
from ths_common.constants.billing_constants import PaymentReceiverTypes
from treebo_commons.utils import dateutils

from ar_module.common.utils import to_dmy_str
from ar_module.domain.constants import (
    CreditCancellationReason,
    CreditStatus,
    CreditType,
)


class PaymentReportAggregate(object):
    def __init__(
        self, payment_push_config, credit_data=None, debtor_id_to_debtor_mapping=None
    ):
        self.payment_push_config = payment_push_config
        self.credit_data = credit_data
        self.debtor_id_to_debtor_mapping = debtor_id_to_debtor_mapping

    @property
    def posting_date(self):
        if self.credit_data.mode_of_credit in self.payment_push_config["ar_pay_modes"]:
            if self._is_created_transaction():
                return to_dmy_str(self.credit_data.date)
            elif self._is_cancelled_transaction():
                cancellation_reason = self.credit_data.cancellation_reason
                if cancellation_reason == CreditCancellationReason.RECTIFICATION:
                    return to_dmy_str(self.credit_data.cancellation_date)
                elif (
                    cancellation_reason == CreditCancellationReason.MOVE_ANOTHER_DEBTOR
                ):
                    return to_dmy_str(self.credit_data.date)
                else:
                    return to_dmy_str(self.credit_data.cancellation_date)
        return None

    @property
    def payment_date(self):
        return to_dmy_str(self.credit_data.date)

    @property
    def payment_amount(self):
        payment_amount = self.credit_data.amount_in_base_currency.amount
        if self._is_created_transaction():
            if self._is_payment_transaction():
                return round(payment_amount, 2)
            if self._is_refund_transaction():
                return -round(payment_amount, 2)

        if self._is_cancelled_transaction():
            if self._is_payment_transaction():
                return -round(payment_amount, 2)
            if self._is_refund_transaction():
                return round(payment_amount, 2)

    @property
    def paymode(self):
        return self.credit_data.mode_of_credit

    @property
    def pg_transaction_id(self):
        return self.credit_data.reference_number

    @property
    def paid_by(self):
        return PaymentReceiverTypes.CORPORATE

    @property
    def paid_to(self):
        return PaymentReceiverTypes.TREEBO

    @property
    def payment_type(self):
        return "PTT"

    @property
    def payor_entity(self):
        return self._get_debtor_code()

    @property
    def athena_code(self):
        return self._get_debtor_code()

    @property
    def payor_name(self):
        return self._get_debtor_name()

    @property
    def channel(self):
        return "ar_module"

    @property
    def is_advance(self):
        return False

    @property
    def uu_id(self):
        if self._is_created_transaction():
            if self._is_payment_transaction():
                return "CTD-" + self.credit_data.credit_id
            if self._is_refund_transaction():
                return "REF-CTD-" + self.credit_data.credit_id

        if self._is_cancelled_transaction():
            if self._is_payment_transaction():
                return "CAN-" + self.credit_data.credit_id
            if self._is_refund_transaction():
                return "REF-CAN-" + self.credit_data.credit_id

    def _is_payment_transaction(self):
        return self.credit_data.credit_type == CreditType.PAYMENT

    def _is_refund_transaction(self):
        return self.credit_data.credit_type == CreditType.CREDIT_REVERSAL

    def _is_created_transaction(self):
        return self.credit_data.status == CreditStatus.CREATED

    def _is_cancelled_transaction(self):
        return self.credit_data.status == CreditStatus.CANCELLED

    def _get_debtor_code(self):
        debtor = self.debtor_id_to_debtor_mapping[self.credit_data.debtor_id]
        return debtor.debtor_code

    def _get_debtor_name(self):
        debtor = self.debtor_id_to_debtor_mapping[self.credit_data.debtor_id]
        return debtor.debtor_name
