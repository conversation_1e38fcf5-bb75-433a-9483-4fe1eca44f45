from treebo_commons.money.constants import CurrencyType
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import (
    get_current_hotel_id,
    get_current_tenant_id,
)
from treebo_commons.utils import dateutils

from ar_module.domain.constants import ARModuleConfigs
from ar_module.domain.dtos.debtor_config_dto import DebtorConfigDto
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance


@register_instance(dependencies=[CatalogServiceClient])
class TenantSettings(object):
    def __init__(self, catalog_service_client: CatalogServiceClient):
        self.catalog_service_client = catalog_service_client
        self.tenant_config_dict = dict()
        self.last_refresh_time = dict()
        # pylint: disable=not-an-iterable
        for tenant in TenantClient.get_active_tenants():
            self.tenant_config_dict[tenant.tenant_id] = dict()
            self.last_refresh_time[tenant.tenant_id] = dict()

    def _get_tenant_config(self, hotel_id=None):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        tenant_config_dict = self.tenant_config_dict.get(current_tenant_id)
        last_refresh_time_dict = self.last_refresh_time.get(current_tenant_id)

        global_config = tenant_config_dict.get("global")
        global_config_last_refresh_time = last_refresh_time_dict.get("global")

        hotel_config = (
            tenant_config_dict.get(hotel_id)
            if tenant_config_dict and hotel_id
            else None
        )
        hotel_config_last_refresh_time = (
            last_refresh_time_dict.get(hotel_id)
            if last_refresh_time_dict and hotel_id
            else None
        )

        tenant_config = hotel_config if hotel_id else global_config
        last_refresh_time = (
            hotel_config_last_refresh_time
            if hotel_id
            else global_config_last_refresh_time
        )

        if tenant_config is None or last_refresh_time < dateutils.subtract(
            dateutils.current_datetime(), minutes=5
        ):
            tenant_configs = self.catalog_service_client.get_tenant_configs(hotel_id)
            tenant_config = {config.config_name: config for config in tenant_configs}
            if hotel_id:
                self.tenant_config_dict[current_tenant_id][hotel_id] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    hotel_id
                ] = dateutils.current_datetime()
            else:
                self.tenant_config_dict[current_tenant_id]["global"] = tenant_config
                self.last_refresh_time[current_tenant_id][
                    "global"
                ] = dateutils.current_datetime()
        return tenant_config

    def get_setting_value(self, setting_name, hotel_id=None):
        tenant_config = self._get_tenant_config(hotel_id)
        if not tenant_config:
            return None
        setting = tenant_config.get(setting_name)
        if not setting:
            return None
        return setting.get_config_value()

    def is_hotel_level_accounts_receivable_configured(self):
        return self.get_setting_value(ARModuleConfigs.HOTEL_LEVEL_ACCOUNTS_RECEIVABLE)

    def get_chain_base_currency(self):
        currency = self.get_setting_value(ARModuleConfigs.CHAIN_BASE_CURRENCY)
        return CurrencyType(currency.upper()) if currency else CurrencyType.INR

    def get_debtor_configs(self):
        debtor_configs = self.get_setting_value(ARModuleConfigs.DEBTOR_CONFIG)
        return {
            config["debtor_code"]: DebtorConfigDto(**config)
            for config in debtor_configs or []
        }

    def get_payment_modes(self):
        payment_modes = self.get_setting_value(
            ARModuleConfigs.ACCEPTED_PAYMENT_METHODS, hotel_id=get_current_hotel_id()
        )
        return {mode["value"] for mode in payment_modes or []}

    def get_refund_modes(self):
        refund_modes = self.get_setting_value(
            ARModuleConfigs.ACCEPTED_REFUND_METHODS, hotel_id=get_current_hotel_id()
        )
        return {mode["value"] for mode in refund_modes or []}

    def get_applicable_roles_for_payment_mode(self, payment_mode):
        payment_modes = self.get_setting_value(
            ARModuleConfigs.ACCEPTED_PAYMENT_METHODS, hotel_id=get_current_hotel_id()
        )
        return next(
            (
                mode.get("applicable_roles", [])
                for mode in payment_modes
                if mode["value"] == payment_mode
            ),
            [],
        )
