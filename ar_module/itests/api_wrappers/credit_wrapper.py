import json

from ar_module.integration_tests.mockers import (
    mock_applicable_roles_for_payment_mode,
    mock_debtor_config,
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
    mock_payment_modes,
    mock_presigned_url_from_s3_url,
)


def create_credit(
    client_,
    create_credit_payload_with_settlements,
    debtor_id,
    debit_id,
    config_value=True,
):
    url = "/ar/v1/credits"
    _payload = dict(json.loads(create_credit_payload_with_settlements))
    _payload["debtor_id"] = debtor_id
    _payload["settlements"][0]["debit_id"] = debit_id
    pay_mode = _payload["mode_of_credit"]
    payload = json.dumps({"data": _payload})
    with mock_get_tenant_configs(
        config_value
    ), mock_payment_modes(), mock_is_hotel_level_accounts_receivable_configured(
        config_value
    ), mock_debtor_config():
        with mock_get_roles(), mock_applicable_roles_for_payment_mode(
            pay_mode
        ), mock_presigned_url_from_s3_url():
            response = client_.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
    assert response.status_code == 200
    credit_response = json.loads(response.data.decode("utf-8"))
    assert len(credit_response["data"]["credit"]) > 0

    return credit_response


def cancel_credit(client_, credit_response, cancellation_reason, config_value=True):
    url = "/ar/v1/credit/" + credit_response["data"]["credit"]["credit_id"]
    pay_mode = credit_response["data"]["credit"]["mode_of_credit"]
    payload = json.dumps({"data": {"cancellation_reason": cancellation_reason}})
    with mock_get_tenant_configs(
        config_value
    ), mock_get_roles(), mock_is_hotel_level_accounts_receivable_configured(
        config_value
    ), mock_applicable_roles_for_payment_mode(
        pay_mode
    ):
        response = client_.post(
            url,
            data=payload,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 200

    return response.status_code
