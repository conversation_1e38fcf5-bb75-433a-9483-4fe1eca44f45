import logging

from ths_common.value_objects import UserData
from treebo_commons.request_tracing.context import request_context

from ar_module.application.dtos.credit_dto import CreditDto
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.credit_service import CreditService
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.utils import (
    is_payment_mode_supported_in_csv_uploads,
    validate_credit_date,
    validate_payment_mode,
)
from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from ar_module.async_job.handlers.bulk_data_ingestors.credits.credit_schema import (
    CreditUploadSchema,
)
from ar_module.async_job.handlers.bulk_data_ingestors.csv_processor import (
    BaseCsvProcessor,
)
from ar_module.domain.constants import AuditType, UserType
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.credit_factory import CreditFactory
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.external_clients.notification_service_client import (
    EmailConfigDto,
    NotificationServiceClient,
)
from object_registry import register_instance

CREDIT_UPLOAD_RESULT_S3_FOLDER_NAME = "ar-credit-upload-results/"
PROCESS_NAME = "credit-csv-upload"

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditRepository,
        AuditTrailService,
        NotificationServiceClient,
        DebtorService,
        TenantSettings,
        CreditService,
    ]
)
class CreditUploadHandler(BaseCsvProcessor, BaseJobHandler):
    def __init__(
        self,
        credit_repo: CreditRepository,
        audit_trail_service: AuditTrailService,
        notification_service_client: NotificationServiceClient,
        debtor_service: DebtorService,
        tenant_settings: TenantSettings,
        credit_service: CreditService,
    ):
        BaseCsvProcessor.__init__(
            self,
            notification_service_client,
            CreditUploadSchema,
            CREDIT_UPLOAD_RESULT_S3_FOLDER_NAME,
            PROCESS_NAME,
        )
        self.credit_repo = credit_repo
        self.audit_trail_service = audit_trail_service
        self.debtor_service = debtor_service
        self.tenant_settings = tenant_settings
        self.credit_service = credit_service

    def execute_job(self, job_id, job_data):
        file_url = job_data.get("file_url")
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_configs = self.tenant_settings.get_debtor_configs()
        user_data = job_data.get("user_data", {})
        user_data = UserData(
            user_type=user_data.get("user_type") or UserType.BACKEND_SYSTEM,
            user=user_data.get("user") or UserType.BACKEND_SYSTEM,
            hotel_id=user_data.get("hotel_id"),
            user_auth_id=user_data.get("user_auth_id"),
        )
        if is_hotel_level_ar_configured:
            request_context.hotel_id = user_data.hotel_id
        base_currency = self.tenant_settings.get_chain_base_currency()
        self.process_file(
            file_url,
            is_hotel_level_ar_configured=is_hotel_level_ar_configured,
            user_data=user_data,
            ack_email_config=self.get_email_config_for_ack(job_data),
            debtor_configs=debtor_configs,
            base_currency=base_currency,
        )
        request_context.hotel_id = None

    @staticmethod
    def get_email_config_for_ack(job_data):
        if not job_data.get("ack_email"):
            return None
        return EmailConfigDto(
            subject=f"{PROCESS_NAME} results",
            receivers=[job_data.get("ack_email")],
        )

    def ingest_data(
        self,
        credit_dto: CreditDto,
        ack_record,
        is_hotel_level_ar_configured=False,
        user_data=None,
        debtor_configs=None,
        **kwargs,
    ):
        try:
            query = DebtorSearchQuery(
                debtor_code=credit_dto.debtor_code,
                hotel_id=user_data.hotel_id,
            )
            debtor_aggregates, _ = self.debtor_service.get_debtors(
                query,
                user_data=user_data,
                hotel_level_accounts_receivable=is_hotel_level_ar_configured,
            )
            if not debtor_aggregates:
                raise Exception(
                    f"Don't have access to Debtor {credit_dto.debtor_code} Hotel: {user_data.hotel_id}"
                )

            is_payment_mode_supported_in_csv_uploads(credit_dto.mode_of_credit)
            validate_credit_date(credit_dto.date)
            debtor_aggregate = debtor_aggregates[0]
            credit_dto.debtor_id = debtor_aggregate.debtor_id

            validate_payment_mode(
                credit_dto.mode_of_credit,
                debtor_aggregate.debtor_code,
                debtor_configs,
                user_data,
            )
            credit_aggregate = CreditFactory.create_new_credit(credit_dto=credit_dto)
            self.credit_service.record_auto_debit_to_corresponding_payment_mode_debtor_if_needed(
                credit_aggregate,
                debtor_configs,
            )
            self.credit_repo.save(credit_aggregate)
            self.audit_trail_service.create_credit_audit_trail(
                debtor_aggregate,
                credit_aggregate,
                debtor_aggregate.hotel_id,
                AuditType.CREDIT_CREATED,
                user_data=user_data,
            )
            return credit_aggregate, None
        except Exception as e:
            logger.exception(e)
            return None, str(e)
