import json

from ar_module.integration_tests.utilities.common_utils import return_date

hotel_level_accounts_receivable_true_response = {
    "config_name": "ar_module.hotel_level_accounts_receivable",
    "config_value": "true",
    "value_type": "boolean",
}

property_response = {"current_business_date": str(return_date(0))}

config_response = {
    "ar_module.accepted_payment_methods": {
        "config_value": json.dumps(
            [
                {"label": "Bank Transfer", "value": "bank_transfer"},
                {"label": "RazorPay", "value": "razorpay_api"},
                {"label": "TDS", "value": "tds"},
                {
                    "label": "Write Off",
                    "value": "write_off",
                    "applicable_roles": "ar-manager",
                },
            ]
        )
    }
}

accepted_payment_methods_config_value = [
    {"label": "Bank Transfer", "value": "bank_transfer"},
    {"label": "RazorPay", "value": "razorpay_api"},
    {"label": "TDS", "value": "tds"},
    {"label": "Write Off", "value": "write_off", "applicable_roles": "ar-manager"},
]

debtor_config_response = [
    {
        "payment_mode": "tds",
        "debtor_code": "txdp737f",
        "allowed_payment_modes": [
            {"label": "TDS Reconciled", "value": "tds_reconciled"},
        ],
        "settlement_frequency": "quarterly",
        "data_push_trigger": "after_posting",
    },
]

chain_base_currency = "INR"

get_role_privilege_response = [
    {
        "role": "super-admin",
        "privileges": [
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_NEW_DEBTOR",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_SETTLEMENTS",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_DEBTORS",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_MANUAL_DEBIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CANCEL_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_DEBTOR_SUMMARY",
            },
        ],
    },
    {
        "role": "ar-manager",
        "privileges": [
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_NEW_DEBTOR",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_SETTLEMENTS",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_DEBTORS",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CREATE_MANUAL_DEBIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "CANCEL_CREDIT",
            },
            {
                "attributes": None,
                "description": None,
                "module": "Account Receivable",
                "privilege_code": "VIEW_DEBTOR_SUMMARY",
            },
        ],
    },
]

enum_response = [
    {
        "enum_name": "ar_payment_cancellation_reasons",
        "enum_values": [
            {
                "label": "Wrong receipt recorded - rectification",
                "value": "wrong_receipt_recorded_rectification",
            },
            {"label": "Moving to another debtor", "value": "moving_to_another_debtor"},
        ],
        "label": "AR Payment Cancellation Reasons",
    }
]
