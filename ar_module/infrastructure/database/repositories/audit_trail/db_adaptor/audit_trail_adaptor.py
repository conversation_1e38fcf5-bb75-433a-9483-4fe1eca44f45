from ar_module.domain.entities.audit_trail import AuditTrail
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.audit_trail_models import AuditTrailModel


class AuditTrailAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: AuditTrail, **kwargs):
        # noinspection PyArgumentList
        return AuditTrailModel(
            audit_id=domain_entity.audit_id,
            user=domain_entity.user,
            user_type=domain_entity.user_type,
            audit_type=domain_entity.audit_type,
            audit_payload=domain_entity.audit_payload,
            request_id=domain_entity.request_id,
            hotel_id=domain_entity.hotel_id,
            created_at=domain_entity.created_at,
        )

    def to_domain_entity(self, db_entity: AuditTrailModel, **kwargs):
        return AuditTrail(
            audit_id=db_entity.audit_id,
            user=db_entity.user,
            user_type=db_entity.user_type,
            audit_type=db_entity.audit_type,
            audit_payload=db_entity.audit_payload,
            request_id=db_entity.request_id,
            hotel_id=db_entity.hotel_id,
            created_at=db_entity.created_at,
        )
