from sqlalchemy import NUMERIC, Column, Date, Integer, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)


class SettlementModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "settlement"

    debit_id = Column(String, primary_key=True)
    settlement_id = Column(Integer, primary_key=True)
    settled_via = Column(String)
    reference_number = Column(String)
    amount = Column(NUMERIC)
    currency = Column(String)
    credit_id = Column(String)
    settlement_date = Column(Date)
    remarks = Column(String)
