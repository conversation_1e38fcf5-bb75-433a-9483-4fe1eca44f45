class PreviewDebtorSummaryBuilder:
    def __init__(self, debtor_id, from_date, to_date):
        self.data = PreviewDebtorSummaryData(debtor_id, from_date, to_date).__dict__


class PreviewDebtorSummaryData:
    def __init__(self, debtor_id, from_date, to_date):
        self.debtor_id = debtor_id
        self.from_date = from_date
        self.include_mapped_records = False
        self.to_date = to_date
