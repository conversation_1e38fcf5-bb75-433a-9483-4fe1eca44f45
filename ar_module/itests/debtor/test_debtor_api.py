import json
from urllib.parse import urlencode

from ar_module.integration_tests.mockers import mock_get_roles, mock_get_tenant_configs
from ar_module.itests.api_wrappers.debtor_wrapper import create_debtor


def test_get_debtors(
    client_,
    create_debtor_payload,
    config_value=True,
):
    debtor_ids = [create_debtor(client_, create_debtor_payload) for i in range(3)]
    query_params = {"debtor_ids": ",".join(debtor_ids)}
    url = f"/ar/v1/debtors?{urlencode(query_params)}"
    with mock_get_tenant_configs(config_value), mock_get_roles():
        response = client_.get(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
    debtors_data = json.loads(response.data.decode("utf-8"))
    retrieved_debtors_id = [
        debtor["debtor_id"] for debtor in debtors_data["data"]["debtors"]
    ]
    assert response.status_code == 200
    assert sorted(debtor_ids) == sorted(retrieved_debtors_id)
