import os

import flask
from treebo_commons.request_tracing.flask.after_request import after_request
from treebo_commons.request_tracing.flask.before_request import before_request

from ar_module.middlewares.request_middleware import (
    after_request as custom_after_request,
)

app_env = os.environ.get("APP_ENV")


class DefaultConfig(object):
    """
    Base config
    """

    DEBUG = app_env != "production"
    TESTING = app_env == "testing"

    # Rabbit MQ
    URL_PREFIX = "/ar"

    # Logging
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "ERROR")
    LOG_ROOT = os.environ.get("LOG_ROOT", ".")

    MAX_THREAD_FOR_PDF_GENERATION = os.environ.get("MAX_THREAD_FOR_PDF_GENERATION", 4)

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [lambda: before_request(flask.request)]
    AFTER_REQUEST_MIDDLEWARES = [
        lambda resp: after_request(resp, flask.request),
        custom_after_request,
    ]

    JSONIFY_PRETTYPRINT_REGULAR = False
    SLACK_WEBHOOK_URL = "*******************************************************************************"
