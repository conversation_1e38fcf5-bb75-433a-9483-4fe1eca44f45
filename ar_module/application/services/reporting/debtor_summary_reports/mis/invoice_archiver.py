import logging
import os
import shutil
import uuid
from pathlib import Path

from treebo_commons.aws_services.s3_client import S3Client

from ar_module.common.tenant_utils import get_tenant_id
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)

TEMP_INVOICES_STORAGE_DIR = (
    f'{os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/")}/invoices'
)
ARCHIVE_S3_PATH = "ar-invoice-archive/"
logger = logging.getLogger(__name__)
EXPIRY_TIME = 24 * 30 * 3600


class FileMeta:
    def __init__(self, signed_url, file_size, file_name):
        self.signed_url = signed_url
        self.file_size = file_size
        self.file_name = file_name


class InputFileMetaForInvoiceArchiver:
    def __init__(self, file_url, file_name):
        self.file_url = file_url
        self.file_name = file_name


class InvoiceArchiver:
    def __init__(self):
        self.temp_invoices_storage_dir = f"{TEMP_INVOICES_STORAGE_DIR}/{uuid.uuid4()}"
        self.archive_path = f"{TEMP_INVOICES_STORAGE_DIR}/{uuid.uuid4()}_invoice"
        self._setup_invoices_storage_dir()

    def download_file(self, file_meta: InputFileMetaForInvoiceArchiver):
        with open(
            f"{self.temp_invoices_storage_dir}/{file_meta.file_name}", "wb"
        ) as file_object:
            AwsServiceClient.download_file_from_s3(file_meta.file_url, file_object)

    def prepare_archive_and_upload(self) -> FileMeta:
        shutil.make_archive(
            self.archive_path,
            "zip",
            self.temp_invoices_storage_dir,
        )
        file_path = f"{self.archive_path}.zip"
        pre_signed_url = S3Client.upload_file_to_s3_and_get_cloudfront_signed_url(
            file_path,
            ARCHIVE_S3_PATH,
            get_tenant_id(),
            EXPIRY_TIME,
        )
        file_size_in_mb = (
            os.path.getsize(file_path) / 1000**2
        )  # lets consider in decimal not binary
        file_name = Path(file_path).name
        self._teardown_invoices_storage_dir()
        logger.info(f"signed url for pdf {pre_signed_url}")
        return FileMeta(
            signed_url=pre_signed_url, file_size=file_size_in_mb, file_name=file_name
        )

    def _setup_invoices_storage_dir(self):
        if os.path.exists(self.temp_invoices_storage_dir):
            shutil.rmtree(self.temp_invoices_storage_dir)
        os.makedirs(self.temp_invoices_storage_dir)

    def _teardown_invoices_storage_dir(self):
        try:
            shutil.rmtree(self.temp_invoices_storage_dir)
            os.remove(f"{self.archive_path}.zip")
        except FileNotFoundError:
            logger.info(f"No temp storage directory found")
