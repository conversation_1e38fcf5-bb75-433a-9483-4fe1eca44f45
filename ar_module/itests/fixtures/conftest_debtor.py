import json

import pytest

from ar_module.application.consumers.constants import DebtorTypes
from ar_module.common.id_generator_utils import random_id_generator
from ar_module.itests.payload_generators.debtor_payload_generator import (
    create_new_debtor_payload,
)


@pytest.fixture
def create_debtor_payload():
    debtor_type = DebtorTypes.B2B
    debtor_code = random_id_generator()
    payload = create_new_debtor_payload(
        debtor_type=debtor_type, debtor_code=debtor_code
    )

    return json.dumps(payload)
