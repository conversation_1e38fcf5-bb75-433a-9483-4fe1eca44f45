import time

import pytest

from ar_module.integration_tests.config.common_config import (
    AR_MANAGER,
    CREATE_CREDIT_AND_MULTIPLE_DEBIT,
    CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
    CREATE_CREDIT_WITH_SETTLEMENT,
    CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT_AND_SETTLEMENT,
    ERROR_CODES,
    FDM,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.config.sheet_names import EDIT_SETTLEMENT_SHEET_NAME
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.settlement.validations.validation_edit_settlement import (
    ValidationEditSettlement,
)


class TestEditSettlement(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message",
        [
            (
                "EditSettlement_01",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Edit settlement by changing mapped amount",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_02",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Edit settlement by changing mapped amount and add new settlement",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_03",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Edit multiple settlements by changing mapped amounts",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_04",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Delete multiple settlements and add a new settlement only for one debit",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_05",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Edit multiple settlement but keep the mapped amount unchanged",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_06",
                CREATE_CREDIT_AND_MULTIPLE_DEBIT,
                "Edit Settlement such that credits partially settles multiple debits",
                200,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_07",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Edit settlement by changing mapped amount to 0",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_08",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Edit settlement by changing mapped amount such that amount exceeds unused credit amount",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_09",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Edit settlement as an FDM",
                403,
                FDM,
                None,
            ),
            (
                "EditSettlement_10",
                CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT_AND_SETTLEMENT,
                "Edit settlement as super-admin who is not allowed to edit settlement of write-off credits",
                400,
                SUPER_ADMIN,
                None,
            ),
            (
                "EditSettlement_11",
                CREATE_DEBTOR_WITH_WRITE_OFF_CREDIT_AND_SETTLEMENT,
                "Edit settlement as ar-manager who is allowed to edit settlement of write-off credits",
                200,
                AR_MANAGER,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_edit_settlement(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
    ):

        if skip_message:
            pytest.skip(skip_message)

        self.debit_request.debit_id.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.settlement_request.create_settlement_request(
            client_,
            test_case_id,
            self.debit_request.debit_id,
            self.credit_request.credit_id,
            status_code,
            user_type,
            self.credit_request.pay_mode,
            sheet_name=EDIT_SETTLEMENT_SHEET_NAME,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                test_case_id,
                response,
                self.settlement_request,
                self.credit_request,
                self.debit_request,
                self.audit_trail_request,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(
        client_,
        test_case_id,
        response,
        settlement_request,
        credit_request,
        debit_request,
        audit_trail_request,
    ):

        validation = ValidationEditSettlement(
            client_,
            test_case_id,
            response,
            settlement_request,
            credit_request,
            debit_request,
            audit_trail_request,
        )
        validation.validate_response()
        validation.validate_get_settlement()
        validation.validate_audit_trail()
