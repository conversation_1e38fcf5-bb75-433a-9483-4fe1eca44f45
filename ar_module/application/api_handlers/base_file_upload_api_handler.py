import logging

from ar_module.application.dtos.file_upload_jobs_dto import FileUploadJobsResponseDto
from ar_module.async_job.handlers.bulk_data_ingestors.dtos import FileUploadDto
from ar_module.async_job.job.dto.job_dto import Async<PERSON><PERSON><PERSON><PERSON>
from ar_module.async_job.job.job_constants import Job<PERSON><PERSON>, JobStatus
from ar_module.async_job.job_scheduler_service import JobSchedulerService
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.external_clients.authentication.authn_service_client import (
    AuthNClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[JobSchedulerService, UserRepository, AuthNClient])
class BaseFileUploadAPIHandler(object):
    def __init__(
        self,
        job_scheduler_service: JobSchedulerService,
        user_repo: UserRepository,
        auth_client: AuthNClient,
    ):
        self.job_scheduler_service = job_scheduler_service
        self.user_repo = user_repo
        self.auth_client = auth_client

    def handle(self, file_urls, user_data=None):
        self.is_authorised(user_data)
        user_auth_id = user_data.user_auth_id
        current_user_email = self.get_email_of_logged_in_user(user_auth_id)
        response = []
        for index, file_url in enumerate(file_urls):
            try:
                job_data = self._get_job_dto(file_url, current_user_email, user_data)
                job_id = self.job_scheduler_service.schedule(job_data).job_id
                response.append(
                    FileUploadJobsResponseDto(
                        index=index,
                        job_id=job_id,
                        status=JobStatus.CREATED,
                        file_url=file_url,
                    )
                )
            except Exception as e:
                logger.exception(e)
                response.append(
                    FileUploadJobsResponseDto(
                        index=index,
                        job_id=None,
                        status=JobStatus.CREATION_FAILED,
                        file_url=file_url,
                    )
                )

        return response

    def _get_job_dto(self, file_url, ack_email, user_data):
        return AsyncJobDTO(
            job_name=self.get_job_name(),
            data=FileUploadDto(file_url, ack_email, user_data),
            hotel_id=user_data.hotel_id,
        )

    def is_authorised(self, user_data):
        pass

    def get_job_name(self):
        raise NotImplementedError()

    def get_email_of_logged_in_user(self, user_id):
        # TODO Handle in better way
        try:
            return self.user_repo.search_user_by_id(user_id=user_id).user.email
        except Exception as e:
            return self.auth_client.get_user_by_id(user_id).email
        except Exception as e:
            return None
