from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from ar_module.domain.entities.credit_reversal import CreditReversal
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.credit_model import (
    CreditReversalMappingModel,
)


class CreditReversalAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: CreditReversal, **kwargs):
        return CreditReversalMappingModel(
            id=domain_entity.id,
            payment_credit_id=domain_entity.payment_credit_id,
            refund_credit_id=domain_entity.refund_credit_id,
            amount_in_base_currency=domain_entity.amount_in_base_currency.amount,
            amount_in_credit_currency=domain_entity.amount_in_credit_currency.amount,
            base_currency=domain_entity.amount_in_base_currency.currency.value,
            credit_currency=domain_entity.amount_in_credit_currency.currency.value,
            remarks=domain_entity.remarks,
            status=domain_entity.status,
            cancellation_date=domain_entity.cancellation_date,
        )

    def to_domain_entity(self, db_entity, **kwargs):
        amount_in_base_currency = Money(
            amount=db_entity.amount_in_base_currency,
            currency=CurrencyType(db_entity.base_currency),
        )
        amount_in_credit_currency = Money(
            amount=db_entity.amount_in_credit_currency,
            currency=CurrencyType(db_entity.credit_currency),
        )
        return CreditReversal(
            id=db_entity.id,
            payment_credit_id=db_entity.payment_credit_id,
            refund_credit_id=db_entity.refund_credit_id,
            amount_in_base_currency=amount_in_base_currency,
            amount_in_credit_currency=amount_in_credit_currency,
            remarks=db_entity.remarks,
            status=db_entity.status,
            cancellation_date=db_entity.cancellation_date,
            created_at=db_entity.created_at,
            modified_at=db_entity.modified_at,
            deleted=db_entity.deleted,
        )
