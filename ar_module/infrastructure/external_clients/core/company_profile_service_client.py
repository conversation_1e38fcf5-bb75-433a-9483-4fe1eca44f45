import logging

import requests
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from ar_module.domain.dtos.company_profiles_dto import CompanyProfilePOCDTO
from ar_module.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class CompanyProfileServiceClient(object):
    def __init__(self):
        self.cps_base_url = ServiceRegistryClient.get_company_profile_service_url()

    @staticmethod
    def get_headers():
        headers = {"content-type": "application/json"}
        enrich_outgoing_request(headers)
        return headers

    def get_company_profiles(self, hotel_id, company_name, source=None):
        url = (
            self.cps_base_url + "/company-profiles/v1/search"
            "?legal_entity_name={company_name}&is_fuzzy_search={ifs}".format(
                company_name=company_name, ifs=False
            )
        )
        if source:
            url += f"&source={source}"

        headers = self.get_headers()
        headers["X-Hotel-Id"] = hotel_id
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Company Profile API Error. Status Code: {0}".format(
                    response.status_code
                )
            )
        api_response = None
        try:
            if response.json() and response.json().get("data"):
                api_response = response.json()["data"].get(
                    "parent_entities"
                ) or response.json()["data"].get("sub_entities")
        except Exception as e:
            error = f"Exception in get company profile call: {str(e)}"
            logger.exception(error)
        return api_response

    def get_company_profile_on_company_code(
        self, hotel_id, company_code, return_both_entities=False
    ):
        url = (
            self.cps_base_url + "/company-profiles/v1/search"
            "?superhero_company_code={company_code}".format(company_code=company_code)
        )
        headers = self.get_headers()
        headers["X-Hotel-Id"] = hotel_id
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Company Profile API Error. Status Code: {0}".format(
                    response.status_code
                )
            )
        api_response = None
        try:
            if response.json() and response.json().get("data"):
                if return_both_entities:
                    return response.json()["data"]
                api_response = response.json()["data"].get(
                    "parent_entities"
                ) or response.json()["data"].get("sub_entities")
        except Exception as e:
            error = f"Exception in get company profile call: {str(e)}"
            logger.exception(error)
        return api_response

    def get_poc_details(self, hotel_id, company_code):
        entities = self.get_company_profile_on_company_code(hotel_id, company_code)
        if not entities:
            return []
        pocs = entities[0].get("point_of_contacts") or []
        return [CompanyProfilePOCDTO(**poc) for poc in pocs]
