from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.view_credit_facts import ViewCreditFacts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class ViewCreditRule(BaseRule):
    def allow(self, facts: ViewCreditFacts, privileges=None):
        if PrivilegeCode.VIEW_CREDIT not in privileges:
            raise PolicyAuthException(error=PolicyError.VIEW_CREDIT_NOT_ALLOWED)
        return True
