import logging

from flask import current_app
from flask.cli import with_appcontext
from ths_common.constants.tenant_settings_constants import TenantSettingName
from treebo_commons.multitenancy.tenant_client import TenantClient

from ar_module.application.consumers.constants import (
    CompanyProfileEventType,
    POCsDesignation,
)
from ar_module.application.consumers.decorator import consumer_middleware
from ar_module.application.dtos.debtor_dto import DebtorDTO
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.common.slack_alert_helper import SlackAlert
from ar_module.domain.constants import ARModuleConfigs
from ar_module.infrastructure.consumers.base_consumer import BaseRMQConsumer
from ar_module.infrastructure.consumers.consumer_config import (
    CompanyProfileConsumerConfig,
)
from ar_module.infrastructure.external_clients.authentication.authn_service_client import (
    AuthNClient,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import locate_instance

logger = logging.getLogger(__name__)


class CompanyProfileConsumer(BaseRMQConsumer):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(CompanyProfileConsumerConfig(tenant_id))
        self.tenant_id = tenant_id
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.accounts_receivable_service = locate_instance(
            AccountReceivableApplicationService
        )
        self.catalog_client = locate_instance(CatalogServiceClient)
        self.authn_client = locate_instance(AuthNClient)
        self.tenant_settings = locate_instance(TenantSettings)
        self.cache = {}

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        message.ack()
        logger.info(f"Received Company Profile Event with body {body}. Message Acked")

        with current_app.test_request_context():
            event = body.get("events")
            parent_ent_lvl_debtor_creation_enabled = (
                self.tenant_settings.get_setting_value(
                    ARModuleConfigs.PARENT_ENTITY_LEVEL_DEBIT_CREATION
                )
            )
            sub_ent_lvl_debtor_creation_enabled = (
                self.tenant_settings.get_setting_value(
                    ARModuleConfigs.SUB_ENTITY_LEVEL_DEBIT_CREATION
                )
            )
            is_test = event.get("payload").get("is_test")
            if is_test:
                logger.info(
                    f"Received test debtor {event.get('payload').get('superhero_company_code')}"
                )
                return
            if parent_ent_lvl_debtor_creation_enabled:
                if (
                    body.get("event_type")
                    == CompanyProfileEventType.PARENT_ENTITY_CREATED
                ):
                    self.process_create_debtor_event(event.get("payload"))
                elif (
                    body.get("event_type")
                    == CompanyProfileEventType.PARENT_ENTITY_UPDATED
                ):
                    self.process_update_debtor_event(event.get("payload"))

            if sub_ent_lvl_debtor_creation_enabled:
                if body.get("event_type") == CompanyProfileEventType.SUB_ENTITY_CREATED:
                    self.process_create_debtor_event(event.get("payload"))
                elif (
                    body.get("event_type") == CompanyProfileEventType.SUB_ENTITY_UPDATED
                ):
                    self.process_update_debtor_event(event.get("payload"))

            if (
                not parent_ent_lvl_debtor_creation_enabled
                and not sub_ent_lvl_debtor_creation_enabled
            ):
                logger.info(
                    "Debtor creation is disabled for both parent and sub entity level"
                )

        logger.info("Completed processing Company Profile Event")

    def process_create_debtor_event(self, profile_data):
        debtor_code = profile_data.get("superhero_company_code")
        try:
            poc_user = self.get_poc_user_from_sh_profile_data(profile_data)
            debtor_dto = DebtorDTO.create_from_sh_profile_data(profile_data)
            self.accounts_receivable_service.create_update_user_debtor_info_using_profile_data(
                poc_user, debtor_dto
            )
        except Exception as e:
            logger.info(
                "Error processing create debtor event for corporate: {0}, error: {1}".format(
                    debtor_code, str(e)
                )
            )

    def process_update_debtor_event(self, profile_data):
        debtor_code = profile_data.get("superhero_company_code")
        try:
            poc_user = self.get_poc_user_from_sh_profile_data(profile_data)
            debtor_dto = DebtorDTO.create_from_sh_profile_data(profile_data)
            self.accounts_receivable_service.create_update_user_debtor_info_using_profile_data(
                poc_user, debtor_dto
            )
        except Exception as e:
            logger.info(
                "Error processing update debtor event for corporate: {0}, error: {1}".format(
                    debtor_code, str(e)
                )
            )

    def get_poc_user_from_sh_profile_data(self, profile_data):
        pocs = dict()
        if profile_data.get("point_of_contacts"):
            for poc in profile_data["point_of_contacts"]:
                pocs[poc["designation"]] = self._make_poc(poc)
        finance_poc = pocs.get(POCsDesignation.FINANCE_POC)

        if not finance_poc or not finance_poc.get("email"):
            if self.tenant_id != TenantSettingName.TREEBO_TENANT_ID.value:
                return None
            logger.info(
                "Finance poc info is incomplete for debtor: {0}".format(
                    profile_data.get("superhero_company_code")
                )
            )
            return None

        poc_email = finance_poc.get("email")
        try:
            poc_user = self.authn_client.get_user_by_email(poc_email)
        except Exception:
            SlackAlert.send_alert(
                "No user found for email: {0}".format(poc_email),
                tenant_id=self.tenant_id,
            )
            logger.exception("No user found for email: {0}".format(poc_email))
            return None
        return poc_user

    @staticmethod
    def _make_poc(data):
        return dict(
            first_name=data.get("name"),
            phone_number=data["phone_number"].get("number")
            if data.get("phone_number")
            else "",
            email=data["email_ids"][0] if data.get("email_ids") else None,
            is_active=True,
            auth_id=None,
        )
