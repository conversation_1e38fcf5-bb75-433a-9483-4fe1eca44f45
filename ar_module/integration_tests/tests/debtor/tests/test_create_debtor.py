import pytest

from ar_module.integration_tests.config.common_config import (
    CREATE_DEBTOR,
    CREATE_DEBTOR_HOTEL_LEVEL,
    ERROR_CODES,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.debtor.validations.validation_create_debtor import (
    ValidationCreateDebtor,
)


class TestCreateDebtor(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_message",
        [
            (
                "CreateDebtor_01",
                "",
                "create a user with debtor_code, debtor_name and hotel_id",
                200,
                None,
            ),
            (
                "CreateDebtor_02",
                "",
                "create a debtor with debtor_code and name but without hotel_id",
                200,
                None,
            ),
            ("CreateDebtor_03", "", "create a debtor without debtor_code", 400, None),
            ("CreateDebtor_04", "", "create a debtor without debtor_name", 400, None),
            (
                "CreateDebtor_05",
                CREATE_DEBTOR_HOTEL_LEVEL,
                "create a debtor with debtor_name  already exist on same " "hotel",
                200,
                None,
            ),
            (
                "CreateDebtor_06",
                CREATE_DEBTOR_HOTEL_LEVEL,
                "create a debtor with debtor_name that already exist on different hotel",
                200,
                None,
            ),
            (
                "CreateDebtor_07",
                CREATE_DEBTOR,
                "create a debtor ç debtor_code that already exist",
                400,
                "No Feature",
            ),
            (
                "CreateDebtor_08",
                CREATE_DEBTOR_HOTEL_LEVEL,
                "create a existing debtor on new hotel",
                200,
                None,
            ),
            (
                "CreateDebtor_09",
                CREATE_DEBTOR,
                "create a debtor already exists(ex CreateDebtor_01)",
                400,
                "No Feature",
            ),
            (
                "CreateDebtor_10",
                "",
                "create a user with debtor_code, debtor_name, b2b debtor_type and hotel_id",
                200,
                None,
            ),
            (
                "CreateDebtor_11",
                "",
                "create a user with debtor_code, debtor_name, b2c debtor_type and hotel_id",
                200,
                None,
            ),
            (
                "CreateDebtor_12",
                "",
                "create a user with debtor_code, debtor_name, invalid debtor_type and hotel_id",
                400,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_create_debtor(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.debtor_request.create_new_debtor_request(
            client_, test_case_id, status_code, SUPER_ADMIN
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(
                client_,
                response,
                test_case_id,
                self.debtor_request,
                self.debtor_request.hotel_id,
            )
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, debtor_request, hotel_id):
        validation = ValidationCreateDebtor(
            client_, test_case_id, response, debtor_request, hotel_id
        )
        validation.validate_response()
        validation.validate_get_debtors()
