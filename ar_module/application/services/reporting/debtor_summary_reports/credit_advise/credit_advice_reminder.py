import datetime
import os
import uuid
from collections import OrderedDict
from pathlib import Path

from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.reporting.dtos import DebtorSummaryRequestData
from ar_module.application.services.reporting.excel_writer import ExcelWriter
from ar_module.domain.constants import AuditType, CreditType
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

REPORT_PATH = "ar-reports/"


@register_instance(
    dependencies=[
        CreditRepository,
        DebtorRepository,
        CommunicationServiceClient,
        AuditTrailService,
    ]
)
class CreditAdviceReminderDispatcher:

    credits_report_column_mapping = OrderedDict(
        dict(
            credit_date="Payment Date",
            reference_number="Payment Id",
            amount_in_base_currency="Payment Amount",
            unused_credit_amount="Unused Amount",
            mode_of_credit="Mode of Payment",
        )
    )

    email_identifier = "ar_payment_advice_reminder"

    def __init__(
        self,
        credit_repo: CreditRepository,
        debtor_repo: DebtorRepository,
        communication_service_client: CommunicationServiceClient,
        audit_trail_service: AuditTrailService,
    ):
        self.credit_repo = credit_repo
        self.debtor_repo = debtor_repo
        self.communication_service_client = communication_service_client
        self.audit_trail_service = audit_trail_service

    def dispatch(self, request: DebtorSummaryRequestData):
        debtor = self.debtor_repo.load(request.debtor_id)
        name = debtor.debtor_name
        report_file_path = f'{os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/")}{self._generate_report_filename(name)}'
        credit_search_query = CreditSearchQuery(
            debtor_id=request.debtor_id,
            from_date=request.from_date,
            to_date=request.to_date,
            has_unused_credit=True,
            show_cancelled_credits=False,
            credit_types=[CreditType.PAYMENT, CreditType.TDS, CreditType.CREDIT_NOTE],
        )
        credit_items = self.credit_repo.load_credits(
            credit_search_query, credits_only=True, yield_result=True
        )
        total_unused_credits = self.credit_repo.total_unused_credits(
            credit_search_query
        )
        with ExcelWriter(report_file_path) as excel_writer:
            if credit_items:
                excel_writer.write_aggregates(
                    credit_items,
                    self.credits_report_column_mapping,
                    sheet_name="Payments",
                ).save_file()
            signed_url_of_payment_advice_reports = (
                (
                    AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                        REPORT_PATH,
                        excel_writer.file_path,
                        self.get_default_expiration_time(),
                    )
                )
                if credit_items
                else None
            )
        self._send_email_communication(
            request,
            debtor,
            signed_url_of_payment_advice_reports,
            total_unused_credits if total_unused_credits else 0.0,
            report_file_path,
        )
        self.audit_trail_service.create_debtor_communication_audit_trail(
            self.email_identifier,
            debtor,
            AuditType.DEBTOR_COMMUNICATION,
            request.user_data,
        )

    @staticmethod
    def _generate_report_filename(name):
        return f"{name}_Payment_Advice_Follow_Up_Report.xlsx"

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def _send_email_communication(
        self,
        request: DebtorSummaryRequestData,
        debtor,
        signed_url_of_payment_advice_reports,
        total_unused_credits,
        report_file_path,
    ):
        subject = (
            f"{debtor.debtor_code} - {debtor.debtor_name} - Payment Advice Required"
        )
        context_data = dict(
            customer_name=debtor.debtor_name,
            from_date=request.from_date,
            to_date=request.to_date,
            report_date=str(datetime.datetime.today()),
            total_unused_credits=total_unused_credits,
        )
        attachments = [
            dict(
                url=signed_url_of_payment_advice_reports,
                filename=Path(report_file_path).name,
            )
        ]
        self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=request.to_recipients,
            cc=request.cc_recipients,
            subject=subject,
            attachments=attachments,
        )
