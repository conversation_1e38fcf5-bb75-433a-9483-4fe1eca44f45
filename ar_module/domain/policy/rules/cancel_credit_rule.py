from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.cancel_credit_facts import CancelCreditFacts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CancelCreditRule(BaseRule):
    def allow(self, facts: CancelCreditFacts, privileges=None):
        if PrivilegeCode.CANCEL_CREDIT not in privileges:
            raise PolicyAuthException(error=PolicyError.CANCEL_CREDIT_NOT_ALLOWED)
        return True
