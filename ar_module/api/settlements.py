from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas.request.common import BulkFileUploadRequestSchema
from ar_module.api.schemas.request.settlement import (
    SettlementRequestSchema,
    SettlementSearchSchema,
)
from ar_module.api.schemas.response.common import FileUploadResponseSchema
from ar_module.api.schemas.response.settlement import SettlementResponseSchema
from ar_module.application.api_handlers.bulk_upload_settlements import (
    BulkUploadSettlementHandler,
)
from ar_module.application.services.settlement_service import SettlementService
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/credits/<string:credit_id>/settlements", methods=["POST"])
@schema_wrapper_parser(SettlementRequestSchema, many=True)
@inject(settlement_service=SettlementService)
def create_settlements(settlement_service, parsed_request, credit_id):
    """
    Settles the credit against one or more debits
    ---
    operationId: create_settlements
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Settles the credit (of given credit_id), against one or more debit.

            NOTE - If passed settlement amount exceeds the unused_credit_amount, the entire operation fails,
            and non of the
            settlement is created from that particular request
        tags:
            - Settlement
        parameters:
            - in: path
              name: credit_id
              type: string
              required: True
            - in: body
              name: body
              description: List of settlements to be created against this credit
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: array
                        items:
                            $ref: "#/definitions/SettlementRequestSchema"
        responses:
            200:
                description: List of Settlement
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                settlements:
                                    type: array
                                    items:
                                        $ref: "#/definitions/SettlementResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    settlements = settlement_service.create_settlements_from_existing_credit(
        credit_id, parsed_request, user_data=user_data
    )
    settlements_response_schema = SettlementResponseSchema(many=True)
    response = settlements_response_schema.dump(settlements)
    return ApiResponse.build(data=dict(settlements=response.data), status_code=200)


@ar_bp.route("/settlements", methods=["GET"])
@schema_wrapper_parser(SettlementSearchSchema, param_type=RequestTypes.ARGS)
@inject(settlement_service=SettlementService)
def fetch_settlements_for_invoice(settlement_service, parsed_request):
    """
    Get all settlements for the given reference_id (which is invoice_id)
    ---
    operationId: fetch_settlements_for_invoice
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema: SettlementSearchSchema
        description: Fetch settlements for an invoice
        tags:
            - Settlement
        responses:
            200:
                description: List of Settlements for the given filter criteria
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                settlements:
                                    type: array
                                    items:
                                        $ref: "#/definitions/SettlementResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    settlements = settlement_service.search_settlements(
        filter_criteria=parsed_request, user_data=user_data
    )
    settlements_schema = SettlementResponseSchema(many=True)
    response = settlements_schema.dump(settlements)
    return ApiResponse.build(data=dict(settlements=response.data), status_code=200)


@ar_bp.route("/settlements/bulk-upload", methods=["POST"])
@schema_wrapper_parser(BulkFileUploadRequestSchema)
@inject(handler=BulkUploadSettlementHandler)
def bulk_upload_settlements(handler: BulkUploadSettlementHandler, parsed_request):
    """
    Bulk process settlements in async
    ---
    operationId: create_credits_async
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Creates settlements in async by processing file input.
        tags:
            - Credit-Upload
        parameters:
            - in: body
              name: body
              description: The object contains list of file urls to be processed
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: array
                        $ref: "#/definitions/BulkFileUploadRequestSchema"
        responses:
            200:
                description: List of async jobs created to process files
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            $ref: "#/definitions/FileUploadResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    response_dto = handler.handle(parsed_request.get("file_urls"), user_data=user_data)
    response = FileUploadResponseSchema(many=True).dump(response_dto).data
    return ApiResponse.build(data=response, status_code=200)
