-- revision: '20231128162400_async_job_table'
-- down_revision: '20231115195346_add_more_columns_to_debtor'

-- upgrade
CREATE TABLE job (
    job_id VARCHAR(255) PRIMARY KEY,
    job_name VARCHAR(255) NOT NULL,
    data TEXT,
    eta timestamp with time zone DEFAULT now() NOT NULL,
    generated_at timestamp with time zone DEFAULT now() NOT NULL,
    status VARCHAR(255),
    hotel_id VARCHAR(255),
    picked_at TIMESTAMP WITH TIME ZONE,
    failure_message TEXT,
    total_tries INTEGER
);

-- downgrade
DROP TABLE job;