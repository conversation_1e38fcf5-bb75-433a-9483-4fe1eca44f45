import json

from ar_module.domain.constants import CreditCancellationReason
from ar_module.integration_tests.mockers import (
    mock_applicable_roles_for_payment_mode,
    mock_catalog_service_client,
    mock_debtor_config,
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
    mock_payment_modes,
    mock_presigned_url_from_s3_url,
)
from ar_module.itests.api_wrappers.credit_wrapper import cancel_credit, create_credit
from ar_module.itests.api_wrappers.debit_wrapper import create_debit
from ar_module.itests.api_wrappers.debtor_wrapper import create_debtor
from ar_module.itests.payload_generators.settlement_payload_generator import (
    create_new_settlements_payload,
)


def test_create_credit(
    client_,
    create_credit_payload_with_settlements,
    create_debtor_payload,
    create_debit_payload,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    assert credit_response["data"]["credit"]["credit_id"] is not None


def test_cancel_credit(
    client_,
    create_credit_payload_with_settlements,
    create_debtor_payload,
    create_debit_payload,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    with mock_catalog_service_client():
        cancel_status = cancel_credit(
            client_, credit_response, CreditCancellationReason.RECTIFICATION
        )
    assert cancel_status == 200


def test_get_credit(
    client_,
    create_credit_payload_with_settlements,
    create_debtor_payload,
    create_debit_payload,
    config_value=True,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    credit_id = credit_response["data"]["credit"]["credit_id"]

    url = f"/ar/v1/credits?debtor_id={debtor_id}"
    with mock_get_tenant_configs(config_value), mock_get_roles():
        response = client_.get(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
    credit_data = json.loads(response.data.decode("utf-8"))
    assert response.status_code == 200
    assert len(credit_data["data"]["credits"]) == 2

    url = f"/ar/v1/credits?debtor_id={debtor_id}&credit_id={credit_id}"
    with mock_get_tenant_configs(config_value), mock_get_roles():
        response = client_.get(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
    credit_data = json.loads(response.data.decode("utf-8"))
    assert response.status_code == 200
    assert credit_data["data"]["credits"][0]["credit_id"] == credit_id


def test_create_credit_with_multiple_settlements(
    client_,
    create_debtor_payload,
    create_debit_payload,
    create_credit_payload,
    config_value=True,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_ids = [
        create_debit(client_, create_debit_payload, debtor_id) for i in range(3)
    ]
    _payload = dict(json.loads(create_credit_payload))
    _payload["debtor_id"] = debtor_id
    _payload["settlements"] = create_new_settlements_payload(debit_ids)
    pay_mode = _payload["mode_of_credit"]
    payload = json.dumps({"data": _payload})

    url = "/ar/v1/credits"
    with mock_get_tenant_configs(
        config_value
    ), mock_payment_modes(), mock_is_hotel_level_accounts_receivable_configured(
        config_value
    ), mock_debtor_config():
        with mock_get_roles(), mock_applicable_roles_for_payment_mode(
            pay_mode
        ), mock_presigned_url_from_s3_url():
            response = client_.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )

    assert response.status_code == 200
    credit_data = json.loads(response.data.decode("utf-8"))["data"]["credit"]
    assert len(credit_data) > 0
    assert credit_data["unused_credit_amount"] == "440.00"
