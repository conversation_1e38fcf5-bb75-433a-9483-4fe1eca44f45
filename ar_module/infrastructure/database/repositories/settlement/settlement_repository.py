from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.settlement_model import SettlementModel
from ar_module.infrastructure.database.repositories.debit.db_adaptors.settlement_adaptor import (
    SettlementAdaptor,
)
from object_registry import register_instance


@register_instance()
class SettlementRepository(BaseRepository):
    settlement_adaptor = SettlementAdaptor()

    def load_settlements_with_credit_ids(self, credit_ids):
        query = self.query(SettlementModel)
        query = query.filter(
            SettlementModel.credit_id.in_(credit_ids),
            SettlementModel.deleted.is_(False),
        )
        settlements = query.all()
        settlements = [
            self.settlement_adaptor.to_domain_entity(settlement)
            for settlement in settlements
        ]
        return settlements

    def load_settlements(self, **filters):
        query = self.query(SettlementModel)
        credit_id = filters.get("credit_id")
        debit_id = filters.get("debit_id")
        if credit_id:
            query = query.filter(SettlementModel.credit_id == credit_id)
        if debit_id:
            query = query.filter(SettlementModel.debit_id == debit_id)
        settlements = query.filter(SettlementModel.deleted.is_(False)).all()
        settlements = [
            self.settlement_adaptor.to_domain_entity(settlement)
            for settlement in settlements
        ]
        return settlements
