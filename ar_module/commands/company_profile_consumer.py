# coding=utf-8
"""Click commands."""

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from ar_module.application.consumers.company_profile_consumer import (
    CompanyProfileConsumer,
)
from ar_module.core.threadlocal import consumer_context


@click.command("start_company_profile_consumer")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
def start_company_profile_consumer(tenant_id=TenantClient.get_default_tenant()):
    # TODO: Figure out a way to only setup database session registry for this tenant in db_engine.
    # Right now, db_engine sets up all the tenant session. One way is to setup scoped session lazily.
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = CompanyProfileConsumer(tenant_id)
    consumer.start_consumer()
