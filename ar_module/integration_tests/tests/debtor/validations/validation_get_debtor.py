import json

from ar_module.integration_tests.config.sheet_names import GET_DEBTOR_SHEET_NAME
from ar_module.integration_tests.utilities.common_utils import assert_
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationGetDebtor:
    def __init__(self, client, response, test_case_id, debtor_request):
        self.client = client
        self.response = response
        self.test_data = get_test_case_data(GET_DEBTOR_SHEET_NAME, test_case_id)[0]
        self.debtor_request = debtor_request

    def validate_response(self):
        expected_debtor_response = json.loads(self.test_data["expected_response"])
        actual_debtor_response = self.response["data"]["debtors"]

        actual_debtor_response = sorted(
            actual_debtor_response, key=lambda i: i["debtor_code"]
        )
        expected_debtor_response = sorted(
            expected_debtor_response, key=lambda i: i["debtor_code"]
        )

        for actual_data, expected_data in zip(
            actual_debtor_response, expected_debtor_response
        ):
            assert_(actual_data["debtor_code"], expected_data["debtor_code"])
            assert actual_data["debtor_id"] in self.debtor_request.debtor_ids
            assert_(actual_data["debtor_name"], expected_data["debtor_name"])
            assert_(actual_data["debtor_type"], expected_data["debtor_type"])
            assert_(actual_data["hotel_id"], expected_data["hotel_id"])
            assert_(actual_data["user_profile_id"], expected_data["user_profile_id"])
