version: "2"

services:
  accounts_receivable:
    image: "docker-hub.treebo.com:5000/${DOCKER_TAGNAME}:${VERSION}"
    hostname: "${HOSTNAME}"
    ports:
      - "${HOST_PORT}:8000"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/ar_module/"
    container_name: "ar_module_app_server"
    restart: always
    entrypoint: /usr/src/app/gunicorn.sh
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

