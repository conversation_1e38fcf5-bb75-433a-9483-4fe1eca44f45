from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas.request.audit_trail import AuditTrailSearchSchema
from ar_module.api.schemas.response.audit_trail import AuditTrailPaginatedResponseSchema
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/audit-trails", methods=["GET"])
@schema_wrapper_parser(AuditTrailSearchSchema, param_type=RequestTypes.ARGS)
@inject(audit_trail_app_service=AuditTrailService)
def get_audit_trails(audit_trail_app_service, parsed_request):
    """
    Returns all the audits for the given query (in query param).
    ---
    operationId: get_audits
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: true
              schema: AuditTrailSearchSchema
        description: Fetch audits for the given query. Apply any extra filter passed to the result
        tags:
            - Audit Trail
        responses:
            200:
                description: List of Audit Trail
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                audits:
                                    type: array
                                    items:
                                        $ref: "#/definitions/AuditTrailPaginatedResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    if parsed_request.get("audit_types"):
        parsed_request["audit_types"] = parsed_request.get("audit_types").split(",")
    get_total_count = (
        True
        if parsed_request.get("limit") is not None
        and parsed_request.get("offset") is not None
        else False
    )
    audit_aggregates, total_audits = audit_trail_app_service.search_audits(
        parsed_request, get_total_count=get_total_count, user_data=user_data
    )
    audit_response_schema = AuditTrailPaginatedResponseSchema()
    response = audit_response_schema.dump(
        dict(
            audit_trails=[aggregate.audit_trail for aggregate in audit_aggregates],
            limit=parsed_request.get("limit"),
            offset=parsed_request.get("offset"),
            total=total_audits,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)
