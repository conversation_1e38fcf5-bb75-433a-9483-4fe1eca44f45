# coding=utf-8
"""
App Initiate file
"""
import logging
import os

import click
from flask import Flask
from healthcheck import HealthCheck
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.request_tracing.flask.after_request import clear_request_context

from ar_module.api.blueprints import ar_bp
from ar_module.async_job.job_executor import async_job_executor
from ar_module.commands.clean_debtor import clean_debtor
from ar_module.commands.company_profile_consumer import start_company_profile_consumer
from ar_module.commands.crs_consumer import start_crs_consumer
from ar_module.commands.erp_worker import start_erp_worker
from ar_module.commands.ingest_debtor import ingest_debtor
from ar_module.commands.ingest_invoice import ingest_invoice
from ar_module.config.app_config import DefaultConfig
from ar_module.config.logging_conf import configure_logging
from ar_module.config.swagger_config import init_swagger_docs
from ar_module.middlewares.request_middleware import exception_handler
from ar_module.scripts.create_users_debtors_in_bulk import create_users_debtors_in_bulk
from ar_module.scripts.populate_hotel_id_in_debit import populate_hotel_ids_in_debits
from object_registry import locate_instance

logger = logging.getLogger(__name__)


def create_app():
    """
    Create App
    :return:
    """
    app = Flask(
        __name__,
        instance_relative_config=False,
        instance_path=os.environ.get("FLASK_APP_INSTANCE_PATH"),
    )
    setup_config(app)
    register_extensions(app)
    register_blueprints(app, app.config["URL_PREFIX"])
    register_commands(app)
    app.before_request_funcs = {None: app.config["BEFORE_REQUEST_MIDDLEWARES"]}
    app.after_request_funcs = {None: app.config["AFTER_REQUEST_MIDDLEWARES"]}
    register_error_handlers(app)
    configure_swagger(app)
    with app.app_context():
        init_swagger_docs(app, app.config["URL_PREFIX"])
    setup_health_check(app)

    @app.shell_context_processor
    def make_shell_context():
        return {"locate_instance": locate_instance, "ctx": app.test_request_context()}

    @app.teardown_request
    def shutdown_session(exception=None):
        if os.environ.get("APP_ENV") != "testing":
            # NOTE: During testing, removing session in request context will rollback the data setup of test,
            # which are done and not committed, which can result in test failure
            # For testing, we remove session in teardown_appcontext instead, which is done after every test,
            # instead of after every api call.
            db_engine.remove_session()

    @app.teardown_appcontext
    def clear_thread_local(exception=None):
        try:
            current_tenant_id = get_current_tenant_id()
            # logger.debug("Connection pool status [%s] => %s", current_tenant_id,
            #              db_engine.get_engine(current_tenant_id).pool.status())
        except:
            logger.error("Can't log connection pool status")
            pass

        if os.environ.get("APP_ENV") == "testing":
            db_engine.remove_session()
        clear_request_context()

    return app


def register_commands(app):
    """Register Click commands."""
    app.cli.add_command(start_crs_consumer)
    app.cli.add_command(start_erp_worker)
    app.cli.add_command(start_company_profile_consumer)
    app.cli.add_command(async_job_executor)
    app.cli.add_command(create_users_debtors_in_bulk)
    app.cli.add_command(populate_hotel_ids_in_debits)
    app.cli.add_command(clean_debtor)
    app.cli.add_command(ingest_debtor)
    app.cli.add_command(ingest_invoice)


def setup_health_check(app):
    """

    :param app:
    :return:
    """
    health = HealthCheck(app, "/api/health", ["rds"])

    def rds_available():
        for tenant_id, scoped_session in db_engine.tenant_wise_sessions.items():
            Session = scoped_session()
            try:
                logger.info(
                    "Making connection with RDS for tenant_id {0}".format(tenant_id)
                )
                Session.execute("SELECT 1")
                logger.info("Connection successful with RDS")
            except Exception as e:
                logger.error("Exception occured while connection with RDS %s" % e)
                raise e
            finally:
                scoped_session.remove()
        return True, "connection successful"

    health.add_check(rds_available)


def register_error_handlers(app):
    """

    :param app:
    :return:
    """
    app.register_error_handler(Exception, exception_handler)


def setup_config(app):
    """ " """
    environment = os.environ.get("APP_ENV", "local")
    # load the default config
    app.config.from_object(DefaultConfig)
    # load from config set by the app
    try:
        app.config.from_envvar("CONFIG_FILE_PATH", silent=False)
    except RuntimeError:
        if not os.environ.get("CONFIG_FILE_PATH"):
            click.echo(
                "CONFIG_FILE_PATH environment variable is not set. Default Config will be used"
            )
        else:
            click.echo(
                "Couldn't load config file from: %s"
                % os.environ.get("CONFIG_FILE_PATH")
            )

    click.echo(
        "Setting up Flask App: '%s', using environment: '%s', and config file: %s"
        % (__name__, environment, os.environ.get("CONFIG_FILE_PATH", "DefaultConfig"))
    )
    configure_logging(app)


def register_extensions(app):
    pass


def register_blueprints(app, url_prefix=None):
    """
    Registering BluePrints
    :param app:
    :param url_prefix:
    :return:
    """
    app.register_blueprint(ar_bp, url_prefix=ar_bp.url_prefix)


def configure_swagger(app):
    app.config["SWAGGER"] = {
        "title": "AR Module API Spec",
        "uiversion": 3,
    }
