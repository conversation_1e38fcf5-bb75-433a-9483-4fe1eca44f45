from treebo_commons.utils import dateutils


class InvoiceMeta:
    def __init__(
        self,
        guest_name,
        hotel_name,
        hotel_id,
        checkin,
        checkout,
    ):
        self.guest_name = guest_name
        self.hotel_name = hotel_name
        self.hotel_id = hotel_id
        self.checkin = dateutils.to_date(checkin) if checkin else None
        self.checkout = dateutils.to_date(checkout) if checkout else None

    def serialize(self):
        return dict(
            guest_name=self.guest_name,
            hotel_name=self.hotel_name,
            hotel_id=self.hotel_id,
            checkin=dateutils.date_to_ymd_str(self.checkin) if self.checkin else None,
            checkout=dateutils.date_to_ymd_str(self.checkout)
            if self.checkout
            else None,
        )

    @staticmethod
    def load_from_dict(data):
        return InvoiceMeta(
            guest_name=data.get("guest_name"),
            hotel_name=data.get("hotel_name"),
            hotel_id=data.get("hotel_id"),
            checkin=dateutils.ymd_str_to_date(data.get("checkin"))
            if data.get("checkin")
            else None,
            checkout=dateutils.ymd_str_to_date(data.get("checkout"))
            if data.get("checkout")
            else None,
        )
