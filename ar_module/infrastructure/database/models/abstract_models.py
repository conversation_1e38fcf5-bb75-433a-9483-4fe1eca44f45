from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, func


class TimeStampMixin(object):
    """
    model for created time and modified time
    """

    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    modified_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )


class DeleteMixin(object):
    deleted = Column("deleted", Boolean, default=False)
