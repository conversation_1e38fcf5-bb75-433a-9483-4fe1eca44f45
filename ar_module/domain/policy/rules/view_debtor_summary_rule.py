from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.facts import Facts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class ViewDebtorSummaryRule(BaseRule):
    def allow(self, facts: Facts, privileges=None):
        if PrivilegeCode.VIEW_DEBTOR_SUMMARY not in privileges:
            raise PolicyAuthException(error=PolicyError.DEBTOR_SUMMARY_VIEW_NOT_ALLOWED)
        return True
