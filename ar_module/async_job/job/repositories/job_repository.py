import json

from ar_module.async_job.job.aggregates.job_aggregate import JobAggregate
from ar_module.async_job.job.entities.job_entity import JobEntity
from ar_module.async_job.job.job_constants import JobStatus
from ar_module.async_job.job.models import JobModel
from ar_module.infrastructure.database.base_repository import BaseRepository
from object_registry import register_instance


@register_instance()
class JobRepository(BaseRepository):
    def to_entity(self, db_entity: JobModel):
        pass

    @staticmethod
    def to_aggregate(db_entity: JobModel):
        return JobAggregate(
            job_entity=JobEntity(
                job_name=db_entity.job_name,
                data=json.loads(db_entity.data) if db_entity.data else None,
                eta=db_entity.eta,
                generated_at=db_entity.generated_at,
                job_id=db_entity.job_id,
                status=db_entity.status,
                picked_at=db_entity.picked_at,
                failure_message=db_entity.failure_message,
                total_tries=db_entity.total_tries,
                hotel_id=db_entity.hotel_id,
            )
        )

    @staticmethod
    def from_entity(domain_entity: JobEntity):
        # noinspection PyArgumentList
        return JobModel(
            job_name=domain_entity.job_name,
            data=json.dumps(domain_entity.data, default=str)
            if domain_entity.data
            else None,
            eta=domain_entity.eta,
            generated_at=domain_entity.generated_at,
            job_id=domain_entity.job_id,
            status=domain_entity.status,
            picked_at=domain_entity.picked_at,
            failure_message=domain_entity.failure_message,
            total_tries=domain_entity.total_tries,
            hotel_id=domain_entity.hotel_id,
        )

    def get_job_for_execution(self, job_id):
        query = (
            self.session()
            .query(JobModel)
            .filter(JobModel.status.in_([JobStatus.CREATED, JobStatus.RESCHEDULED]))
            .filter(JobModel.job_id == job_id)
        )

        query = query.with_for_update(skip_locked=True)  # Skips rows that are locked
        job = query.first()
        return self.to_aggregate(job) if job else None

    def save(self, job_aggregate: JobAggregate):
        self._save(self.from_entity(job_aggregate.job_entity))

    def update_job(self, job_aggregate: JobAggregate):
        entity = job_aggregate.job_entity
        self._bulk_update_mappings(JobModel, [self.from_entity(entity).mapping_dict()])
        self.flush_session()
