from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_new_debtor_facts import CreateNewDebtorFacts
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CreateNewDebtorRule(BaseRule):
    def allow(self, facts: CreateNewDebtorFacts, privileges=None):
        if PrivilegeCode.CREATE_NEW_DEBTOR not in privileges:
            raise PolicyAuthException(error=PolicyError.CREATE_NEW_DEBTOR_NOT_ALLOWED)

        return True
