from functools import wraps

from treebo_commons.request_tracing.flask import after_request, before_request

from ar_module.core.threadlocal import consumer_context


def consumer_middleware(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        from flask import current_app

        with current_app.test_request_context():
            from flask import request

            # TODO: Any better way to set tenant_id. May be better to move this out of treebo_commons?
            # TODO: In treebo_commons, figure out a better way to set context for background jobs, instead of from
            #  request headers
            request_headers = dict(request.headers)
            request_headers["X-Tenant-Id"] = consumer_context.tenant_id
            before_request(request_headers)
            r_val = func(*args, **kwargs)
            after_request(response=None, request=None)
            return r_val

    return wrapper
