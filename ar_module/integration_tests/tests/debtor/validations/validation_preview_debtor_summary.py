from ar_module.integration_tests.config.sheet_names import (
    PREVIEW_DEBTOR_SUMMARY_SHEET_NAME,
)
from ar_module.integration_tests.utilities.common_utils import assert_
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationPreviewDebtorSummary:
    def __init__(self, response, test_case_id):
        self.response = response
        self.test_data = get_test_case_data(
            PREVIEW_DEBTOR_SUMMARY_SHEET_NAME, test_case_id
        )[0]

    def validate_response(self):
        actual_data = self.response["data"]
        assert_(actual_data["total_credits"], self.test_data["expected_total_credits"])
        assert_(
            actual_data["total_unsettled_debits"],
            self.test_data["expected_total_unsettled_debits"],
        )
        assert_(
            actual_data["total_unused_credits"],
            self.test_data["expected_total_unused_credits"],
        )
