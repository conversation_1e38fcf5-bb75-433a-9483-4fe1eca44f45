from typing import List, Optional

from treebo_commons.money import Money

from ar_module.common.tenant_utils import TenantMixin
from ar_module.domain.constants import Modules


class SettlementDto(object):
    def __init__(
        self, amount: Money, debit_id, tds_settlement_amount: Money, remarks=None
    ):
        self.amount = amount
        self.debit_id = debit_id
        self.tds_settlement_amount = tds_settlement_amount
        self.remarks = remarks


class SettlementDtoV2(SettlementDto):
    def __init__(
        self,
        amount: Money,
        invoice_number,
        payment_id,
        credit_id,
        action,
        remarks,
        tds_settlement_amount: Money = None,
    ):
        super().__init__(amount, None, tds_settlement_amount, remarks)
        self.invoice_number = invoice_number
        self.payment_id = payment_id
        self.credit_id = credit_id
        self.action = action


class CreditDto(TenantMixin):
    def __init__(
        self,
        debtor_id,
        credit_type,
        date,
        amount_in_base_currency: Money,
        reference_number,
        amount_in_credit_currency: Money,
        settlements: List[SettlementDto] = None,
        reference_id=None,
        mode_of_credit=None,
        recorded_via=Modules.AR_MODULE,
        used_to_auto_settle_debit=False,
        debtor_code=None,
        approval_document=None,
        cancellation_date=None,
        tenant_id=None,
    ):
        super().__init__(tenant_id)
        self.debtor_id = debtor_id
        self.credit_type = credit_type
        self.date = date
        self.amount_in_base_currency = amount_in_base_currency
        self.reference_number = reference_number
        self.reference_id = reference_id
        self.mode_of_credit = mode_of_credit
        self.amount_in_credit_currency = amount_in_credit_currency
        self.settlements = settlements
        self.used_to_auto_settle_debit = used_to_auto_settle_debit
        self.debtor_code = debtor_code
        self.approval_document = approval_document
        self.cancellation_date = cancellation_date
        self.recorded_via = recorded_via


class CreditReversalDetailsDto:
    def __init__(
        self,
        payment_credit_id: str,
        amount_in_base_currency: Money,
        amount_in_credit_currency: Money,
        remarks: Optional[str] = None,
    ):
        self.credit_id = payment_credit_id
        self.amount_in_base_currency = amount_in_base_currency
        self.amount_in_credit_currency = amount_in_credit_currency
        self.remarks = remarks


class CreditReversalDto(CreditDto):
    def __init__(
        self,
        credit_reversals: Optional[List[CreditReversalDetailsDto]] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.credit_reversals = credit_reversals if credit_reversals is not None else []


class CreditCancellationDto:
    def __init__(
        self,
        credit_id=None,
        cancellation_reason=None,
    ):
        self.credit_id = credit_id
        self.cancellation_reason = cancellation_reason


class CreditReversalCancellationDto(CreditCancellationDto):
    pass
