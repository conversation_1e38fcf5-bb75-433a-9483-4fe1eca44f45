import csv
import logging
import os
from typing import List

from ths_common.exceptions import InvalidOperationError

logger = logging.getLogger(__name__)


class CsvWriter(object):
    def __init__(self, file_path):
        self.file_path = file_path
        self._file_object = open(self.file_path, "a+")
        self.csv_writer = csv.writer(self._file_object, delimiter=",")
        self._write_header_row = True

    def __enter__(self):
        return self

    def __exit__(self, type, value, traceback):
        self._file_object.close()
        self.tear_down()

    def flush(self):
        self._file_object.flush()

    def write_aggregates(self, aggregates: List, attributes: List):
        """
        write a list of aggregates to csv file
        given the list of attributes to write to column
        """
        if not aggregates:
            raise InvalidOperationError(
                description="Empty aggregate list to generate CSV file"
            )

        rows_for_csv = self._get_data_rows(attributes, aggregates)
        if self._write_header_row:
            header_row = self._get_header_row(attributes, aggregates[0])
            rows_for_csv = [header_row] + rows_for_csv
            self._write_header_row = False

        try:
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
        except Exception:
            raise

        for row in rows_for_csv:
            self.csv_writer.writerow(row)

        self.flush()

    def _get_header_row(self, attributes, aggregate):
        header_row = list()
        for attribute in attributes:
            attrvalue = (
                aggregate[attribute]
                if isinstance(aggregate, dict)
                else getattr(aggregate, attribute)
            )
            if isinstance(attrvalue, dict):
                dict_keys = [
                    attribute + "_" + dict_key for dict_key in attrvalue.keys()
                ]
                header_row.extend(dict_keys)
            else:
                header_row.append(attribute)
        return header_row

    def _get_data_rows(self, attributes, aggregates):
        data_rows = list()
        for aggregate in aggregates:
            data_row = []
            for attribute in attributes:
                attrvalue = (
                    aggregate[attribute]
                    if isinstance(aggregate, dict)
                    else getattr(aggregate, attribute)
                )
                if isinstance(attrvalue, dict):
                    data_row.extend(attrvalue.values())
                else:
                    data_row.append(attrvalue)
            data_rows.append(data_row)

        return data_rows

    def tear_down(self):
        try:
            os.remove(self.file_path)
        except FileNotFoundError:
            logger.info(f"No file found to tear down")
        except OSError as e:
            logger.exception(f"Unable to tear down file due to {e}")
