from ths_common.exceptions import DownstreamSystemFailure
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.core.common.globals import global_context
from ar_module.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
    logger,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from ar_module.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)
from ar_module.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class RoleManagerClient(BaseExternalClient):
    page_map = {
        "get_privilege": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/role-manager/roles/{role_name}/privileges?resource_type={"
            "resource_type}&resource_id={resource_id}",
        )
    }

    def __init__(self):
        super().__init__()

    def get_domain(self):
        return ServiceRegistryClient.get_role_manager_service_url()

    def get_headers(self):
        headers = BaseExternalClient.get_headers()
        if global_context.get_current_seller_id():
            headers["X-Seller-Id"] = global_context.get_current_seller_id()
        return headers

    def get_privilege_by_role_name(self, role_name):
        ar_configured_at_hotel_level = TenantSettings(
            CatalogServiceClient()
        ).is_hotel_level_accounts_receivable_configured()
        if ar_configured_at_hotel_level:
            resource_id = (
                global_context.get_current_seller_id()
                or global_context.get_current_hotel_id()
            )
            resource_type = (
                "seller" if global_context.get_current_seller_id() else "hotel"
            )
        else:
            resource_id = get_current_tenant_id() or TenantClient.get_default_tenant()
            resource_type = "chain"

        page_name = "get_privilege"
        url_params = dict(
            role_name=role_name, resource_type=resource_type, resource_id=resource_id
        )
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            logger.error("Role Manager API failed: %s", response)
            raise DownstreamSystemFailure(
                message="Role Privilege API Error",
                description="Role Privilege API Error Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                ),
                extra_payload=url_params,
            )
        api_response = None
        try:
            api_response = (
                [RolePrivilegesDTO.from_json(r) for r in response.data]
                if response.data
                else []
            )
        except Exception as e:
            error = f"Generic Exception in get role privilege call: {str(e)}"
            logger.exception(error)

        return api_response
