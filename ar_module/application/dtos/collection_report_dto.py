from collections import defaultdict
from typing import List

from treebo_commons.money import Money


class TotalPaymentCreditDto(object):
    def __init__(self, payment_mode, amount):
        self.payment_mode = payment_mode
        self.amount = amount


class PaymentsDto(object):
    def __init__(self, payments=List[TotalPaymentCreditDto]):
        self.payments = payments


class SummarisedCollectionReportDto(object):
    def __init__(
        self,
        mtd_collections,
        debtor_count,
        settled_debits,
        total_payment_credits: List[TotalPaymentCreditDto],
    ):
        self.mtd_collections = mtd_collections
        self.debtor_count = debtor_count
        self.settled_debits = settled_debits
        self.total_payment_credits = total_payment_credits


class DetailedCollectionReportDto(object):
    def __init__(
        self,
        debtor_code,
        debtor_name,
        total_payment_credits,
        settled_debits,
        debtor_type=None,
    ):
        self.debtor_code = debtor_code
        self.debtor_name = debtor_name
        self.debtor_type = debtor_type
        self.total_payment_credits = total_payment_credits
        self.settled_debits = settled_debits


class MonthWiseCollectionSummary(object):
    def __init__(
        self, start_date, end_date, summary, tenant_payment_modes, base_currency
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.summary = summary
        self.tenant_payment_modes = tenant_payment_modes
        self.base_currency = base_currency

    @property
    def debtor_count(self):
        return self.summary.debtor_count

    @property
    def mtd_collections(self):
        return self.summary.mtd_collections

    @property
    def settled_debits(self):
        return self.summary.settled_debits

    @property
    def payment(self):
        payments_group = defaultdict(list)
        for mode in self.tenant_payment_modes:
            payments_group[mode] = Money(0, self.base_currency)

        return self.allocate_payments(
            self.summary.total_payment_credits, payments_group
        )

    @staticmethod
    def allocate_payments(total_payment_credits, payment_group):
        for payment in total_payment_credits:
            if payment_group.get(payment.payment_mode):
                payment_group.get(payment.payment_mode).amount = payment.amount

        return payment_group


class CollectionReportDto(object):
    def __init__(
        self,
        month_wise_summary: List[MonthWiseCollectionSummary],
        detailed,
        summarised_report_download_url,
        detailed_report_download_url,
    ):
        self.month_wise_summary = month_wise_summary
        self.detailed = detailed
        self.summarised_report_download_url = summarised_report_download_url
        self.detailed_report_download_url = detailed_report_download_url
