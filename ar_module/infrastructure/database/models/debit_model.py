from sqlalchemy import <PERSON><PERSON><PERSON>, NUMERIC, Boolean, Column, Date, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from ar_module.infrastructure.database.models.abstract_models import (
    DeleteMixin,
    TimeStampMixin,
)


class DebitModel(Base, TimeStampMixin, DeleteMixin):
    __tablename__ = "debit"

    debit_id = Column(String, primary_key=True)
    debtor_id = Column(String)
    debit_type = Column(String)
    debit_date = Column(Date)
    pretax_amount = Column(NUMERIC)
    tax_amount = Column(NUMERIC)
    posttax_amount = Column(NUMERIC)
    currency = Column(String)
    reference_number = Column(String)
    reference_id = Column(String)
    settlement_status = Column(String)
    unsettled_amount = Column(NUMERIC)
    debit_template_url = Column(String)
    booking_reference_number = Column(String)
    tenant_id = Column(String)
    hotel_id = Column(String)
    bill_to_debtor_code = Column(String)
    due_date = Column(Date)
    remarks = Column(String)
    auto_settled_via_credit = Column(Boolean)
    recorded_via = Column(String)
    invoice_meta = Column(JSON)
