class UserDataDTO:
    def __init__(self, auth_id, email, phone_number, first_name, last_name):
        self.auth_id = auth_id
        self.email = email
        self.phone_number = phone_number
        self.first_name = first_name
        self.last_name = last_name

    @staticmethod
    def from_json(user_data_json):
        return (
            UserDataDTO(
                auth_id=str(user_data_json.get("id")),
                email=user_data_json.get("email"),
                phone_number=user_data_json.get("phone_number"),
                first_name=user_data_json.get("first_name"),
                last_name=user_data_json.get("last_name"),
            )
            if user_data_json
            else None
        )
