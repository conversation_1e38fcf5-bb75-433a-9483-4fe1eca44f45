from contextlib import contextmanager

from treebo_commons.money.constants import CurrencyType
from treebo_commons.service_discovery.service_registry import ServiceRegistry

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.domain.dtos.debtor_config_dto import DebtorConfigDto
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from ar_module.infrastructure.external_clients.core.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ar_module.infrastructure.external_clients.role_privilege.role_privilege_client import (
    RoleManagerClient,
)
from ar_module.infrastructure.external_clients.role_privilege.role_privilege_dto import (
    RolePrivilegesDTO,
)
from ar_module.integration_tests.config import mockers_response
from ar_module.integration_tests.config.mockers_response import (
    accepted_payment_methods_config_value,
    chain_base_currency,
    config_response,
    debtor_config_response,
    enum_response,
    get_role_privilege_response,
    property_response,
)


@contextmanager
def mock_get_tenant_configs(config_value):
    real_get_setting_value = (
        CatalogServiceClient.is_hotel_level_accounts_receivable_configured
    )
    real_fetch_property = CatalogServiceClient.fetch_property
    real_get_ar_config = CatalogServiceClient.get_ar_config
    real_get_tenant_config = CatalogServiceClient.get_tenant_configs

    def mock_get_is_hotel_level_accounts_receivable_configured(self):
        nonlocal config_value
        if config_value:
            return mockers_response
        else:
            return {}

    def mock_get_ar_config(self, hotel_id=None):
        return config_response

    def mock_fetch_property(self, hotel_id):
        return property_response

    def mock_get_tenant_config(self, hotel_id):
        return []

    CatalogServiceClient.is_hotel_level_accounts_receivable_configured = (
        mock_get_is_hotel_level_accounts_receivable_configured
    )
    CatalogServiceClient.fetch_property = mock_fetch_property
    CatalogServiceClient.get_ar_config = mock_get_ar_config
    CatalogServiceClient.get_tenant_configs = mock_get_tenant_config
    yield
    CatalogServiceClient.is_hotel_level_accounts_receivable_configured = (
        real_get_setting_value
    )
    CatalogServiceClient.fetch_property = real_fetch_property
    CatalogServiceClient.get_ar_config = real_get_ar_config
    CatalogServiceClient.get_tenant_configs = real_get_tenant_config


@contextmanager
def mock_get_roles():
    real_get_privilege_by_role_name = RoleManagerClient.get_privilege_by_role_name

    def mock_get_privilege_by_role_name(self, role_name):
        role_and_privilege_mapping = {
            role["role"]: role for role in get_role_privilege_response
        }
        privileges_by_role = role_and_privilege_mapping.get(role_name, dict()).get(
            "privileges", None
        )
        return (
            [RolePrivilegesDTO.from_json(r) for r in privileges_by_role]
            if privileges_by_role
            else None
        )

    RoleManagerClient.get_privilege_by_role_name = mock_get_privilege_by_role_name
    yield
    RoleManagerClient.get_privilege_by_role_name = real_get_privilege_by_role_name


@contextmanager
def mock_is_hotel_level_accounts_receivable_configured(config_value):
    real_hotel_level_accounts_receivable_configured = (
        TenantSettings.is_hotel_level_accounts_receivable_configured
    )

    def mock_get_is_hotel_level_accounts_receivable_configured(self):
        return config_value

    TenantSettings.is_hotel_level_accounts_receivable_configured = (
        mock_get_is_hotel_level_accounts_receivable_configured
    )
    yield
    TenantSettings.is_hotel_level_accounts_receivable_configured = (
        real_hotel_level_accounts_receivable_configured
    )


@contextmanager
def mock_payment_modes():
    real_get_payment_modes = TenantSettings.get_payment_modes

    def mock_get_payment_modes(self):
        payment_modes = accepted_payment_methods_config_value
        return {mode["value"] for mode in payment_modes or []}

    TenantSettings.get_payment_modes = mock_get_payment_modes
    yield
    TenantSettings.get_payment_modes = real_get_payment_modes


@contextmanager
def mock_debtor_config():
    real_debtor_config = TenantSettings.get_debtor_configs

    def mock_get_debtor_config(self):
        debtor_configs = debtor_config_response
        return {
            config["debtor_code"]: DebtorConfigDto(**config)
            for config in debtor_configs or []
        }

    TenantSettings.get_debtor_configs = mock_get_debtor_config
    yield
    TenantSettings.get_debtor_configs = real_debtor_config


@contextmanager
def mock_chain_base_currency():
    real_chain_base_currency = TenantSettings.get_chain_base_currency

    def mock_get_chain_base_currency(self):
        return CurrencyType(chain_base_currency.upper())

    TenantSettings.get_chain_base_currency = mock_get_chain_base_currency
    yield
    TenantSettings.get_chain_base_currency = real_chain_base_currency


@contextmanager
def mock_applicable_roles_for_payment_mode(pay_mode):
    real_applicable_roles_for_payment_mode = (
        TenantSettings.get_applicable_roles_for_payment_mode
    )

    def mock_get_applicable_roles_for_payment_modes(self, payment_mode):
        payment_modes = accepted_payment_methods_config_value
        return next(
            (
                mode.get("applicable_roles", [])
                for mode in payment_modes
                if mode["value"] == pay_mode
            ),
            [],
        )

    TenantSettings.get_applicable_roles_for_payment_mode = (
        mock_get_applicable_roles_for_payment_modes
    )
    yield
    TenantSettings.get_applicable_roles_for_payment_mode = (
        real_applicable_roles_for_payment_mode
    )


@contextmanager
def mock_presigned_url_from_s3_url():
    real_presigned_url_from_s3_url = AwsServiceClient.get_presigned_url_from_s3_url

    def mock_get_presigned_url_from_s3_url(cls, s3_url: str, link_expires_in=None):
        return "mock-approval-document-url"

    AwsServiceClient.get_presigned_url_from_s3_url = mock_get_presigned_url_from_s3_url
    yield
    AwsServiceClient.get_presigned_url_from_s3_url = real_presigned_url_from_s3_url


@contextmanager
def mock_catalog_service_client():
    real_catalog_service_enums = CatalogServiceClient.get_enums

    def mock_get_catalog_service_client(cls, enum_names: str):
        return enum_response

    CatalogServiceClient.get_enums = mock_get_catalog_service_client
    yield
    CatalogServiceClient.get_enums = real_catalog_service_enums


@contextmanager
def mock_get_poc_details():
    real_get_poc_details = CompanyProfileServiceClient.get_poc_details

    def mock_get_poc_details(cls, *args, **kwargs):
        return []

    CompanyProfileServiceClient.get_poc_details = mock_get_poc_details
    yield
    CompanyProfileServiceClient.get_poc_details = real_get_poc_details
