from treebo_commons.money import Money

from ar_module.domain.entities.base_entity import EntityChangeTracker


class Settlement(EntityChangeTracker):
    def __init__(
        self,
        settlement_id,
        settled_via,
        reference_number,
        amount: Money,
        credit_id,
        settlement_date,
        debit_id=None,
        dirty=True,
        new=True,
        deleted=False,
        remarks=None,
    ):
        super().__init__(dirty=dirty, new=new)
        self.settlement_id = settlement_id
        self.settled_via = settled_via
        self.reference_number = reference_number
        self.amount = amount
        self.credit_id = credit_id
        self.debit_id = debit_id
        self.settlement_date = settlement_date
        self.deleted = deleted
        self.remarks = remarks

    def delete(self):
        self.deleted = True
        self.mark_dirty()
