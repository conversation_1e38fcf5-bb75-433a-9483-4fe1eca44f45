from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from ar_module.domain.entities.settlement import Settlement
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.settlement_model import SettlementModel


class SettlementAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Settlement, **kwargs):
        # noinspection PyArgumentList
        return SettlementModel(
            debit_id=kwargs.get("debit_id"),
            settlement_id=domain_entity.settlement_id,
            settled_via=domain_entity.settled_via,
            reference_number=domain_entity.reference_number,
            amount=domain_entity.amount.amount,
            currency=domain_entity.amount.currency.value,
            credit_id=domain_entity.credit_id,
            settlement_date=domain_entity.settlement_date,
            deleted=domain_entity.deleted,
            remarks=domain_entity.remarks,
        )

    def to_domain_entity(self, db_entity: SettlementModel, **kwargs):
        return Settlement(
            settlement_id=db_entity.settlement_id,
            settled_via=db_entity.settled_via,
            reference_number=db_entity.reference_number,
            debit_id=db_entity.debit_id,
            amount=Money(db_entity.amount, CurrencyType(db_entity.currency)),
            credit_id=db_entity.credit_id,
            settlement_date=db_entity.settlement_date,
            dirty=False,
            new=False,
            deleted=db_entity.deleted,
            remarks=db_entity.remarks,
        )
