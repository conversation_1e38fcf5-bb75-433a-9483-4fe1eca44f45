import datetime
import os
import uuid
from collections import OrderedDict, defaultdict
from pathlib import Path

from treebo_commons.utils import dateutils

from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.reporting.dtos import (
    DebitLineItemDto,
    DebtorSummaryRequestData,
)
from ar_module.application.services.reporting.excel_writer import ExcelWriter
from ar_module.domain.constants import AuditType, CreditType, SettlementStatus
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery, OverDueDateFilter
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

REPORT_PATH = "ar-reports/"


@register_instance(
    dependencies=[
        DebitRepository,
        CreditRepository,
        DebtorRepository,
        CommunicationServiceClient,
        AuditTrailService,
    ]
)
class DebitOverDueReminderDispatcher:
    debits_report_column_mapping = OrderedDict(
        dict(
            debit_date="Invoice Date",
            reference_number="Invoice Number",
            posttax_amount="Invoice Amount",
            unsettled_amount="Pending Amount",
            booking_reference_number="Booking Id",
            due_date="Due Date",
            pretax_amount="Pre Tax",
            tax_amount="Tax",
            guest_name="Guest Name",
            hotel_name="Hotel Name",
            checkin="Check In",
            checkout="Check Out",
        )
    )

    email_identifier = "ar_payment_overdue"

    def __init__(
        self,
        debit_repo: DebitRepository,
        credit_repo: CreditRepository,
        debtor_repo: DebtorRepository,
        communication_service_client: CommunicationServiceClient,
        audit_trail_service: AuditTrailService,
    ):
        self.debit_repo = debit_repo
        self.credit_repo = credit_repo
        self.debtor_repo = debtor_repo
        self.communication_service_client = communication_service_client
        self.audit_trail_service = audit_trail_service

    def dispatch(self, request: DebtorSummaryRequestData):
        debtor = self.debtor_repo.load(request.debtor_id)
        name = debtor.debtor_name
        report_file_path = f'{os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/")}{self._generate_report_filename(name)}'
        debit_search_query = DebitSearchQuery(
            debtor_id=request.debtor_id,
            settlement_status=[
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ],
        )

        credit_search_query = CreditSearchQuery(
            debtor_id=request.debtor_id,
            has_unused_credit=True,
            show_cancelled_credits=False,
            credit_types=[CreditType.PAYMENT, CreditType.TDS, CreditType.CREDIT_NOTE],
        )
        total_unused_credits = self.credit_repo.total_unused_credits(
            credit_search_query
        )
        total_unsettled_amount = self.debit_repo.total_unsettled_amount(
            debit_search_query
        )

        over_due_date_filter = None
        if request.from_date or request.to_date:
            over_due_date_filter = OverDueDateFilter(
                from_date=request.from_date, to_date=request.to_date
            )
        debit_search_query_for_over_due_invoices = DebitSearchQuery(
            only_over_due_invoice=True,
            debtor_id=request.debtor_id,
            over_due_date_filter=over_due_date_filter,
            settlement_status=[
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ],
        )

        over_due_debits = self.debit_repo.search_debits(
            debit_search_query_for_over_due_invoices, debits_only=True
        )
        over_due_debits = [
            DebitLineItemDto.create_from_debit_data(debit) for debit in over_due_debits
        ]

        debit_search_query_for_non_over_due_invoices = DebitSearchQuery(
            only_non_over_due_invoice=True,
            debtor_id=request.debtor_id,
            settlement_status=[
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ],
        )
        non_over_due_debits = self.debit_repo.search_debits(
            debit_search_query_for_non_over_due_invoices, debits_only=True
        )
        invoice_stats = self._get_stats_for_given_debits(
            over_due_debits, non_over_due_debits
        )
        over_due_amount = sum(debit.unsettled_amount for debit in over_due_debits)

        with ExcelWriter(report_file_path) as excel_writer:
            if over_due_debits:
                excel_writer.write_aggregates(
                    over_due_debits,
                    self.debits_report_column_mapping,
                    sheet_name="Invoice",
                ).save_file()
            signed_url_of_over_due_reports = (
                (
                    AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                        REPORT_PATH,
                        excel_writer.file_path,
                        self.get_default_expiration_time(),
                    )
                )
                if over_due_debits
                else None
            )
        self._send_email_communication(
            request,
            debtor,
            signed_url_of_over_due_reports,
            total_unused_credits if total_unused_credits else 0.0,
            total_unsettled_amount if total_unsettled_amount else 0.0,
            over_due_amount,
            invoice_stats,
            report_file_path,
        )
        self.audit_trail_service.create_debtor_communication_audit_trail(
            self.email_identifier,
            debtor,
            AuditType.DEBTOR_COMMUNICATION,
            request.user_data,
        )

    @staticmethod
    def _generate_report_filename(name):
        return f"{name}_Follow_up_for_Overdue_Report.xlsx"

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def _send_email_communication(
        self,
        request: DebtorSummaryRequestData,
        debtor,
        signed_url_of_over_due_reports,
        total_unused_credits,
        total_unsettled_amount,
        over_due_amount,
        invoice_stats,
        report_file_path,
    ):
        subject = f"{debtor.debtor_code} - {debtor.debtor_name} - Payment Overdue"
        context_data = dict(
            customer_name=debtor.debtor_name,
            from_date=request.from_date,
            to_date=request.to_date,
            report_date=str(datetime.datetime.today()),
            total_unused_credits=total_unused_credits,
            total_unsettled_amount=total_unsettled_amount,
            over_due_amount=over_due_amount,
            invoice_stats=invoice_stats,
        )
        attachments = [
            dict(
                url=signed_url_of_over_due_reports,
                filename=Path(report_file_path).name,
            )
        ]
        self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=request.to_recipients,
            cc=request.cc_recipients,
            subject=subject,
            attachments=attachments,
        )

    @staticmethod
    def _get_stats_for_given_debits(over_due_debits, non_over_due_debits):
        current_date = dateutils.today().date()
        stats = defaultdict(lambda: {"amount": 0, "count": 0})
        for debit in over_due_debits:
            if current_date >= debit.due_date > dateutils.subtract(current_date, 30):
                stats["invoices_due_in_last_one_month"][
                    "amount"
                ] += debit.unsettled_amount
                stats["invoices_due_in_last_one_month"]["count"] += 1
            elif (
                dateutils.subtract(current_date, 30)
                >= debit.due_date
                > dateutils.subtract(current_date, 60)
            ):
                stats["invoices_due_in_range_31_60_days"][
                    "amount"
                ] += debit.unsettled_amount
                stats["invoices_due_in_range_31_60_days"]["count"] += 1
            elif (
                dateutils.subtract(current_date, 60)
                >= debit.due_date
                > dateutils.subtract(current_date, 90)
            ):
                stats["invoices_due_in_range_61_90_days"][
                    "amount"
                ] += debit.unsettled_amount
                stats["invoices_due_in_range_61_90_days"]["count"] += 1
            else:
                stats["invoices_due_in_range_more_than_90"][
                    "amount"
                ] += debit.unsettled_amount
                stats["invoices_due_in_range_more_than_90"]["count"] += 1
        return dict(
            non_due_invoices=dict(
                amount=sum(debit.unsettled_amount for debit in non_over_due_debits),
                count=len(non_over_due_debits),
            ),
            due_in_range_0_30=stats["invoices_due_in_last_one_month"],
            due_in_range_31_60=stats["invoices_due_in_range_31_60_days"],
            due_in_range_61_90=stats["invoices_due_in_range_61_90_days"],
            due_in_range_more_than_90=stats["invoices_due_in_range_more_than_90"],
        )
