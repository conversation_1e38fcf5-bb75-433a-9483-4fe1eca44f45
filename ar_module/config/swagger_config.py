import inspect

import marshmallow
from apispec import APISpec
from flasgger import Swagger

from ar_module.api import schemas
from ar_module.api.audit_trail import get_audit_trails
from ar_module.api.credit import (
    bulk_upload_cancel_credit_reversals,
    bulk_upload_cancel_credits,
    bulk_upload_credit_reversals,
    bulk_upload_credits,
    cancel_credit,
    cancel_credit_reversal,
    create_credit_reversal,
    create_new_credit,
    get_debtor_credits,
)
from ar_module.api.data_ingestors import bulk_ingest_payments
from ar_module.api.debit import create_new_debit, get_debits, get_settlements
from ar_module.api.debtor import create_new_debtor, search_debtors
from ar_module.api.reports import (
    get_collection_reports,
    get_collection_reports_by_credit_date,
    get_collection_reports_for_multiple_date_range,
    get_debtor_ledger_report,
    get_receivable_reports,
    preview_debtor_summary,
    pull_payment_data,
    push_payments_report_to_erp,
    schedule_debtor_summary_report_generation,
)
from ar_module.api.settlements import (
    bulk_upload_settlements,
    create_settlements,
    fetch_settlements_for_invoice,
)


def setup_schema_definition(spec):
    for name, obj in inspect.getmembers(schemas):
        if inspect.isclass(obj) and type(obj) == marshmallow.schema.SchemaMeta:
            spec.definition(name, schema=obj)


def setup_path(spec):
    spec.add_path(view=create_new_credit)
    spec.add_path(view=search_debtors)
    spec.add_path(view=create_new_debit)
    spec.add_path(view=get_debtor_credits)
    spec.add_path(view=cancel_credit)
    spec.add_path(view=bulk_upload_credits)
    spec.add_path(view=get_debits)
    spec.add_path(view=create_new_debtor)
    spec.add_path(view=create_settlements)
    spec.add_path(view=get_settlements)
    spec.add_path(view=fetch_settlements_for_invoice)
    spec.add_path(view=bulk_upload_settlements)
    spec.add_path(view=get_collection_reports)
    spec.add_path(view=get_receivable_reports)
    spec.add_path(view=get_debtor_ledger_report)
    spec.add_path(view=get_collection_reports_for_multiple_date_range)
    spec.add_path(view=get_collection_reports_by_credit_date)
    spec.add_path(view=get_audit_trails)
    spec.add_path(view=schedule_debtor_summary_report_generation)
    spec.add_path(view=preview_debtor_summary)
    spec.add_path(view=bulk_ingest_payments)
    spec.add_path(view=push_payments_report_to_erp)
    spec.add_path(view=pull_payment_data)
    spec.add_path(view=create_credit_reversal)
    spec.add_path(view=cancel_credit_reversal)
    spec.add_path(view=bulk_upload_credit_reversals)
    spec.add_path(view=bulk_upload_cancel_credits)
    spec.add_path(view=bulk_upload_cancel_credit_reversals)


def init_swagger_docs(app, url_prefix):
    ctx = app.test_request_context()
    ctx.push()

    # Create an APISpec
    spec = APISpec(
        title="Swagger API Doc",
        version="1.0.0",
        plugins=[
            "apispec.ext.flask",
            "apispec.ext.marshmallow",
        ],
    )

    config = Swagger.DEFAULT_CONFIG.copy()
    config["specs_route"] = url_prefix + "/apidocs/"
    config["static_url_path"] = url_prefix + "/flassger_static"
    config["specs"][0]["route"] = url_prefix + "/apispec_1.json"
    config["openapi"] = "3.0.2"

    setup_schema_definition(spec)
    setup_path(spec)
    sw = Swagger(template=spec.to_dict(), config=config)
    sw.init_app(app)
