import json

from ar_module.application.consumers.constants import DebtorTypes
from ar_module.integration_tests.mockers import (
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
)


def create_debtor(
    client_, create_debtor_payload, debtor_type=DebtorTypes.B2B, config_value=True
):
    url = "/ar/v1/debtors"
    _payload = json.loads(create_debtor_payload)
    _payload["debtor_type"] = debtor_type
    payload = json.dumps({"data": _payload})
    with mock_get_tenant_configs(
        config_value
    ), mock_is_hotel_level_accounts_receivable_configured(config_value):
        with mock_get_roles():
            response = client_.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )
    assert response.status_code == 200
    debtor_response = json.loads(response.data.decode("utf-8"))
    assert len(debtor_response["data"]) > 0
    debtor_id = debtor_response["data"]["debtor"]["debtor_id"]

    return debtor_id
