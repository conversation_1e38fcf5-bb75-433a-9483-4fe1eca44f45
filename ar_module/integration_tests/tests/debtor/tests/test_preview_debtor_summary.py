import pytest

from ar_module.integration_tests.config.common_config import (
    CREATE_CREDIT,
    CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
    CREATE_CREDIT_WITH_SETTLEMENT,
    CREATE_DEBTOR,
    CREATE_DEBTOR_WITH_CREDIT,
    CREATE_DEBTOR_WITH_MULTIPLE_DEBITS_AND_CREDITS,
    CREATE_DEBTOR_WITH_MULTIPLE_SETTLEMENTS,
    CREATE_DEBTOR_WITH_PARTIAL_SETTLEMENT,
    CREATE_SINGLE_DEBIT,
    ERROR_CODES,
    FDM,
    MULTIPLE_CREDITS_INCLUDING_ONE_CANCELLED_CREDIT,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.debtor.validations.validation_preview_debtor_summary import (
    ValidationPreviewDebtorSummary,
)
from ar_module.integration_tests.utilities.common_utils import return_date


class TestPreviewDebtorSummary(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message, extras",
        [
            (
                "PreviewDebtorSummary_01",
                CREATE_DEBTOR,
                "Preview debtor summary of a debtor without any credit or debit",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_02",
                CREATE_SINGLE_DEBIT,
                "Preview debtor summary of a debtor with only debit",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_03",
                CREATE_CREDIT,
                "Preview debtor summary of a debtor with only credit",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_04",
                CREATE_DEBTOR_WITH_CREDIT,
                "Preview debtor summary of a debtor with a debit and a credit",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_05",
                CREATE_CREDIT_WITH_SETTLEMENT,
                "Preview debtor summary of a debtor with a fully mapped debit and credit",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_06",
                CREATE_DEBTOR_WITH_CREDIT,
                "Preview debtor summary of a debtor with a debit and a credit as an FDM",
                403,
                FDM,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_07",
                CREATE_DEBTOR_WITH_CREDIT,
                "Preview debtor summary of a invalid debtor",
                403,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_08",
                MULTIPLE_CREDITS_INCLUDING_ONE_CANCELLED_CREDIT,
                "Preview debtor summary of a debtor with multiple credits including cancelled credits",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_09",
                CREATE_DEBTOR_WITH_PARTIAL_SETTLEMENT,
                "Preview debtor summary of a debtor with debits and a credit where a debit is partially settled",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_10",
                CREATE_CREDIT_WITH_MULTIPLE_SETTLEMENT,
                "Preview debtor summary of a debtor with multiple settlements",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_11",
                CREATE_DEBTOR_WITH_MULTIPLE_SETTLEMENTS,
                "Preview debtor summary of a debtor with multiple credits, debits and settlements",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_12",
                CREATE_DEBTOR_WITH_MULTIPLE_SETTLEMENTS,
                "Preview debtor summary of a debtor with multiple credits, debits and settlements for different dates",
                200,
                SUPER_ADMIN,
                None,
                [1, 2],
            ),
            (
                "PreviewDebtorSummary_13",
                CREATE_DEBTOR_WITH_MULTIPLE_DEBITS_AND_CREDITS,
                "Preview debtor summary of a debtor debits and credits of multiple days",
                200,
                SUPER_ADMIN,
                None,
                [-1, 1],
            ),
            (
                "PreviewDebtorSummary_14",
                CREATE_DEBTOR_WITH_MULTIPLE_DEBITS_AND_CREDITS,
                "Preview debtor summary of a debtor debits and credits of multiple days from current date",
                200,
                SUPER_ADMIN,
                None,
                [0, 1],
            ),
        ],
    )
    @pytest.mark.regression
    def test_preview_debtor_summary(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
        extras,
    ):

        if skip_message:
            pytest.skip(skip_message)

        self.debit_request.debit_id.clear()
        from_date = str(return_date(extras[0]))
        to_date = str(return_date(extras[1]))

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        debtor_id = (
            "abc123"
            if test_case_id == "PreviewDebtorSummary_07"
            else self.debtor_request.debtor_id
        )

        response = self.debtor_request.preview_debtor_summary_request(
            client_,
            status_code,
            user_type,
            debtor_id,
            from_date,
            to_date,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id):
        validation = ValidationPreviewDebtorSummary(response, test_case_id)
        validation.validate_response()
