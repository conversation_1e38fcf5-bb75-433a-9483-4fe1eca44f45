import datetime
import os
import uuid
from collections import OrderedDict
from pathlib import Path

from treebo_commons.utils import dateutils

from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.reporting.dtos import (
    DebitLineItemDto,
    DebtorSummaryRequestData,
)
from ar_module.application.services.reporting.excel_writer import ExcelWriter
from ar_module.domain.constants import AuditType, CreditType, SettlementStatus
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.communication_service_client import (
    CommunicationServiceClient,
)
from object_registry import register_instance

OUTSTANDING_DUE_S3_PATH = "ar-reports/"
MAX_FILE_SIZ_FOR_ATTACHMENT = 24


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        CommunicationServiceClient,
        AuditTrailService,
    ]
)
class OutStandingDuesReminderDispatcher:
    debits_report_column_mapping = OrderedDict(
        dict(
            debit_date="Invoice Date",
            reference_number="Invoice Number",
            posttax_amount="Invoice Amount",
            unsettled_amount="Pending Amount",
            booking_reference_number="Booking Id",
            due_date="Due Date",
            pretax_amount="Pre Tax",
            tax_amount="Tax",
            guest_name="Guest Name",
            hotel_name="Hotel Name",
            checkin="Check In",
            checkout="Check Out",
        )
    )

    credits_report_column_mapping = OrderedDict(
        dict(
            credit_date="Payment Date",
            reference_number="Payment Id",
            amount_in_base_currency="Payment Amount",
            unused_credit_amount="Unused Amount",
            mode_of_credit="Mode of Payment",
        )
    )

    email_identifier = "ar_outstanding_dues"

    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        communication_service_client: CommunicationServiceClient,
        audit_trail_service: AuditTrailService,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.communication_service_client = communication_service_client
        self.audit_trail_service = audit_trail_service

    def dispatch(self, request: DebtorSummaryRequestData):
        debtor = self.debtor_repo.load(request.debtor_id)
        name = debtor.debtor_name
        outstanding_report_file_path = f'{os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/")}{self._generate_report_filename(name)}'
        debit_search_query = DebitSearchQuery(
            debtor_id=request.debtor_id,
            to_date=dateutils.current_date(),
        )
        credit_search_query = CreditSearchQuery(
            debtor_id=request.debtor_id,
            to_date=dateutils.current_date(),
            show_cancelled_credits=False,
            credit_types=[CreditType.PAYMENT, CreditType.TDS, CreditType.CREDIT_NOTE],
        )

        if not request.include_mapped_records:
            debit_search_query.settlement_status = [
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ]
            credit_search_query.has_unused_credit = True

        debits = self.debit_repo.search_debits(debit_search_query, debits_only=True)
        debits = [DebitLineItemDto.create_from_debit_data(debit) for debit in debits]
        credit_items = self.credit_repo.load_credits(
            credit_search_query, credits_only=True
        )

        total_open_balance = sum(debit.unsettled_amount for debit in debits)
        total_unmapped_amount = sum(
            credit.unused_credit_amount for credit in credit_items
        )
        net_payable = total_open_balance - total_unmapped_amount

        with ExcelWriter(outstanding_report_file_path) as excel_writer:
            if debits:
                excel_writer.write_aggregates(
                    debits, self.debits_report_column_mapping, sheet_name="Invoice"
                )
            if credit_items:
                excel_writer.write_aggregates(
                    credit_items,
                    self.credits_report_column_mapping,
                    sheet_name="Payments",
                )
            if not debits and credit_items:
                signed_url_of_outstanding_reports = None
            else:
                excel_writer.save_file()
                signed_url_of_outstanding_reports = (
                    AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                        OUTSTANDING_DUE_S3_PATH,
                        excel_writer.file_path,
                        self.get_default_expiration_time(),
                    )
                )
        self._send_email_communication(
            request,
            debtor,
            signed_url_of_outstanding_reports,
            net_payable,
            total_open_balance,
            total_unmapped_amount,
            outstanding_report_file_path,
        )
        self.audit_trail_service.create_debtor_communication_audit_trail(
            self.email_identifier,
            debtor,
            AuditType.DEBTOR_COMMUNICATION,
            request.user_data,
        )

    @staticmethod
    def _generate_report_filename(name):
        return f"{name}_Outstanding_Dues_Report.xlsx"

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def _send_email_communication(
        self,
        request: DebtorSummaryRequestData,
        debtor,
        outstanding_due_report_url,
        net_payable,
        total_open_balance,
        total_unmapped_amount,
        outstanding_report_file_path,
    ):
        subject = f"{debtor.debtor_code} - {debtor.debtor_name} - Outstanding Dues - Till {dateutils.current_date().strftime('%d %B %Y')}"
        context_data = dict(
            customer_name=debtor.debtor_name,
            report_date=str(datetime.datetime.today()),
            net_payable=net_payable,
            total_open_balance=total_open_balance,
            total_unmapped_amount=total_unmapped_amount,
        )
        attachments = [
            dict(
                url=outstanding_due_report_url,
                filename=Path(outstanding_report_file_path).name,
            )
        ]
        self.communication_service_client.send_email(
            identifier=self.email_identifier,
            context_data=context_data,
            to_emails=request.to_recipients,
            cc=request.cc_recipients,
            subject=subject,
            attachments=attachments,
        )
