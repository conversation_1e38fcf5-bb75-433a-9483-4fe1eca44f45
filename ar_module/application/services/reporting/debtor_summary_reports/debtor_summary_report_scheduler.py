import logging

from marshmallow import ValidationError

from ar_module.application.consumers.constants import POCsDesignation
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.reporting.dtos import DebtorSummaryRequestDto
from ar_module.core.common.globals import global_context
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.facts import Facts
from ar_module.exceptions import PolicyAuthException
from ar_module.infrastructure.db_transaction import unit_of_work
from ar_module.infrastructure.external_clients.core.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from object_registry import locate_instance, register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[DebtorService, CompanyProfileServiceClient, TenantSettings]
)
class DebtorSummaryReportScheduler(object):
    def __init__(
        self,
        debtor_service: DebtorService,
        company_profile_service_client: CompanyProfileServiceClient,
        tenant_settings: TenantSettings,
    ):
        self.debtor_service = debtor_service
        self.company_profile_service_client = company_profile_service_client
        self.tenant_settings = tenant_settings

    @unit_of_work
    def schedule(self, request_data: DebtorSummaryRequestDto, user_data=None):
        RuleEngine.action_allowed(
            action="view_debtor_summary",
            facts=Facts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        from ar_module.async_job.job_scheduler_service import JobSchedulerService

        query = DebtorSearchQuery(
            debtor_id=request_data.report_request_data.debtor_id,
            hotel_id=user_data.hotel_id,
        )
        debtors, _ = self.debtor_service.get_debtors(query, user_data=user_data)
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.DEBTOR_SUMMARY_VIEW_NOT_ALLOWED,
                description="Not authorised to view given debtor",
            )
        all_pocs_of_debtor = self.company_profile_service_client.get_poc_details(
            user_data.hotel_id, debtors[0].debtor_code
        )
        self._populate_report_recipients_in_payload(request_data, all_pocs_of_debtor)
        request_data.report_request_data.user_data = user_data
        job_scheduler: JobSchedulerService = locate_instance(JobSchedulerService)
        job_aggregate = job_scheduler.create_debtor_summary_report_jobs(
            user_data.hotel_id, request_data
        )
        return job_aggregate.job_id

    @staticmethod
    def _populate_report_recipients_in_payload(request_data, all_pocs_of_debtor):
        emails_of_finance_pocs = []
        emails_of_internal_team = []
        for poc in all_pocs_of_debtor:
            if poc.designation in (
                POCsDesignation.BOOKING_POC,
                POCsDesignation.FINANCE_ADMIN,
            ):
                emails_of_finance_pocs.extend(poc.email_ids or [])
            elif poc.designation in (
                POCsDesignation.FINANCE_POC,
                POCsDesignation.SALES_POC,
            ):
                emails_of_internal_team.extend(poc.email_ids or [])
        request_data.report_request_data.to_recipients = emails_of_finance_pocs
        if request_data.report_request_data.additional_to_recipients:
            additional_to_recipients_mails = (
                request_data.report_request_data.additional_to_recipients.split(",")
            )
            request_data.report_request_data.to_recipients += (
                additional_to_recipients_mails
            )

        if not request_data.report_request_data.to_recipients:
            raise ValidationError(
                "Unable find pocs to send email report for this debor"
            )

        request_data.report_request_data.cc_recipients = emails_of_internal_team
        if request_data.report_request_data.additional_cc_recipients:
            additional_cc_recipients_mails = (
                request_data.report_request_data.additional_cc_recipients.split(",")
            )
            request_data.report_request_data.cc_recipients += (
                additional_cc_recipients_mails
            )
