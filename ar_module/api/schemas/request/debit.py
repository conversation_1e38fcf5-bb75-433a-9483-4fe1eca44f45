from flask import request
from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas.validators import validate_positive_integer
from ar_module.domain.constants import SortOrders
from ar_module.domain.dtos.debit_search_query import DebitSearchQuery
from ar_module.domain.value_objects.amount import Amount


class AmountSchema(Schema):
    pretax_amount = MoneyField(required=True, validate=validate_positive_integer)
    tax_amount = MoneyField(required=True)
    posttax_amount = MoneyField(required=True, validate=validate_positive_integer)

    @post_load
    def create_object(self, data):
        return Amount(
            pretax_amount=data.get("pretax_amount"),
            tax_amount=data.get("tax_amount"),
            posttax_amount=data.get("posttax_amount"),
        )

    @validates_schema(skip_on_field_errors=True)
    def validate_data(self, data):
        if data.get("pretax_amount"):
            if data["pretax_amount"] > data["posttax_amount"]:
                raise ValidationError(
                    "Pretax amount cannot be greater than posttax amount",
                    field_names=["pretax_amount"],
                )

        if data.get("tax_amount"):
            if data["tax_amount"] > data["posttax_amount"]:
                raise ValidationError(
                    "Tax amount cannot be greater than posttax amount",
                    field_names=["tax_amount"],
                )


class DebitRequestSchema(Schema):
    debtor_id = fields.String(required=True)
    debit_date = fields.Date(required=True)
    debit_amount = fields.Nested(AmountSchema, required=True)
    reference_number = fields.String(required=True)
    debit_type = fields.String(required=False)


class DebitSearchSchema(Schema):
    has_unsettled_amount = fields.Boolean(required=False)
    include_cancelled = fields.Boolean(default=False)
    debtor_id = fields.String(required=False)
    reference_id = fields.String(required=False)
    reference_numbers = fields.String(required=False)
    debit_date = fields.Date(required=False)
    hotel_id = fields.String(required=False)
    only_manual_debits = fields.Boolean(required=False)
    auto_settled_via_credit = fields.Boolean(
        required=False, description="Filter debits based on auto_settled_via_credit"
    )
    debit_ids = fields.String(
        description="Comma separated list of debit ids to be searched", allow_none=True
    )
    from_date = fields.Date(required=False)
    to_date = fields.Date(required=False)
    limit = fields.Integer(description="Number of results required", allow_none=True)
    offset = fields.Integer(
        description="Offset from which results are required", allow_none=True
    )
    sort_order = fields.String(
        validate=OneOf(
            SortOrders.all(),
            error="Sort Order is not valid. Allowed values are 'asc' and 'desc'",
        ),
        description="Sort order for the results. Allowed values are 'asc' and 'desc'",
        allow_none=True,
    )

    @post_load
    def create_search_dto(self, data, **kwargs):
        if data.get("debit_ids"):
            data["debit_ids"] = data["debit_ids"].split(",")
        return DebitSearchQuery(**data)

    @validates_schema
    def validate_data(self, data):
        if (
            request.headers.get("X-Hotel-Id")
            and data.get("hotel_id")
            and request.headers["X-Hotel-Id"] != data["hotel_id"]
        ):
            raise ValidationError("Hotel id is different than the one sent in headers")

        if data.get("debit_date"):
            if data.get("from_date") and data.get("to_date"):
                raise ValidationError(
                    "Date range search and single date search cannot be supported together "
                )

        if bool(data.get("from_date")) ^ bool(data.get("to_date")):
            raise ValidationError(
                "Date Range search should contain both from_date and to_date"
            )

        if data.get("reference_numbers"):
            reference_numbers = data["reference_numbers"].split(",")
            if len(reference_numbers) > 100:
                raise ValidationError(
                    "A maximum of 100 reference numbers can be provided"
                )
