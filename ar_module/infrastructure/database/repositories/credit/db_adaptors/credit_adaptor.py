from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from ar_module.domain.entities.credit import Credit
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.credit_model import CreditModel


class CreditAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Credit, **kwargs):
        # noinspection PyArgumentList
        return CreditModel(
            credit_id=domain_entity.credit_id,
            debtor_id=domain_entity.debtor_id,
            credit_type=domain_entity.credit_type,
            credit_date=domain_entity.date,
            reference_number=domain_entity.reference_number,
            reference_id=domain_entity.reference_id,
            mode_of_credit=domain_entity.mode_of_credit,
            amount_in_base_currency=domain_entity.amount_in_base_currency.amount,
            base_currency=domain_entity.amount_in_base_currency.currency.value,
            unused_credit_amount=domain_entity.unused_credit_amount.amount,
            refunded_amount=domain_entity.refunded_amount.amount,
            amount_in_credit_currency=domain_entity.amount_in_credit_currency.amount,
            credit_currency=domain_entity.amount_in_credit_currency.currency.value,
            deleted=domain_entity.deleted,
            used_to_auto_settle_debit=domain_entity.used_to_auto_settle_debit,
            linked_debit_id=domain_entity.linked_debit_id,
            status=domain_entity.status,
            approval_document=domain_entity.approval_document,
            cancellation_date=domain_entity.cancellation_date,
            recorded_via=domain_entity.recorded_via,
            cancellation_reason=domain_entity.cancellation_reason,
        )

    def to_domain_entity(self, db_entity, **kwargs):
        amount_in_base_currency = Money(
            amount=db_entity.amount_in_base_currency,
            currency=CurrencyType(db_entity.base_currency),
        )
        amount_in_credit_currency = Money(
            amount=db_entity.amount_in_credit_currency,
            currency=CurrencyType(db_entity.credit_currency),
        )
        return Credit(
            credit_id=db_entity.credit_id,
            debtor_id=db_entity.debtor_id,
            credit_type=db_entity.credit_type,
            date=db_entity.credit_date,
            amount_in_base_currency=amount_in_base_currency,
            reference_number=db_entity.reference_number,
            reference_id=db_entity.reference_id,
            mode_of_credit=db_entity.mode_of_credit,
            amount_in_credit_currency=amount_in_credit_currency,
            unused_credit_amount=Money(
                db_entity.unused_credit_amount, CurrencyType(db_entity.base_currency)
            ),
            refunded_amount=Money(
                db_entity.refunded_amount or 0, CurrencyType(db_entity.base_currency)
            ),
            deleted=db_entity.deleted,
            created_at=db_entity.created_at,
            used_to_auto_settle_debit=db_entity.used_to_auto_settle_debit,
            linked_debit_id=db_entity.linked_debit_id,
            status=db_entity.status,
            approval_document=db_entity.approval_document,
            cancellation_date=db_entity.cancellation_date,
            recorded_via=db_entity.recorded_via,
            cancellation_reason=db_entity.cancellation_reason,
        )
