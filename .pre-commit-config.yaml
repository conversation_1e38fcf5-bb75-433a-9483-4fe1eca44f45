repos:
- repo: https://github.com/psf/black
  rev: 22.1.0
  hooks:
  - id: black

- repo: https://github.com/timothycrosley/isort
  rev: 5.10.1
  hooks:
  - id: isort
    args: ["--profile", "black"]

- repo: local
  hooks:
  - id: pylint
    name: pylint
    entry: pylint
    language: system
    types: [python]
    args:
    - --fail-under=7.5
    - --fail-on=E
    - --disable=C,W,R
    - --rcfile=./.pylintrc

- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v3.2.0
  hooks:
  - id: trailing-whitespace
  - id: end-of-file-fixer
  - id: check-docstring-first
  - id: check-added-large-files
  - id: debug-statements
