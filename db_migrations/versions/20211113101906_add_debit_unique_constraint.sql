-- revision: '20211113101906_add_debit_unique_constraint'
-- down_revision: '20210811102500_audit_trail_models'

-- upgrade
CREATE UNIQUE INDEX uidx_debit_reference_number_non_cancelled ON debit (reference_number) WHERE (settlement_status != 'cancelled');
ALTER TABLE debit DROP CONSTRAINT debit_reference_number_key;

-- downgrade
ALTER TABLE debit ADD CONSTRAINT debit_reference_number_key UNIQUE (reference_number);
DROP INDEX uidx_debit_reference_number_non_cancelled;

