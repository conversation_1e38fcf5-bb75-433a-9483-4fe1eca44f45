class PhoneNumber:
    def __init__(self, country_code, number):
        self.country_code = country_code
        self.number = number


class CompanyProfilePOCDTO:
    def __init__(
        self,
        contact_types,
        department,
        designation,
        email_ids,
        name,
        phone_number,
        poc_type,
        user_id,
        **kwargs,
    ):
        self.contact_types = contact_types
        self.department = department
        self.designation = designation
        self.email_ids = email_ids
        self.name = name
        self.phone_number = PhoneNumber(**phone_number) if phone_number else None
        self.poc_type = poc_type
        self.user_id = user_id
        self.source = kwargs.get("source", None)
