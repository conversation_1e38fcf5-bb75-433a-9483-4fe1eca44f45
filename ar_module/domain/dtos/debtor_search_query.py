class DebtorSearchQuery:
    def __init__(
        self,
        debtor_code=None,
        debtor_name=None,
        query=None,
        hotel_id=None,
        debtor_type=None,
        debtor_ids=None,
        debtor_id=None,
        debtor_codes=None,
        user_accessible_debtors_id=None,
        only_fields=None,
        limit=None,
        offset=None,
        sort_order=None,
    ):
        self.debtor_code = debtor_code
        self.debtor_name = debtor_name
        self.query = query
        self.debtor_type = debtor_type
        self.only_fields = only_fields
        self.hotel_id = hotel_id
        self.debtor_ids = debtor_ids
        self.debtor_id = debtor_id
        self.debtor_codes = debtor_codes
        self.user_accessible_debtors_id = user_accessible_debtors_id
        self.sort_order = sort_order
        self.limit = limit
        self.offset = offset
