import logging
from typing import List

from ths_common.constants.billing_constants import PaymentModes, PaymentTypes

from ar_module.api.schemas.request.common import BulkIngestFinancialDataRequestSchema
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from ar_module.async_job.handlers.bulk_data_ingestors.frontdesk_payments.dtos import (
    CommonAttributesForIngestion,
    CreditDtoForBulkIngestion,
    DebitDtoForBulkIngestion,
)
from ar_module.async_job.handlers.bulk_data_ingestors.frontdesk_payments.error import (
    HandledException,
)
from ar_module.common.slack_alert_helper import SlackAlert
from ar_module.common.tenant_utils import get_tenant_id
from ar_module.common.utils import calculate_due_date_based_on_settlement_frequency
from ar_module.core.common.globals import global_context
from ar_module.domain.constants import CREDIT_PAYMENT_TYPES
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.credit_factory import CreditFactory
from ar_module.domain.factories.debit_factory import DebitFactory
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CatalogServiceClient,
        CreditRepository,
        DebitRepository,
        AuditTrailService,
        DebtorRepository,
        TenantSettings,
    ]
)
class FinancialDataIngestionHandler(BaseJobHandler):
    def __init__(
        self,
        catalog_client: CatalogServiceClient,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        audit_trail_service: AuditTrailService,
        debtor_repo: DebtorRepository,
        tenant_settings: TenantSettings,
    ):
        self.catalog_client = catalog_client
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.audit_trail_service = audit_trail_service
        self.debtor_repo = debtor_repo
        self.tenant_settings = tenant_settings
        self.tenant_id = global_context.tenant_id

    def execute_job(self, job_id, job_data):
        self.ingest_data(job_id, job_data)

    @unit_of_work
    def ingest_data(self, job_id, job_data):
        try:
            financial_data_list = job_data
            debits, credit_items = [], []
            data_validation_error = dict()

            base_currency = self.tenant_settings.get_chain_base_currency()

            debtor_configs = self.tenant_settings.get_debtor_configs()
            if not debtor_configs:
                raise Exception("Debtor configuration missing")

            debtor_codes = list(debtor_configs.keys())
            payment_mode_debtor_code_mapping = {
                config.payment_mode: config.debtor_code
                for config in debtor_configs.values()
            }
            debtor_code_settlement_freq_mapping = {
                config.debtor_code: config.settlement_frequency
                for config in debtor_configs.values()
            }
            tenant_id = get_tenant_id()
            for financial_data in financial_data_list:
                data, error = BulkIngestFinancialDataRequestSchema().load(
                    financial_data
                )
                if error:
                    data_validation_error[data["reference_id"]] = str(error)
                    logger.info(
                        f"Data validation failed for the record: {data['reference_id']} : {str(error)}"
                    )
                elif data["payment_type"] == PaymentTypes.PAYMENT.value:
                    debits.append(
                        DebitDtoForBulkIngestion.create_from_financial_data(
                            data,
                            base_currency,
                            tenant_id=tenant_id,
                        )
                    )
                elif data["payment_type"] in CREDIT_PAYMENT_TYPES:
                    credit_items.append(
                        CreditDtoForBulkIngestion.create_from_financial_data(
                            data,
                            base_currency,
                            tenant_id=tenant_id,
                        )
                    )

            debtor_aggregates = self.debtor_repo.search_debtors(
                DebtorSearchQuery(debtor_codes=debtor_codes)
            )
            debtor_code_to_debtor_mapping = {
                debtor.debtor.debtor_code: debtor for debtor in debtor_aggregates
            }
            self.create_debits(
                debits,
                debtor_configs,
                debtor_code_to_debtor_mapping,
                payment_mode_debtor_code_mapping,
                debtor_code_settlement_freq_mapping,
            )
            self.create_credits(
                credit_items,
                debtor_configs,
                debtor_code_to_debtor_mapping,
                payment_mode_debtor_code_mapping,
            )

            if data_validation_error:
                SlackAlert.send_alert(data_validation_error, tenant_id=self.tenant_id)

        except Exception as e:
            msg = "Financial data ingestion failed for job id: {0} due to exception: {1}".format(
                job_id, str(e)
            )
            logger.exception(e)
            SlackAlert.send_alert(msg, tenant_id=self.tenant_id)
            raise e

    def create_credits(
        self,
        credit_items: List[CreditDtoForBulkIngestion],
        debtor_configs,
        debtor_code_to_debtor_mapping,
        payment_mode_debtor_code_mapping,
    ):
        reference_ids = [credit.reference_id for credit in credit_items]
        existing_credit_aggregates = self.credit_repo.get_credits_by_reference_ids(
            reference_ids
        )
        existing_credits_ref_ids = [
            credit_aggregate.credit.reference_id
            for credit_aggregate in existing_credit_aggregates
        ]
        failed_credits_with_reason = dict()
        credit_aggregates = []
        for credit_dto in credit_items:
            try:
                reference_id = credit_dto.reference_id
                if reference_id in existing_credits_ref_ids:
                    continue

                debtor_code = self._find_debtor_code(
                    credit_dto, payment_mode_debtor_code_mapping
                )

                config = debtor_configs.get(debtor_code)
                if not config or not config.create_auto_debit:
                    raise HandledException(
                        f"Skipping credit creation for refund {reference_id}, "
                        f"ingestion is not enabled for debtor-payment mode config"
                    )

                debtor_aggregate = debtor_code_to_debtor_mapping.get(debtor_code)
                if not debtor_aggregate:
                    raise HandledException(
                        f"Cannot create credit {reference_id},"
                        f" No debtor exists in db with debtor code: {debtor_code}"
                    )

                credit_dto.debtor_id = debtor_aggregate.debtor.debtor_id
                credit_aggregates.append(
                    CreditFactory.create_new_credit(credit_dto=credit_dto)
                )

            except Exception as e:
                self._handle_exception(
                    e, credit_dto.reference_id, failed_credits_with_reason
                )

        if failed_credits_with_reason:
            SlackAlert.send_alert(failed_credits_with_reason, tenant_id=self.tenant_id)

        self.credit_repo.save_all(credit_aggregates)

        return failed_credits_with_reason

    def create_debits(
        self,
        debits: List[DebitDtoForBulkIngestion],
        debtor_configs,
        debtor_code_to_debtor_mapping,
        payment_mode_debtor_code_mapping,
        debtor_code_settlement_freq_mapping,
    ):
        reference_ids = [debit.reference_id for debit in debits]
        existing_debit_aggregates = self.debit_repo.get_debits_by_reference_ids(
            reference_ids
        )
        existing_debit_aggregates_ref_ids = [
            debit_aggregate.debit.reference_id
            for debit_aggregate in existing_debit_aggregates
        ]
        failed_debits_with_reason = dict()
        debit_aggregates = []
        for debit_dto in debits:
            try:
                reference_id = debit_dto.reference_id
                if reference_id in existing_debit_aggregates_ref_ids:
                    continue

                debtor_code = self._find_debtor_code(
                    debit_dto, payment_mode_debtor_code_mapping
                )

                config = debtor_configs.get(debtor_code)
                if not config or not config.create_auto_debit:
                    raise HandledException(
                        f"Skipping debit creation for refund {reference_id}, "
                        f"ingestion is not enabled for debtor-payment mode config"
                    )

                debtor_aggregate = debtor_code_to_debtor_mapping.get(debtor_code)
                if not debtor_aggregate:
                    raise HandledException(
                        f"Cannot create debit {reference_id},"
                        f"No debtor exists in db with debtor code: {debtor_code}"
                    )

                settlement_freq = debtor_code_settlement_freq_mapping.get(
                    debtor_code, "monthly"
                )
                debit_dto.due_date = calculate_due_date_based_on_settlement_frequency(
                    debit_dto.debit_date, settlement_freq
                )
                debit_dto.debtor_id = debtor_aggregate.debtor.debtor_id
                debit_aggregates.append(DebitFactory.create_from_debit_dto(debit_dto))
            except Exception as e:
                self._handle_exception(
                    e, debit_dto.reference_id, failed_debits_with_reason
                )

        if failed_debits_with_reason:
            SlackAlert.send_alert(failed_debits_with_reason, tenant_id=self.tenant_id)
        self.debit_repo.save_all(debit_aggregates)

    @staticmethod
    def _find_debtor_code(
        record: CommonAttributesForIngestion, payment_mode_debtor_code_mapping
    ):
        if record.payment_mode == PaymentModes.PAID_AT_OTA:
            debtor_code = record.ta_ref_id
        else:
            debtor_code = payment_mode_debtor_code_mapping.get(record.payment_mode)
        return debtor_code

    @staticmethod
    def _handle_exception(error, reference_id, trace):
        error_message = str(error)
        if isinstance(error, HandledException):
            logger.info(error_message)
        else:
            logger.exception(error_message)
        trace[reference_id] = error_message
