import logging
import os
import uuid
from collections import defaultdict

from marshmallow import ValidationError
from ths_common.utils.common_utils import group_list
from treebo_commons.money import Money

from ar_module.application.dtos.collection_report_dto import (
    CollectionReportDto,
    DetailedCollectionReportDto,
    MonthWiseCollectionSummary,
    PaymentsDto,
    SummarisedCollectionReportDto,
    TotalPaymentCreditDto,
)
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.common.utils import get_month_wise_date_range_breakup
from ar_module.domain.constants import CreditType, ModeOfCredit, SettlementStatus
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)
COLLECTION_REPORT_FOLDER_NAME = "collection_reports/"


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        CatalogServiceClient,
        TenantSettings,
    ]
)
class CollectionReportApplicationService(object):
    SUMMARISED_COLLECTION_REPORT_COLUMNS = [
        "start_date",
        "end_date",
        "debtor_count",
        "mtd_collections",
        "settled_debits",
        "payment",
    ]

    DETAILED_COLLECTION_REPORT_COLUMNS = [
        "debtor_code",
        "debtor_type",
        "debtor_name",
        "total_payment_credits",
        "settled_debits",
    ]

    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        catalog_client: CatalogServiceClient,
        tenant_settings: TenantSettings,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.catalog_client = catalog_client
        self.tenant_settings = tenant_settings

    def get_collection_report(self, request_data):
        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        if hotel_level_accounts_receivable and not request_data.hotel_id:
            raise ValidationError(
                "Hotel id is mandatory if ar is configured on hotel level"
            )
        debtor_aggregates = self.debtor_repo.search_debtors(
            DebtorSearchQuery(
                hotel_id=request_data.hotel_id,
                debtor_code=request_data.debtor_code,
                debtor_type=request_data.debtor_type,
            )
        )
        debtor_ids = [
            debtor_aggregate.debtor_id for debtor_aggregate in debtor_aggregates
        ]
        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        base_currency = self.tenant_settings.get_chain_base_currency()

        if hotel_level_accounts_receivable:
            base_currency = self.catalog_client.get_hotel_base_currency(
                request_data.hotel_id
            )
        if request_data.max_paid_amount:
            request_data.min_paid_amount = Money(
                request_data.min_paid_amount.amount, base_currency
            )
            request_data.max_paid_amount = Money(
                request_data.max_paid_amount.amount, base_currency
            )
        tenant_payment_modes = self.catalog_client.get_all_payment_modes()
        month_wise_date_breakup = get_month_wise_date_range_breakup(
            request_data.start_date, request_data.end_date
        )
        month_wise_summary = self.get_month_wise_collection_summary(
            debtor_ids, month_wise_date_breakup, base_currency, tenant_payment_modes
        )
        summarised_report_download_url = (
            self.generate_report_download_url(
                report_data=month_wise_summary,
                level="summarised",
                column_values=self.SUMMARISED_COLLECTION_REPORT_COLUMNS,
            )
            if month_wise_summary
            else None
        )
        collection_details = []
        detailed_report_download_url = None
        if request_data.include_detail:
            collection_details = self.get_collection_details(
                debtor_aggregates,
                request_data.start_date,
                request_data.end_date,
                min_paid_amount=request_data.min_paid_amount,
                max_paid_amount=request_data.max_paid_amount,
            )
            detailed_report_download_url = (
                self.generate_report_download_url(
                    report_data=collection_details,
                    level="detailed",
                    column_values=self.DETAILED_COLLECTION_REPORT_COLUMNS,
                )
                if collection_details
                else None
            )
        return CollectionReportDto(
            month_wise_summary=month_wise_summary,
            detailed=collection_details,
            summarised_report_download_url=summarised_report_download_url,
            detailed_report_download_url=detailed_report_download_url,
        )

    def get_collection_reports(self, data):
        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        if hotel_level_accounts_receivable and not data.hotel_id:
            raise ValidationError(
                "Hotel id is mandatory if ar is configured on hotel level"
            )
        debtor_aggregates = self.debtor_repo.search_debtors(
            DebtorSearchQuery(hotel_id=data.hotel_id, debtor_type=data.debtor_type)
        )
        debtor_ids = [
            debtor_aggregate.debtor_id for debtor_aggregate in debtor_aggregates
        ]
        base_currency = self.catalog_client.get_hotel_base_currency(data.hotel_id)

        date_ranges = []
        for date_range in data.date_ranges:
            date_ranges.append(
                dict(
                    month_start_date=date_range.start_date,
                    month_end_date=date_range.end_date,
                )
            )

        tenant_payment_modes = self.catalog_client.get_all_payment_modes()
        month_wise_summary = self.get_month_wise_collection_summary(
            debtor_ids, date_ranges, base_currency, tenant_payment_modes
        )
        summarised_report_download_url = (
            self.generate_report_download_url(
                report_data=month_wise_summary,
                level="summarised",
                column_values=self.SUMMARISED_COLLECTION_REPORT_COLUMNS,
            )
            if month_wise_summary
            else None
        )
        return CollectionReportDto(
            month_wise_summary=month_wise_summary,
            detailed=[],
            summarised_report_download_url=summarised_report_download_url,
            detailed_report_download_url=None,
        )

    def get_collection_details(
        self, debtor_aggregates, start_date, end_date, min_paid_amount, max_paid_amount
    ):
        debtor_ids = [
            debtor_aggregate.debtor_id for debtor_aggregate in debtor_aggregates
        ]
        debtor_id_to_debtor_mapping = {
            debtor_aggregate.debtor_id: debtor_aggregate.debtor
            for debtor_aggregate in debtor_aggregates
        }
        debtor_credits = self.credit_repo.load_for_debtor_ids(
            debtor_ids, start_date, end_date, credit_types=[CreditType.PAYMENT]
        )
        debtor_debits = self.debit_repo.load_for_debtor_ids(
            debtor_ids, start_date, end_date
        )

        debtor_id_wise_credits = group_list(debtor_credits, "debtor_id")
        debtor_id_wise_debits = group_list(debtor_debits, "debtor_id")

        collection_details = []
        for debtor_id in debtor_ids:
            debtor = debtor_id_to_debtor_mapping.get(debtor_id)
            debits = debtor_id_wise_debits.get(debtor_id)
            credits = debtor_id_wise_credits.get(debtor_id)

            total_payment_credits = (
                sum([credit.credit.amount_in_base_currency for credit in credits])
                if credits
                else None
            )
            if not credits:
                continue
            if max_paid_amount and not (
                min_paid_amount <= total_payment_credits <= max_paid_amount
            ):
                continue
            settled_debits = (
                len(
                    [
                        debit
                        for debit in debits
                        if debit.debit.settlement_status == SettlementStatus.SETTLED
                    ]
                )
                if debits
                else None
            )
            collection_details.append(
                DetailedCollectionReportDto(
                    debtor_code=debtor.debtor_code,
                    debtor_name=debtor.debtor_name,
                    total_payment_credits=total_payment_credits,
                    settled_debits=settled_debits,
                    debtor_type=debtor.debtor_type,
                )
            )
        return collection_details

    def get_month_wise_collection_summary(
        self, debtor_ids, month_wise_date_breakup, base_currency, tenant_payment_modes
    ):
        summaries = []
        for date_range in month_wise_date_breakup:
            collection_summary = self._get_collection_summary(
                debtor_ids=debtor_ids,
                start_date=date_range.get("month_start_date"),
                end_date=date_range.get("month_end_date"),
            )
            month_wise_collection_summary = MonthWiseCollectionSummary(
                start_date=date_range.get("month_start_date"),
                end_date=date_range.get("month_end_date"),
                summary=collection_summary,
                tenant_payment_modes=tenant_payment_modes,
                base_currency=base_currency,
            )
            summaries.append(month_wise_collection_summary)
        return summaries

    def _get_collection_summary(self, debtor_ids, end_date, start_date):
        debtor_credits = self.credit_repo.load_for_debtor_ids(
            debtor_ids,
            start_date,
            end_date,
            credit_types=[CreditType.PAYMENT],
            exclude_mode_of_credit_none_types=True,
        )
        debtor_debits = self.debit_repo.load_for_debtor_ids(
            debtor_ids, start_date, end_date
        )
        settled_debits = [
            debit
            for debit in debtor_debits
            if debit.debit.settlement_status == SettlementStatus.SETTLED
        ]
        debtor_count = list(set([credit.credit.debtor_id for credit in debtor_credits]))
        mtd_collections = sum(
            credit.credit.amount_in_base_currency for credit in debtor_credits
        )
        payment_mode_collection_mapping = defaultdict(list)
        for credit in debtor_credits:
            payment_mode_collection_mapping[
                credit.credit.mode_of_credit.upper()
            ].append(credit.credit.amount_in_base_currency)
        total_payment_credits = []
        for payment_mode, collection_amounts in payment_mode_collection_mapping.items():
            collection_amount = sum(collection_amounts)
            total_payment_credits.append(
                TotalPaymentCreditDto(
                    payment_mode=payment_mode, amount=collection_amount
                )
            )
        summary = SummarisedCollectionReportDto(
            mtd_collections=mtd_collections,
            debtor_count=len(debtor_count),
            settled_debits=len(settled_debits),
            total_payment_credits=total_payment_credits,
        )
        return summary

    @staticmethod
    def generate_collection_report_file_name(
        extension="csv", identifier=None, level=None
    ):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"{level}-collection-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def generate_report_download_url(self, report_data, column_values, level):
        file_path = self.generate_collection_report_file_name(level=level)

        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(report_data, column_values)
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                COLLECTION_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                self.get_default_expiration_time(),
            )

        return presigned_url

    def get_collection_summary_by_credit_date(self, request_data):
        debtor_aggregates = self.debtor_repo.search_debtors(
            DebtorSearchQuery(
                hotel_id=request_data.hotel_id,
                debtor_code=request_data.debtor_code,
                debtor_type=request_data.debtor_type,
            )
        )
        debtor_ids = [
            debtor_aggregate.debtor_id for debtor_aggregate in debtor_aggregates
        ]
        debtor_credits = self.credit_repo.load_for_debtor_ids_by_credit_date(
            debtor_ids=debtor_ids,
            credit_date_end_date=request_data.end_date,
            credit_date_start_date=request_data.start_date,
            credit_types=[CreditType.PAYMENT, CreditType.CREDIT_NOTE],
            exclude_mode_of_credit_none_types=False,
        )

        payment_mode_collection_mapping = defaultdict(list)
        for credit in debtor_credits:
            mode_of_credit = ModeOfCredit.OTHERS
            if credit.credit.mode_of_credit:
                mode_of_credit = credit.credit.mode_of_credit
            payment_mode_collection_mapping[mode_of_credit.upper()].append(
                credit.credit.amount_in_base_currency
            )
        total_payment_credits = []
        for payment_mode, collection_amounts in payment_mode_collection_mapping.items():
            collection_amount = sum(collection_amounts)
            total_payment_credits.append(
                TotalPaymentCreditDto(
                    payment_mode=payment_mode, amount=collection_amount
                )
            )
        return PaymentsDto(payments=total_payment_credits)
