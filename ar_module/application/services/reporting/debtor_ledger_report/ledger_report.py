import logging
import os
import uuid
from decimal import Decimal

from marshmallow import ValidationError
from ths_common.utils.common_utils import group_list
from treebo_commons.money import Money

from ar_module.application.consumers.constants import BookKeepingType, POCsDesignation
from ar_module.application.dtos.debtor_ledger_report_dto import (
    DebtorLedgerReportDto,
    DebtorLedgerSummaryDto,
    DetailedDebtorLedgerReportDto,
    SummarisedDebtorLedgerReportDto,
)
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.application.services.reporting.dtos import DebtorSummaryRequestData
from ar_module.core.common.globals import global_context
from ar_module.domain.constants import CreditType, SettlementStatus
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.facts import Facts
from ar_module.exceptions import PolicyAuthException
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)
LEDGER_REPORT_FOLDER_NAME = "ledger_reports/"


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        TenantSettings,
        DebtorService,
        CompanyProfileServiceClient,
    ]
)
class LedgerReportApplicationService(object):
    SUMMARISED_LEDGER_REPORT_COLUMN = [
        "total_credits",
        "total_debits",
        "total_provisional_credits",
        "unapplied_payment_balance",
        "gross_receivables",
        "net_receivables",
    ]

    DETAILED_LEDGER_REPORT_COLUMN = [
        "debtor_code",
        "debtor_type",
        "debtor_name",
        "date",
        "reference_number",
        "entry_type",
        "settlement_date",
        "amount",
        "debtor_net_balance",
        "settlement_reference_number",
    ]

    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        tenant_settings: TenantSettings,
        debtor_service: DebtorService,
        company_service_client: CompanyProfileServiceClient,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.tenant_settings = tenant_settings
        self.debtor_service = debtor_service
        self.company_service_client = company_service_client

    def get_debtor_ledger_report(self, request_data, user_data):
        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        if hotel_level_accounts_receivable and not user_data.hotel_id:
            raise ValidationError(
                "Hotel id is mandatory if ar is configured on hotel level"
            )
        debtors = self.debtor_repo.search_debtors(
            DebtorSearchQuery(
                hotel_id=request_data.hotel_id,
                debtor_code=request_data.debtor_code,
                debtor_type=request_data.debtor_type,
                debtor_id=request_data.debtor_id,
            )
        )
        summarised_debtor_ledger_report = self.get_debtor_ledger_summary(
            debtors, filter_criteria=request_data
        )
        summarised_report_download_url = (
            self.generate_summarised_report_download_url(
                summarised_debtor_ledger_report
            )
            if request_data.generate_download_urls and summarised_debtor_ledger_report
            else None
        )
        detailed_debtor_ledger_report = []
        detailed_report_download_url = None
        if request_data.include_detail:
            detailed_debtor_ledger_report = self.get_debtor_ledger_details(
                debtors, filter_criteria=request_data
            )
            detailed_report_download_url = (
                self.generate_detailed_report_download_url(
                    detailed_debtor_ledger_report
                )
                if request_data.generate_download_urls and detailed_debtor_ledger_report
                else None
            )
        total = len(detailed_debtor_ledger_report)
        paginated_detailed_report = detailed_debtor_ledger_report
        if request_data.offset:
            paginated_detailed_report = paginated_detailed_report[request_data.offset :]
        if request_data.limit:
            paginated_detailed_report = paginated_detailed_report[: request_data.limit]
        return DebtorLedgerReportDto(
            summary=summarised_debtor_ledger_report,
            detailed=paginated_detailed_report,
            summarised_report_download_url=summarised_report_download_url,
            detailed_report_download_url=detailed_report_download_url,
            limit=request_data.limit,
            offset=request_data.offset,
            total=total,
        )

    def get_debtor_ledger_details(self, debtors, filter_criteria):
        debtor_ids = [debtor.debtor_id for debtor in debtors]
        debits = self.debit_repo.load_for_debtor_ids(
            debtor_ids,
            start_date=filter_criteria.start_date,
            end_date=filter_criteria.end_date,
            settlement_statuses=filter_criteria.settlement_status,
            min_debit_amount=filter_criteria.min_amount,
            max_debit_amount=filter_criteria.max_amount,
            settlement_date=filter_criteria.settlement_date,
            sort_settlement_desc=True,
        )
        credits = self.credit_repo.load_for_debtor_ids(
            debtor_ids,
            start_date=filter_criteria.start_date,
            end_date=filter_criteria.end_date,
            min_credit_amount=filter_criteria.min_amount,
            max_credit_amount=filter_criteria.max_amount,
        )

        debtor_wise_debit = group_list(debits, "debtor_id")
        debtor_wise_credit = group_list(credits, "debtor_id")

        detailed_report = []
        for debtor in debtors:
            debtor_debits = debtor_wise_debit.get(debtor.debtor_id)
            debtor_credits = debtor_wise_credit.get(debtor.debtor_id)
            if debtor_debits:
                self._debtor_by_debit(
                    debtor, debtor_debits, detailed_report, filter_criteria
                )
            if debtor_credits:
                self._debtor_by_credit(
                    debtor, debtor_credits, detailed_report, filter_criteria
                )
        sorted_report = sorted(detailed_report, key=lambda dr: dr.date, reverse=True)
        return sorted_report

    def _debtor_by_credit(
        self, debtor, debtor_credits, detailed_report, filter_criteria
    ):
        for credit in debtor_credits:
            entry_type = BookKeepingType.CREDIT
            if credit.credit.credit_type == CreditType.TDS:
                entry_type = BookKeepingType.PROVISIONAL_CREDIT
            if (
                filter_criteria.entry_types
                and entry_type not in filter_criteria.entry_types
            ):
                continue
            credit_type = credit.credit.credit_type or ""
            mode = credit.credit.mode_of_credit or ""
            sub_type = f"{credit_type} {mode}".strip()
            detail_report = DetailedDebtorLedgerReportDto(
                debtor_code=debtor.debtor_code,
                debtor_name=debtor.debtor.debtor_name,
                date=credit.credit.created_at,
                reference_number=credit.credit.reference_number,
                entry_type=entry_type,
                amount=credit.credit.amount_in_base_currency,
                debtor_type=debtor.debtor_type,
                sub_type=sub_type,
            )
            detailed_report.append(detail_report)

    def _debtor_by_debit(self, debtor, debtor_debits, detailed_report, filter_criteria):
        for debit in debtor_debits:
            entry_type = BookKeepingType.DEBIT
            if (
                filter_criteria.entry_types
                and entry_type not in filter_criteria.entry_types
            ):
                continue

            settlement_reference_number = settlement_date = None
            if debit.settlements:
                latest_settlement = debit.settlements[0]
                settlement_date = latest_settlement.settlement_date
                settlement_reference_number = latest_settlement.reference_number

            detail_report = DetailedDebtorLedgerReportDto(
                debtor_code=debtor.debtor_code,
                debtor_name=debtor.debtor.debtor_name,
                date=debit.debit.created_at,
                reference_number=debit.debit.reference_number,
                entry_type=entry_type,
                settlement_date=settlement_date,
                amount=debit.debit.debit_amount.posttax_amount,
                settlement_status=debit.debit.settlement_status,
                settlement_reference_number=settlement_reference_number,
                debtor_type=debtor.debtor_type,
                sub_type=debit.debit.debit_type or "",
            )
            detailed_report.append(detail_report)

    def get_debtor_ledger_summary(self, debtors, filter_criteria):
        (
            debtor_wise_credit_summary,
            debtor_wise_debit_summary,
            debtor_wise_oldest_debit_age,
        ) = self.get_summary_report_for_debtors(
            debtors=debtors, filter_criteria=filter_criteria
        )
        debtors_summary = []
        for debtor in debtors:
            if not debtor_wise_credit_summary.get(
                debtor.debtor_id
            ) or not debtor_wise_debit_summary.get(debtor.debtor_id):
                continue
            (
                total_credits,
                total_provisional_credits,
                unapplied_payment,
            ) = debtor_wise_credit_summary.get(debtor.debtor_id)
            total_debits, gross_receivables = debtor_wise_debit_summary.get(
                debtor.debtor_id
            )
            debtor_summary = SummarisedDebtorLedgerReportDto(
                total_debits=total_debits,
                total_credits=total_credits,
                total_provisional_credits=total_provisional_credits,
                unapplied_payment_balance=unapplied_payment,
                gross_receivables=gross_receivables,
                net_receivables=gross_receivables - unapplied_payment,
            )
            debtors_summary.append(debtor_summary)

        return SummarisedDebtorLedgerReportDto(
            total_debits=sum([ds.total_debits for ds in debtors_summary]),
            total_credits=sum([ds.total_credits for ds in debtors_summary]),
            total_provisional_credits=sum(
                [ds.total_provisional_credits for ds in debtors_summary]
            ),
            unapplied_payment_balance=sum(
                [ds.unapplied_payment_balance for ds in debtors_summary]
            ),
            gross_receivables=sum([ds.gross_receivables for ds in debtors_summary]),
            net_receivables=sum([ds.net_receivables for ds in debtors_summary]),
        )

    def get_summary_report_for_debtors(self, debtors, filter_criteria):
        debtor_ids = [debtor.debtor_id for debtor in debtors]
        debtor_wise_credit_summary = self.credit_repo.get_credit_summary(
            debtor_ids,
            filter_criteria.start_date,
            filter_criteria.end_date,
            min_credit_amount=filter_criteria.min_amount,
            max_credit_amount=filter_criteria.max_amount,
        )
        debtor_wise_debit_summary = self.debit_repo.get_debit_summary(
            debtor_ids,
            filter_criteria.start_date,
            filter_criteria.end_date,
            settlement_statuses=filter_criteria.settlement_status,
            min_debit_amount=filter_criteria.min_amount,
            max_debit_amount=filter_criteria.max_amount,
        )
        debtor_wise_oldest_debit_age = (
            self.debit_repo.get_age_of_oldest_unsettled_debit(debtor_ids)
        )
        return (
            debtor_wise_credit_summary,
            debtor_wise_debit_summary,
            debtor_wise_oldest_debit_age,
        )

    @staticmethod
    def generate_ledger_report_file_name(extension="csv", identifier=None, level=None):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"{level}-ledger-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def generate_summarised_report_download_url(self, summarised_ledger_report):
        file_path = self.generate_ledger_report_file_name(level="summarised")
        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(
                [summarised_ledger_report], self.SUMMARISED_LEDGER_REPORT_COLUMN
            )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                LEDGER_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                self.get_default_expiration_time(),
            )

        return presigned_url

    def generate_detailed_report_download_url(self, detailed_ledger_report):
        file_path = self.generate_ledger_report_file_name(level="detailed")

        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(
                detailed_ledger_report, self.DETAILED_LEDGER_REPORT_COLUMN
            )
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                LEDGER_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                self.get_default_expiration_time(),
            )

        return presigned_url

    def _get_cc_recipients(self, all_pocs_of_debtor):
        designated_team_emails = []
        for poc in all_pocs_of_debtor:
            if poc.designation in (
                POCsDesignation.FINANCE_POC,
                POCsDesignation.SALES_POC,
                POCsDesignation.BOOKING_POC,
                POCsDesignation.FINANCE_ADMIN,
            ):
                designated_team_emails.append(
                    {"role": poc.designation, "email": "".join(poc.email_ids)}
                )
        return designated_team_emails

    def generate_debtor_summary(self, request: DebtorSummaryRequestData, user_data):
        RuleEngine.action_allowed(
            action="view_debtor_summary",
            facts=Facts(
                user_type=(
                    user_data.user_type if user_data else global_context.get_user_type()
                )
            ),
            fail_on_error=True,
        )
        query = DebtorSearchQuery(
            debtor_id=request.debtor_id, hotel_id=user_data.hotel_id
        )
        debtors, _ = self.debtor_service.get_debtors(query, user_data=user_data)
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.DEBTOR_SUMMARY_VIEW_NOT_ALLOWED,
                description="Not authorised to view given debtor",
            )
        credit_summary = self.credit_repo.get_credit_summary(
            [request.debtor_id],
            start_date=request.from_date,
            end_date=request.to_date,
        )
        debit_summary = self.debit_repo.get_debit_summary(
            [request.debtor_id],
            start_date=request.from_date,
            end_date=request.to_date,
            settlement_statuses=[
                SettlementStatus.UNSETTLED,
                SettlementStatus.PARTIALLY_SETTLED,
            ],
        )
        zero = Money(
            amount=Decimal("0"), currency=self.tenant_settings.get_chain_base_currency()
        )
        total_credits, _, total_unused_credits = credit_summary.get(
            request.debtor_id, (zero, zero, zero)
        )
        _, total_unsettled_debits = debit_summary.get(request.debtor_id, (zero, zero))
        all_pocs_of_debtor = self.company_service_client.get_poc_details(
            user_data.hotel_id, debtors[0].debtor_code
        )
        cc_recipients = self._get_cc_recipients(all_pocs_of_debtor)
        debtor_summary_dto = DebtorLedgerSummaryDto(
            total_credits=total_credits,
            total_unused_credits=total_unused_credits,
            total_unsettled_debits=total_unsettled_debits,
        )
        return debtor_summary_dto, cc_recipients
