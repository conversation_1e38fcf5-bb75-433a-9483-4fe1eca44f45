import json

from ar_module.integration_tests.config.common_config import (
    SETTLEMENT_CANCELLED,
    SETTLEMENT_CREATED,
    SUPER_ADMIN,
)
from ar_module.integration_tests.config.sheet_names import (
    EDIT_SETTLEMENT_SHEET_NAME,
    SETTLEMENT_DATA_SHEET_NAME,
)
from ar_module.integration_tests.tests.base_validation import BaseValidations
from ar_module.integration_tests.utilities import excel_utils
from ar_module.integration_tests.utilities.common_utils import (
    assert_,
    return_date,
    sanitize_blank,
)
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidationEditSettlement(BaseValidations):
    def __init__(
        self,
        client,
        test_case_id,
        response,
        settlement_request,
        credit_request,
        debit_request,
        audit_trail_request,
    ):
        self.client = client
        self.test_data = get_test_case_data(EDIT_SETTLEMENT_SHEET_NAME, test_case_id)[0]
        self.response = response
        self.settlement_request = settlement_request
        self.credit_request = credit_request
        self.debit_request = debit_request
        self.audit_trail_request = audit_trail_request

    def validate_response(self):
        if sanitize_blank(self.test_data["settlements"]) is not None:
            total_settlements = self.test_data["settlements"].split(",")
            for settlement_index, settlement in enumerate(total_settlements):
                settlement_data = excel_utils.get_test_case_data(
                    SETTLEMENT_DATA_SHEET_NAME, settlement
                )[0]
                assert_(
                    sanitize_blank(
                        self.response["data"]["settlements"][settlement_index]["amount"]
                    ),
                    settlement_data["expected_amount"],
                )
                assert_(
                    sanitize_blank(
                        self.response["data"]["settlements"][settlement_index][
                            "settled_via"
                        ]
                    ),
                    settlement_data["settled_via"],
                )
                assert_(
                    sanitize_blank(
                        self.response["data"]["settlements"][0]["settlement_date"]
                    ),
                    str(return_date(0)),
                )

    def validate_get_settlement(self):
        get_settlement_response = self.settlement_request.get_settlement_request(
            self.client, self.credit_request.credit_id, 200, SUPER_ADMIN
        )["data"]["settlements"]

        expected_settlement_response = json.loads(
            self.test_data["expected_get_settlement_response"]
        )

        get_settlement_response = sorted(
            get_settlement_response, key=lambda i: (i["amount"], i["settlement_id"])
        )
        expected_settlement_response = sorted(
            expected_settlement_response,
            key=lambda i: (i["amount"], i["settlement_id"]),
        )

        for actual_response, expected_response in zip(
            get_settlement_response, expected_settlement_response
        ):
            assert_(actual_response["amount"], expected_response["amount"])
            assert_(actual_response["credit_id"], self.credit_request.credit_id)
            assert actual_response["debit_id"] in self.debit_request.debit_id
            assert (
                actual_response["invoice_reference_number"]
                in self.debit_request.debit_reference_numbers
            )
            assert_(
                actual_response["mode_of_credit"], expected_response["mode_of_credit"]
            )
            assert_(
                actual_response["payment_reference_number"],
                self.credit_request.credit_reference_number,
            )
            assert_(actual_response["settled_via"], expected_response["settled_via"])
            assert_(
                actual_response["settlement_id"], expected_response["settlement_id"]
            )

    def validate_audit_trail(self):
        self.validate_audit_data(
            self.client,
            self.test_data,
            SETTLEMENT_CREATED,
            self.audit_trail_request,
            self.credit_request,
            self.debit_request,
        )
        self.validate_audit_data(
            self.client,
            self.test_data,
            SETTLEMENT_CANCELLED,
            self.audit_trail_request,
            self.credit_request,
            self.debit_request,
        )
