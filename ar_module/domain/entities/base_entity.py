class EntityChangeTracker(object):
    __slots__ = ("dirty", "new")

    def __init__(self, dirty=True, new=False):
        self.dirty = dirty
        self.new = new
        if self.new:
            self.dirty = True

    def mark_dirty(self):
        self.dirty = True

    def mark_clean(self):
        self.new = False
        self.dirty = False

    def is_dirty(self):
        return bool(self.dirty)

    def is_new(self):
        return bool(self.new)

    def has_changes_to_be_saved(self):
        return bool(self.dirty or self.new)
