from ar_module.common import id_generator_utils
from ar_module.domain.aggregates.debtor_aggregate import DebtorAggregate
from ar_module.domain.entities.debtor import Debtor
from ar_module.infrastructure.external_clients.crs_client import DebtorDTO


class DebtorFactory(object):
    @staticmethod
    def create_new_debtor(
        debtor_code,
        debtor_name,
        credit_period=None,
        credit_limit=None,
        btc_enabled=None,
        hotel_id=None,
        debtor_type=None,
    ):
        debtor_id = id_generator_utils.random_id_generator("DTR")
        return DebtorAggregate(
            Debtor(
                debtor_id=debtor_id,
                debtor_code=debtor_code,
                debtor_name=debtor_name,
                credit_period=credit_period,
                credit_limit=credit_limit,
                btc_enabled=btc_enabled,
                hotel_id=hotel_id,
                debtor_type=debtor_type,
            )
        )

    @staticmethod
    def create_new_debtor_from_debtor_dto(debtor_dto: DebtorDTO):
        debtor_id = id_generator_utils.random_id_generator("DTR")
        return DebtorAggregate(
            Debtor(
                debtor_id=debtor_id,
                debtor_code=debtor_dto.debtor_code,
                debtor_name=debtor_dto.debtor_name,
                user_profile_id=debtor_dto.user_profile_id,
                debtor_type=debtor_dto.debtor_type,
                hotel_id=debtor_dto.hotel_id,
                credit_period=debtor_dto.credit_period,
                credit_limit=debtor_dto.credit_limit,
                btc_enabled=debtor_dto.btc_enabled,
                profile_status=debtor_dto.profile_status,
                pocs=debtor_dto.pocs,
                registered_address=debtor_dto.registered_address,
            )
        )
