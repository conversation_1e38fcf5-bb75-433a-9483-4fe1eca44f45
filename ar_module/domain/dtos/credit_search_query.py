class CreditSearchQuery:
    def __init__(
        self,
        debtor_id=None,
        credit_id=None,
        credit_types=None,
        reference_number=None,
        has_unused_credit=None,
        used_to_auto_settle_debit=None,
        show_cancelled_credits=None,
        should_send_approval_document_url=False,
        should_send_credit_transaction_details=False,
        from_date=None,
        to_date=None,
        debtor_ids=None,
        credit_ids=None,
        limit=None,
        offset=None,
        sort_order=None,
    ):
        self.debtor_id = debtor_id
        self.credit_id = credit_id
        self.credit_types = credit_types
        self.reference_number = reference_number
        self.has_unused_credit = has_unused_credit
        self.used_to_auto_settle_debit = used_to_auto_settle_debit
        self.show_cancelled_credits = show_cancelled_credits
        self.should_send_approval_document_url = should_send_approval_document_url
        self.should_send_credit_transaction_details = (
            should_send_credit_transaction_details
        )
        self.from_date = from_date
        self.to_date = to_date
        self.debtor_ids = debtor_ids
        self.credit_ids = credit_ids
        self.sort_order = sort_order
        self.limit = limit
        self.offset = offset
