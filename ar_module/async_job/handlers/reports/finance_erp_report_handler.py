from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from ar_module.reporting.finance_erp_reporting.finance_erp_reporting_service import (
    FinanceERPReportingService,
)
from object_registry import register_instance


@register_instance(dependencies=[FinanceERPReportingService])
class FinanceERPReportHandler(BaseJobHandler):
    def __init__(
        self,
        finance_erp_reporting_service: FinanceERPReportingService,
    ):
        self.finance_erp_reporting_service = finance_erp_reporting_service

    def execute_job(self, job_id, job_data):
        date = job_data.get("date") if job_data else None
        self.finance_erp_reporting_service.push_payment_reports(date)
