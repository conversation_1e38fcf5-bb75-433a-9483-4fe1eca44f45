from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from ar_module.domain.constants import CreditStatus, CreditType
from ar_module.domain.entities.credit import Credit
from ar_module.domain.entities.credit_reversal import CreditReversal
from ar_module.domain.entities.settlement import Settlement


class CreditAggregate(object):
    def __init__(self, credit: Credit):
        self.credit = credit

    @property
    def credit_id(self):
        return self.credit.credit_id

    def delete(self):
        self.credit.deleted = True

    def knock_off_settlement(self, settlement: Settlement):
        self.credit.knock_off_used_amount(settlement.amount)

    def reverse_settlement_amount(self, settlement_amount: Money):
        self.credit.reverse_settlement_amount(settlement_amount)

    def is_tds_credit(self):
        return self.credit.credit_type == CreditType.TDS

    def cancel(self, cancellation_reason=None):
        self.credit.status = CreditStatus.CANCELLED
        if cancellation_reason:
            self.credit.cancellation_reason = cancellation_reason
        self.credit.cancellation_date = dateutils.current_date()
        if self.credit.credit_reversals:
            for credit_reversal in self.credit.credit_reversals:
                credit_reversal.cancel_reversal()

    @property
    def debtor_id(self):
        return self.credit.debtor_id

    @property
    def tenant_id(self):
        return self.credit.tenant_id

    def link_debit(self, debit_id):
        self.credit.link_debit(debit_id)

    def add_credit_reversals(self, credit_reversal_dtos):
        """
        Add credit reversals directly from the DTOs.
        :param credit_reversal_dtos: List of CreditReversalDetailsDto instances
        """
        for dto in credit_reversal_dtos:
            credit_reversal = CreditReversal(
                refund_credit_id=self.credit.credit_id,
                payment_credit_id=dto.credit_id,
                amount_in_base_currency=dto.amount_in_base_currency,
                amount_in_credit_currency=dto.amount_in_credit_currency,
                remarks=dto.remarks,
            )
            self.credit.credit_reversals.append(credit_reversal)

    def get_credit_reversals(self):
        return self.credit.credit_reversals

    def update_refunded_amount(self, refund_amount: Money):
        self.credit.update_refunded_amount(refund_amount)
