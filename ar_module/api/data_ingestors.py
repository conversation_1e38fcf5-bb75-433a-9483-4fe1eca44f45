from flask import request

from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.async_job.job.job_constants import JobStatus
from ar_module.async_job.job_scheduler_service import JobSchedulerService
from object_registry import inject


@ar_bp.route("/financial-data/ingest", methods=["POST"])
@inject(job_scheduler=JobSchedulerService)
def bulk_ingest_payments(job_scheduler: JobSchedulerService):
    request_data = request.json.get("data")
    job_aggregate = job_scheduler.create_bulk_ingest_financial_data_job(
        None, request_data
    )
    return ApiResponse.build(
        data=dict(job_id=job_aggregate.job_id, status=JobStatus.CREATED),
        status_code=200,
    )
