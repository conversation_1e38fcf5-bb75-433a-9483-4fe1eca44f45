import datetime

from sqlalchemy import func
from ths_common.utils.common_utils import group_list
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from ar_module.domain.aggregates.debit_aggregate import DebitAggregate
from ar_module.domain.constants import SettlementStatus, SortOrders
from ar_module.exceptions import AggregateNotFound
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.debit_model import DebitModel
from ar_module.infrastructure.database.models.settlement_model import SettlementModel
from ar_module.infrastructure.database.repositories.debit.db_adaptors.debit_adaptor import (
    DebitAdaptor,
)
from ar_module.infrastructure.database.repositories.debit.db_adaptors.settlement_adaptor import (
    SettlementAdaptor,
)
from object_registry import register_instance


@register_instance()
class DebitRepository(BaseRepository):
    debit_adaptor = DebitAdaptor()
    settlement_adaptor = SettlementAdaptor()

    def from_aggregate(self, aggregate: DebitAggregate = None):
        debit = aggregate.debit
        debit_model = self.debit_adaptor.to_db_entity(debit)
        settlement_models = [
            self.settlement_adaptor.to_db_entity(settlement, debit_id=debit.debit_id)
            for settlement in aggregate.settlements
        ]
        return debit_model, settlement_models

    def save(self, aggregate: DebitAggregate):
        debit_model, settlement_models = self.from_aggregate(aggregate)
        self._save(debit_model)
        self._save_all(settlement_models)
        self.flush_session()

    def save_all(self, aggregates):
        for debit_aggregate in aggregates:
            self.save(debit_aggregate)

    def load(self, debit_id):
        debit_model = (
            self.query(DebitModel)
            .filter(DebitModel.debit_id == debit_id, DebitModel.deleted.is_(False))
            .first()
        )
        settlement_models = self.query(SettlementModel).filter(
            SettlementModel.debit_id == debit_id, SettlementModel.deleted.is_(False)
        )
        return DebitAggregate(
            debit=self.debit_adaptor.to_domain_entity(debit_model),
            settlements=[
                self.settlement_adaptor.to_domain_entity(model)
                for model in settlement_models
            ],
        )

    def get_debit_by_reference_number(
        self, reference_number, auto_settled_via_credit=None
    ):
        debit_model = self.filter(
            DebitModel,
            DebitModel.reference_number == reference_number,
            DebitModel.deleted.is_(False),
        )
        if auto_settled_via_credit is not None:
            debit_model = debit_model.filter(
                DebitModel.auto_settled_via_credit.is_(auto_settled_via_credit)
            )
        debit_model = debit_model.first()
        if not debit_model:
            raise AggregateNotFound("Debit", f"reference number {reference_number}")
        return DebitAggregate(
            debit=self.debit_adaptor.to_domain_entity(debit_model), settlements=[]
        )

    def get_debit_by_reference_numbers(self, reference_numbers):
        debit_models = self.filter(
            DebitModel,
            DebitModel.reference_number.in_(reference_numbers),
            DebitModel.deleted.is_(False),
        ).all()
        return [
            DebitAggregate(
                debit=self.debit_adaptor.to_domain_entity(db_model), settlements=[]
            )
            for db_model in debit_models
        ]

    def get_debits_by_reference_ids(self, reference_ids):
        debit_models = self.filter(
            DebitModel,
            DebitModel.reference_id.in_(reference_ids),
            DebitModel.deleted.is_(False),
        ).all()
        return [
            DebitAggregate(
                debit=self.debit_adaptor.to_domain_entity(db_model), settlements=[]
            )
            for db_model in debit_models
        ]

    def load_for_invoices(self, invoice_ids=None, invoice_numbers=None):
        if not invoice_ids and not invoice_numbers:
            return []
        debit_models = self.query(DebitModel)
        if invoice_ids:
            debit_models = debit_models.filter(
                DebitModel.reference_id.in_(invoice_ids), DebitModel.deleted.is_(False)
            ).all()
        elif invoice_numbers:
            debit_models = debit_models.filter(
                DebitModel.reference_number.in_(invoice_numbers),
                DebitModel.deleted.is_(False),
            ).all()
        settlement_models = self.query(SettlementModel).filter(
            SettlementModel.debit_id.in_([model.debit_id for model in debit_models]),
            SettlementModel.deleted.is_(False),
        )
        grouped_settlement_models = group_list(settlement_models, "debit_id")

        return [
            DebitAggregate(
                debit=self.debit_adaptor.to_domain_entity(db_model),
                settlements=[
                    self.settlement_adaptor.to_domain_entity(model)
                    for model in grouped_settlement_models.get(
                        db_model.debit_id, list()
                    )
                ],
            )
            for db_model in debit_models
        ]

    def total_unsettled_amount(self, debit_search_query):
        query = self.query(func.sum(DebitModel.unsettled_amount))
        query = self._apply_filters_on_debit_models(query, debit_search_query)
        return query.first()[0]

    def search_debits(self, debit_search_query, debits_only=False):
        debit_models = self.query(DebitModel)
        debit_models = self._apply_filters_on_debit_models(
            debit_models, debit_search_query
        )
        if debits_only:
            return debit_models.all()
        if debit_search_query.sort_order:
            if debit_search_query.sort_order == SortOrders.ASC:
                debit_models = debit_models.order_by(DebitModel.created_at.asc())
            else:
                debit_models = debit_models.order_by(DebitModel.created_at.desc())
        else:
            debit_models = debit_models.order_by(DebitModel.created_at.desc())
        if (
            debit_search_query.limit is not None
            and debit_search_query.offset is not None
        ):
            debit_models = debit_models.limit(debit_search_query.limit).offset(
                debit_search_query.offset
            )
        debit_models = debit_models.all()
        settlement_models = (
            self.query(SettlementModel)
            .filter(
                SettlementModel.debit_id.in_([model.debit_id for model in debit_models])
            )
            .filter(SettlementModel.deleted.is_(False))
        )
        grouped_settlement_models = group_list(settlement_models, "debit_id")
        return [
            DebitAggregate(
                self.debit_adaptor.to_domain_entity(db_model),
                settlements=[
                    self.settlement_adaptor.to_domain_entity(model)
                    for model in grouped_settlement_models.get(
                        db_model.debit_id, list()
                    )
                ],
            )
            for db_model in debit_models
        ]

    def count_debits(self, debit_search_query):
        debit_models = self.query(DebitModel)
        debit_models = self._apply_filters_on_debit_models(
            debit_models, debit_search_query
        )
        return debit_models.count()

    @staticmethod
    def _apply_filters_on_debit_models(debit_models, debit_search_query):
        if debit_search_query.debtor_id:
            debit_models = debit_models.filter(
                DebitModel.debtor_id == debit_search_query.debtor_id.strip()
            )

        if debit_search_query.reference_id:
            debit_models = debit_models.filter(
                DebitModel.reference_id == debit_search_query.reference_id.strip()
            )

        if debit_search_query.reference_numbers:
            debit_models = debit_models.filter(
                DebitModel.reference_number.in_(debit_search_query.reference_numbers)
            )

        if debit_search_query.has_unsettled_amount is not None:
            if bool(debit_search_query.has_unsettled_amount):
                debit_models = debit_models.filter(DebitModel.unsettled_amount > 0)
            else:
                debit_models = debit_models.filter(DebitModel.unsettled_amount == 0)

        if not debit_search_query.include_cancelled:
            debit_models = debit_models.filter(
                DebitModel.settlement_status != SettlementStatus.CANCELLED
            )

        if debit_search_query.debtor_ids:
            debit_models = debit_models.filter(
                DebitModel.debtor_id.in_(debit_search_query.debtor_ids)
            )

        if debit_search_query.debit_date:
            debit_models = debit_models.filter(
                DebitModel.debit_date == debit_search_query.debit_date
            )

        if debit_search_query.from_date and debit_search_query.to_date:
            debit_models = debit_models.filter(
                debit_search_query.from_date <= DebitModel.debit_date
            ).filter(debit_search_query.to_date >= DebitModel.debit_date)

        if debit_search_query.from_date is None and debit_search_query.to_date:
            debit_models = debit_models.filter(
                debit_search_query.to_date >= DebitModel.debit_date
            )

        if debit_search_query.debit_ids is not None:
            debit_models = debit_models.filter(
                DebitModel.debit_id.in_(debit_search_query.debit_ids)
            )

        if debit_search_query.settlement_status:
            debit_models = debit_models.filter(
                DebitModel.settlement_status.in_(debit_search_query.settlement_status)
            )

        if debit_search_query.only_over_due_invoice:
            if not debit_search_query.over_due_date_filter:
                debit_models = debit_models.filter(
                    DebitModel.due_date <= datetime.datetime.today().date()
                )
            else:
                if debit_search_query.over_due_date_filter.from_date:
                    debit_models = debit_models.filter(
                        DebitModel.due_date
                        >= debit_search_query.over_due_date_filter.from_date
                    )
                if debit_search_query.over_due_date_filter.to_date:
                    debit_models = debit_models.filter(
                        DebitModel.due_date
                        <= debit_search_query.over_due_date_filter.to_date
                    )

        if debit_search_query.only_non_over_due_invoice:
            debit_models = debit_models.filter(
                DebitModel.due_date > datetime.datetime.today().date()
            )

        if debit_search_query.auto_settled_via_credit is not None:
            debit_models = debit_models.filter(
                DebitModel.auto_settled_via_credit
                == debit_search_query.auto_settled_via_credit
            )

        if debit_search_query.only_manual_debits:
            debit_models = debit_models.filter(DebitModel.reference_id == None)
        debit_models = debit_models.filter(DebitModel.deleted.is_(False))
        return debit_models

    def load_for_update(self, debit_id):
        debit_model = self.get_for_update(DebitModel, debit_id=debit_id)
        if not debit_model:
            raise AggregateNotFound("Debit", debit_id)
        settlement_models = self.query(SettlementModel).filter(
            SettlementModel.debit_id == debit_id
        )

        return DebitAggregate(
            debit=self.debit_adaptor.to_domain_entity(debit_model),
            settlements=[
                self.settlement_adaptor.to_domain_entity(model)
                for model in settlement_models
            ],
        )

    def load_all_for_update(self, debit_ids, no_wait=True):
        debit_models = (
            self.filter(
                DebitModel,
                DebitModel.debit_id.in_(debit_ids),
                DebitModel.deleted.is_(False),
                for_update=True,
                nowait=no_wait,
            )
            .order_by(DebitModel.debit_id)
            .all()
        )
        settlement_models = (
            self.query(SettlementModel)
            .filter(
                SettlementModel.debit_id.in_([model.debit_id for model in debit_models])
            )
            .all()
        )
        grouped_settlement_models = group_list(settlement_models, "debit_id")

        return [
            DebitAggregate(
                debit=self.debit_adaptor.to_domain_entity(db_model),
                settlements=[
                    self.settlement_adaptor.to_domain_entity(model)
                    for model in grouped_settlement_models.get(
                        db_model.debit_id, list()
                    )
                ],
            )
            for db_model in debit_models
        ]

    def update(self, aggregate: DebitAggregate):
        debit_model, settlement_models = self.from_aggregate(aggregate)
        self._update(debit_model)
        self._update_all(settlement_models)
        self.flush_session()

    def update_all(self, aggregates):
        for debit_aggregate in aggregates:
            self.update(debit_aggregate)

    def get_debit_summary(
        self,
        debtor_ids,
        start_date=None,
        end_date=None,
        settlement_statuses=None,
        min_debit_amount=None,
        max_debit_amount=None,
        reference_date=None,
    ):
        """
        Returns total_unsettled_amount and total_debits

        :param debtor_ids:
        :param reference_date:
        :param start_date:
        :param end_date:
        :param settlement_statuses:
        :param min_debit_amount:
        :param max_debit_amount:
        :return: total_unsettled_amount, total_debits
        """
        debit_summary = self.query(
            DebitModel.debtor_id,
            DebitModel.currency,
            func.sum(DebitModel.unsettled_amount).label("total_unsettled_amount"),
            func.sum(DebitModel.posttax_amount).label("total_debits"),
        ).group_by(DebitModel.debtor_id, DebitModel.currency)

        if debtor_ids:
            debit_summary = debit_summary.filter(DebitModel.debtor_id.in_(debtor_ids))
        if start_date:
            debit_summary = debit_summary.filter(DebitModel.debit_date >= start_date)
        if end_date:
            debit_summary = debit_summary.filter(DebitModel.debit_date <= end_date)
        if settlement_statuses:
            debit_summary = debit_summary.filter(
                DebitModel.settlement_status.in_(settlement_statuses)
            )
        if min_debit_amount:
            debit_summary = debit_summary.filter(
                DebitModel.posttax_amount >= min_debit_amount.amount
            )
        if max_debit_amount:
            debit_summary = debit_summary.filter(
                DebitModel.posttax_amount <= max_debit_amount.amount
            )
        debit_summary = debit_summary.filter(DebitModel.deleted.is_(False))

        debit_summaries = debit_summary.all()
        debtor_wise_summary = {
            summary.debtor_id: (
                Money(summary.total_debits, CurrencyType(summary.currency)),
                Money(summary.total_unsettled_amount, CurrencyType(summary.currency)),
            )
            for summary in debit_summaries
        }
        return debtor_wise_summary

    def get_age_of_oldest_unsettled_debit(self, debtor_ids):
        oldest_unsettled_debit_dates = (
            self.query(DebitModel.debtor_id, func.min(DebitModel.debit_date))
            .filter(
                DebitModel.debtor_id.in_(debtor_ids),
                DebitModel.settlement_status == SettlementStatus.UNSETTLED,
                DebitModel.deleted.is_(False),
            )
            .group_by(DebitModel.debtor_id)
        )
        debtor_wise_oldest_debit_date = {
            oldest_date.debtor_id: oldest_date[1]
            for oldest_date in oldest_unsettled_debit_dates
        }

        return {
            debtor_id: (dateutils.current_date() - oldest_debit_date).days
            for debtor_id, oldest_debit_date in debtor_wise_oldest_debit_date.items()
        }

    def load_for_debtor_ids(
        self,
        debtor_ids,
        start_date=None,
        end_date=None,
        settlement_statuses=None,
        min_debit_amount=None,
        max_debit_amount=None,
        settlement_date=None,
        sort_settlement_desc=None,
    ):
        query = self.query(DebitModel).filter(
            DebitModel.debtor_id.in_(debtor_ids), DebitModel.deleted.is_(False)
        )
        if start_date and end_date:
            query = query.filter(DebitModel.debit_date >= start_date)
            query = query.filter(DebitModel.debit_date <= end_date)
        if settlement_statuses:
            query = query.filter(DebitModel.settlement_status.in_(settlement_statuses))
        if min_debit_amount:
            query = query.filter(DebitModel.posttax_amount >= min_debit_amount.amount)
        if max_debit_amount:
            query = query.filter(DebitModel.posttax_amount <= max_debit_amount.amount)
        debit_models = query.all()
        settlement_models = self.query(SettlementModel).filter(
            SettlementModel.debit_id.in_([model.debit_id for model in debit_models])
        )
        if sort_settlement_desc:
            settlement_models = settlement_models.order_by(
                SettlementModel.created_at.desc()
            )
        if settlement_date:
            settlement_models = settlement_models.filter(
                SettlementModel.settlement_date == settlement_date
            )
        grouped_settlement_models = group_list(settlement_models, "debit_id")
        return [
            DebitAggregate(
                self.debit_adaptor.to_domain_entity(db_model),
                settlements=[
                    self.settlement_adaptor.to_domain_entity(model)
                    for model in grouped_settlement_models.get(
                        db_model.debit_id, list()
                    )
                ],
            )
            for db_model in debit_models
        ]
