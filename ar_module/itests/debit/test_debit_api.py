import json

from ar_module.integration_tests.mockers import mock_get_roles
from ar_module.itests.api_wrappers.debit_wrapper import create_debit
from ar_module.itests.api_wrappers.debtor_wrapper import create_debtor


def test_create_debit(client_, create_debtor_payload, create_debit_payload):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)

    assert debit_id is not None


def test_get_debit(client_, create_debtor_payload, create_debit_payload):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    url = f"/ar/v1/debits?debit_id={debit_id}"
    response = client_.get(
        url, content_type="application/json", headers={"X-User-Type": "super-admin"}
    )
    assert response.status_code == 200
    debit_data = json.loads(response.data.decode("utf-8"))
    assert debit_data["data"]["debits"][0]["debit_id"] == debit_id

    # getting debit from debtor
    url = f"/ar/v1/debits?debtor_id={debtor_id}"
    with mock_get_roles():
        response = client_.get(
            url, content_type="application/json", headers={"X-User-Type": "super-admin"}
        )
    assert response.status_code == 200
    debit_data = json.loads(response.data.decode("utf-8"))
    assert debit_data["data"]["debits"][0]["debtor_id"] == debtor_id
