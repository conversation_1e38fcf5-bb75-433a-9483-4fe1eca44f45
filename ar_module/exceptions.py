from ths_common.constants.base_enum import BaseEnum


class ARError(BaseEnum):
    @property
    def error_code(self):
        return self.values[0]

    @property
    def message(self):
        return self.values[1]


class BaseARException(Exception):
    error_code = "0001"
    message = "Something went wrong. Please contact escalations team"
    APP_CODE = {"ar": "01"}

    def __init__(self, description=None, extra_payload=None, message=None):
        self.description = description
        self.extra_payload = extra_payload
        if message is not None:
            self.message = message

    def __str__(self):
        return str(
            dict(
                error_code=self.error_code,
                message=self.message,
                description=self.description,
                extra_payload=self.extra_payload,
            )
        )

    def with_description(self, description):
        self.description = description
        return self

    def code(self, app_name="ar"):
        pod_code = "01"
        app_code = self.APP_CODE.get(app_name, "00")
        # https://treebo.atlassian.net/wiki/spaces/PFM/pages/201490819/Error+Handling+Guidelines
        return "{pod_code}{app_code}{error_code}".format(
            pod_code=pod_code, app_code=app_code, error_code=self.error_code
        )


class ApiValidationException(BaseARException):
    error_code = "0002"

    def __init__(self, error_messages=None):
        self.error_messages = error_messages
        super(ApiValidationException, self).__init__(
            description=None, extra_payload=None
        )

    def __str__(self):
        return "exception: error_code=%s messages=%s" % (
            self.error_code,
            self.error_messages,
        )


class ResourceNotFound(BaseARException):
    error_code = "0003"

    def __init__(self, message, extra_payload=None):
        self.message = message
        super().__init__(extra_payload=extra_payload)


class InvalidSettlementException(BaseARException):
    error_code = "0004"

    def __init__(self, message):
        super().__init__(message=message)


class AggregateNotFound(BaseARException):
    error_code = "0005"

    def __init__(self, class_name, id):
        self.message = "Aggregate: {} with id: {} missing.".format(class_name, id)
        super(AggregateNotFound, self).__init__(description="", extra_payload="")


class DatabaseError(BaseARException):
    error_code = "0006"
    message = "Something went wrong with the database"


class PrimaryKeyCollision(DatabaseError):
    error_code = "0007"
    message = "Collision in id"


class DebitReferenceNumberCollision(DatabaseError):
    error_code = "0008"
    message = "Collision in debit reference number"


class HotelLevelDebtorUniqueConstraintCollision(DatabaseError):
    error_code = "0009"
    message = "Collision in hotel id and debtor code combination"


class HotelIDHeaderMisMatch(BaseARException):
    error_code = "0010"
    message = "Mismatch in request hotel id and request params hotel id"

    def __init__(self):
        super(HotelIDHeaderMisMatch, self).__init__(
            description="X-Hotel-Id is not matching with request params"
        )


class AccessEntityRestricted(BaseARException):
    error_code = "0011"
    message = "Trying to access entity of another hotel"

    def __init__(self):
        super(AccessEntityRestricted, self).__init__(
            description="Trying to access entity of another hotel"
        )


class CorporateDetailsNotFound(BaseARException):
    error_code = "0012"

    def __init__(self, booking_id):
        self.message = (
            "Corporate details not found in CRS or Company Profiles for booking_id : "
            "{}".format(booking_id)
        )
        super(CorporateDetailsNotFound, self).__init__(
            description="Corporate details not found in CRS or Company Profiles"
        )


class AuthorizationError(BaseARException):
    error_code = "0013"
    message = "You're not authorized to perform this operation"

    def __init__(self, description=None, extra_payload=None):
        super(AuthorizationError, self).__init__(
            description=description, extra_payload=extra_payload
        )


class PolicyAuthException(AuthorizationError):
    error_code = "0014"

    def __init__(self, error=None, message=None, description=None, extra_payload=None):
        if error:
            self.error_code = error.error_code
            self.message = error.message
        else:
            self.error_code = self.error_code
            self.message = message if message else self.message
        super(PolicyAuthException, self).__init__(
            description=description, extra_payload=extra_payload
        )
