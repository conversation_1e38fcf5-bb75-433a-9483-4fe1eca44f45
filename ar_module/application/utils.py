from marshmallow import ValidationError
from treebo_commons.utils import dateutils

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.domain.constants import ARPaymentMode, CreditType
from object_registry import locate_instance


def validate_payment_mode(
    payment_mode, debtor_code=None, debtor_configs=None, user_data=None
):
    tenant_settings = locate_instance(TenantSettings)
    if debtor_code is not None:
        debtor_configs = debtor_configs or tenant_settings.get_debtor_configs()
        if debtor_code in debtor_configs:
            if not debtor_configs[debtor_code].has_access_to_payment(payment_mode):
                raise ValidationError(
                    f"Payment mode {payment_mode} can't be used for debtor {debtor_code}"
                )
            return
    payment_modes = tenant_settings.get_payment_modes()
    if payment_mode not in payment_modes:
        raise ValidationError("Invalid payment mode")

    validate_payment_mode_based_on_role(payment_mode, user_data)


def validate_refund_mode(
    refund_mode, debtor_code=None, debtor_configs=None, user_data=None
):
    tenant_settings = locate_instance(TenantSettings)
    if debtor_code is not None:
        debtor_configs = debtor_configs or tenant_settings.get_debtor_configs()
        if debtor_code in debtor_configs:
            if not debtor_configs[debtor_code].has_access_to_payment(refund_mode):
                raise ValidationError(
                    f"Refund mode {refund_mode} can't be used for debtor {debtor_code}"
                )
            return
    refund_modes = tenant_settings.get_refund_modes()
    if refund_mode not in refund_modes:
        raise ValidationError("Invalid refund mode")


def validate_payment_mode_based_on_role(payment_mode, user_data):
    tenant_settings = locate_instance(TenantSettings)
    applicable_roles_for_payment_mode = (
        tenant_settings.get_applicable_roles_for_payment_mode(payment_mode)
    )
    if (
        applicable_roles_for_payment_mode
        and user_data
        and user_data.user_type not in applicable_roles_for_payment_mode
    ):
        raise ValidationError(
            f"Payment mode {payment_mode} not supported for user {user_data.user_type}"
        )


def validate_credit_type(credit_type, valid_credit_type):
    if credit_type != valid_credit_type:
        raise ValidationError("Invalid credit type")


def validate_credit_date(credit_date):
    if credit_date > dateutils.current_date():
        raise ValidationError("Credit date of future is not allowed")


def is_payment_mode_supported_in_csv_uploads(payment_mode):
    if payment_mode == ARPaymentMode.WRITE_OFF:
        raise ValidationError(f"Payment mode {payment_mode} not supported")


def validate_credit_reversal(credit_type):
    if credit_type == CreditType.CREDIT_REVERSAL:
        raise ValidationError(
            f"Settlement cannot be created for Credit Type {credit_type}"
        )
