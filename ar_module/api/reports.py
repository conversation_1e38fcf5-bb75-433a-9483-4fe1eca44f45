from ar_module.api import ApiResponse
from ar_module.api.blueprints import ar_bp
from ar_module.api.schemas.request.report import (
    DebtorSummaryReportData,
    DebtorSummaryReportRequestSchema,
    MultipleDateRangeSearchReportRequestSchema,
    PullPaymentDataSchema,
    PushPaymentReportSchema,
    SearchCollectionReportRequestSchema,
    SearchLedgerReportRequestSchema,
    SearchReceivableReportRequestSchema,
)
from ar_module.api.schemas.response.report import (
    CollectionReportCreditResponseSchema,
    CollectionReportResponseSchema,
    DebtorLedgerPreview,
    DebtorLedgerReportResponseSchema,
    ReceivableReportResponseSchema,
)
from ar_module.application.services.reporting.collection_report.collection_report import (
    CollectionReportApplicationService,
)
from ar_module.application.services.reporting.debtor_ledger_report.ledger_report import (
    LedgerReportApplicationService,
)
from ar_module.application.services.reporting.debtor_summary_reports.debtor_summary_report_scheduler import (
    DebtorSummaryReportScheduler,
)
from ar_module.application.services.reporting.dtos import DebtorSummaryRequestData
from ar_module.application.services.reporting.receivable_report.receivable_report import (
    ReceivableReportApplicationService,
)
from ar_module.async_job.job.job_constants import JobStatus
from ar_module.async_job.job_scheduler_service import JobSchedulerService
from ar_module.core.common.globals import global_context
from ar_module.reporting.finance_erp_reporting.finance_erp_reporting_service import (
    FinanceERPReportingService,
)
from ar_module.request_parsers import (
    RequestTypes,
    read_user_data_from_request_header,
    schema_wrapper_parser,
)
from object_registry import inject


@ar_bp.route("/reports/collections-reports", methods=["GET"])
@schema_wrapper_parser(
    SearchCollectionReportRequestSchema, param_type=RequestTypes.ARGS
)
@inject(collection_report_app_service=CollectionReportApplicationService)
def get_collection_reports(collection_report_app_service, parsed_request):
    """
    Fetch collection report for all the debtors in a given time period
    ---
    operationId: get_collection_reports
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: True
              schema: SearchCollectionReportRequestSchema
        description: Fetch collection reports for all the debtors for given time period

            To get detailed report also pass level = detailed
        tags:
            - Report
        responses:
            200:
                description: Fetch collection reports and group it month wise
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/CollectionReportResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    collection_report = collection_report_app_service.get_collection_report(
        parsed_request
    )
    collection_report_response_schema = CollectionReportResponseSchema()
    response = collection_report_response_schema.dump(collection_report)
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/reports/debtor-ledger-reports", methods=["GET"])
@schema_wrapper_parser(SearchLedgerReportRequestSchema, param_type=RequestTypes.ARGS)
@inject(ledger_report_app_service=LedgerReportApplicationService)
def get_debtor_ledger_report(ledger_report_app_service, parsed_request):
    """
    Fetch debtor ledger report for all the debtors in a given time period
    ---
    operationId: get_debtor_ledger_report
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: True
              schema: SearchLedgerReportRequestSchema
        description: Fetch debtor ledger reports for all the debtors for given time period

            To get detailed report also pass level = detailed
        tags:
            - Report
        responses:
            200:
                description: Fetch debtor ledger reports
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/DebtorLedgerReportResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """

    user_data = read_user_data_from_request_header()
    # Ensure hotel_id is set in request_data for correct filtering
    if parsed_request.hotel_id is None and user_data.hotel_id is not None:
        parsed_request.hotel_id = user_data.hotel_id
    debtor_ledger_report = ledger_report_app_service.get_debtor_ledger_report(
        parsed_request, user_data
    )
    debtor_ledger_report_response_schema = DebtorLedgerReportResponseSchema()
    response = debtor_ledger_report_response_schema.dump(debtor_ledger_report)
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/reports/multiple-date-collections-reports", methods=["POST"])
@schema_wrapper_parser(MultipleDateRangeSearchReportRequestSchema)
@inject(collection_report_app_service=CollectionReportApplicationService)
def get_collection_reports_for_multiple_date_range(
    collection_report_app_service, parsed_request
):
    """Search for collection report with multiple date ranges.
    ---
    operationId: get_collection_reports_for_multiple_date_range
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Generates summary report for multiple date ranges.
        tags:
            - Report
        parameters:
            - in: body
              name: body
              description: Date ranges for which report needed to be generated
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/MultipleDateRangeSearchReportRequestSchema"
        responses:
            200:
                description: Collection report for multiple date ranges
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/CollectionReportResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    collection_report = collection_report_app_service.get_collection_reports(
        parsed_request
    )
    collection_report_response_schema = CollectionReportResponseSchema()
    response = collection_report_response_schema.dump(collection_report)
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/reports/receivable-reports", methods=["GET"])
@schema_wrapper_parser(
    SearchReceivableReportRequestSchema, param_type=RequestTypes.ARGS
)
@inject(receivable_app_service=ReceivableReportApplicationService)
def get_receivable_reports(receivable_app_service, parsed_request):
    """
    Fetch receivable report for all the debtors in a given time period
    ---
    operationId: get_receivable_reports
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: True
              schema: SearchReceivableReportRequestSchema
        description: Fetch receivable reports for all the debtors for given time period
        tags:
            - Report
        responses:
            200:
                description: Fetch receivable reports
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/ReceivableReportResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    receivable_report = receivable_app_service.get_receivable_report_v1(
        parsed_request, user_data
    )
    receivable_report_response_schema = ReceivableReportResponseSchema()
    if receivable_report:
        response = receivable_report_response_schema.dump(
            dict(
                month_wise_summary=receivable_report.summarised_report,
                detailed=receivable_report.detailed_report,
                summarised_report_download_url=receivable_report.summarised_report_download_url,
                detailed_report_download_url=receivable_report.detailed_report_download_url,
            )
        )
        return ApiResponse.build(data=response.data, status_code=200)
    else:
        return ApiResponse.build(data=[], status_code=200)


@ar_bp.route("/reports/collections-reports/credit-date", methods=["GET"])
@schema_wrapper_parser(
    SearchCollectionReportRequestSchema, param_type=RequestTypes.ARGS
)
@inject(collection_report_app_service=CollectionReportApplicationService)
def get_collection_reports_by_credit_date(
    collection_report_app_service, parsed_request
):
    """
    Fetch collection report for all the debtors in a given time period
    ---
    operationId: get_collection_reports_by_credit_created_at
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    get:
        parameters:
            - name: search_criteria
              in: query
              required: True
              schema: SearchCollectionReportRequestSchema
        description: Fetch payment details in given time period

        tags:
            - Report
        responses:
            200:
                description: Fetch collection reports collection details
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/CollectionReportCreditResponseSchema"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    collection_report_credit_details = (
        collection_report_app_service.get_collection_summary_by_credit_date(
            parsed_request
        )
    )
    collection_report_credit_response_schema = CollectionReportCreditResponseSchema()
    response = collection_report_credit_response_schema.dump(
        collection_report_credit_details
    )
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/reports/async-debtor-summary-reports", methods=["POST"])
@schema_wrapper_parser(DebtorSummaryReportRequestSchema)
@inject(report_scheduler=DebtorSummaryReportScheduler)
def schedule_debtor_summary_report_generation(
    report_scheduler: DebtorSummaryReportScheduler, parsed_request
):
    """
    Schedule various debtor summary reports
    ---
    operationId: schedule_debtor_summary_report_generation
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Schedule Async report generation job
        tags:
            - Reports
        parameters:
            - in: body
              name: body
              description: Request to schedule the job
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/DebtorSummaryReportRequestSchema"
        responses:
            200:
                description: Scheduled Job id
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                job_id:
                                    type: string
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    job_id = report_scheduler.schedule(parsed_request, user_data)
    return ApiResponse.build(data=dict(job_id=job_id), status_code=200)


@ar_bp.route("/reports/preview-debtor-summary", methods=["POST"])
@schema_wrapper_parser(DebtorSummaryReportData)
@inject(ledger_report=LedgerReportApplicationService)
def preview_debtor_summary(
    ledger_report: LedgerReportApplicationService, parsed_request
):
    """
    Return debtor_summary
    ---
    operationId: preview_debtor_summary
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Preview debtor summary
        tags:
            - Reports
        parameters:
            - in: body
              name: body
              description: Request to view the report summary
              required: True
              schema:
                type: object
                properties:
                    data:
                        $ref: "#/definitions/DebtorSummaryReportData"
        responses:
            200:
                description: Scheduled Job id
                schema:
                    type: object
                    properties:
                        data:
                            $ref: "#/definitions/DebtorLedgerPreview"
                        meta:
                            type: object
                            additionalProperties: {}
                        errors:
                            type: array
                            items:
                                $ref: "#/definitions/ApiErrorSchema"
    """
    user_data = read_user_data_from_request_header()
    request_dto = DebtorSummaryRequestData(**parsed_request)
    report, summary_email_recipients = ledger_report.generate_debtor_summary(
        request_dto, user_data
    )
    response = DebtorLedgerPreview().dump(
        dict(
            total_credits=report.total_credits,
            total_unsettled_debits=report.total_unsettled_debits,
            total_unused_credits=report.total_unused_credits,
            summary_email_recipients=summary_email_recipients,
        )
    )
    return ApiResponse.build(data=response.data, status_code=200)


@ar_bp.route("/reports/erp-payment-reporting", methods=["POST"])
@schema_wrapper_parser(PushPaymentReportSchema, param_type=RequestTypes.ARGS)
@inject(job_scheduler_service=JobSchedulerService)
def push_payments_report_to_erp(
    job_scheduler_service: JobSchedulerService, parsed_request
):
    """Finance ERP Report Push
    ---
    operationId: push_finance_reports
    post:
        parameters:
            - in: query
              name: filter
              required: False
              schema:
                $ref: "#/components/schemas/PushPaymentReportSchema"
        tags:
            - Reporting
        responses:
            200: None
    """
    job_aggregate = job_scheduler_service.create_bulk_push_payments_job(
        None, parsed_request
    )
    return ApiResponse.build(
        data=dict(job_id=job_aggregate.job_id, status=JobStatus.CREATED),
        status_code=200,
    )


@ar_bp.route("/reports/pull-payment-report", methods=["POST"])
@schema_wrapper_parser(PullPaymentDataSchema)
@inject(
    finance_erp_reporting_service=FinanceERPReportingService,
)
def pull_payment_data(finance_erp_reporting_service, parsed_request):
    """Pull Payment Data for Reporting
    ---
    operationId: pull_payment_data
    consumes:
        - application/json
    produces:
        - application/json
    schemes: ['http', 'https']
    deprecated: false
    post:
        parameters:
            - in: body
              name: body
              description: Pull Financial Data
              required: True
              schema:
                type: object
                properties:
                    data:
                        type: object
                        items:
                            $ref: "#/components/schemas/PullPaymentDataSchema"
        tags:
            - Reporting
        responses:
            200:
                description: Successful Pull of the financial data
    """
    response = finance_erp_reporting_service.pull_payment_report(parsed_request)
    return ApiResponse.build(data=response, status_code=200)
