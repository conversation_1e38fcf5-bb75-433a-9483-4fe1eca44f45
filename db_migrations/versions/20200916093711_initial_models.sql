-- revision: '20200916093711_initial_models'
-- down_revision: ''

-- upgrade
CREATE TABLE debtor (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted boolean,
    debtor_id varchar NOT NULL,
    hotel_id varchar,
    debtor_code varchar NOT NULL,
    debtor_name varchar NOT NULL
);

CREATE UNIQUE INDEX hotel_level_debtor_uc ON debtor (debtor_code, hotel_id) WHERE (hotel_id IS NOT NULL);
CREATE UNIQUE INDEX tenant_level_debtor_uc ON debtor (debtor_code) WHERE (hotel_id IS NULL);
ALTER TABLE debtor ADD CONSTRAINT debtor_pkey PRIMARY KEY (debtor_id);


CREATE TABLE credit (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    credit_date date NOT NULL,
    deleted boolean,
    reference_id varchar,
    mode_of_credit varchar,
    credit_id varchar NOT NULL,
    base_currency varchar NOT NULL,
    unused_credit_amount numeric(15,4),
    amount_in_credit_currency numeric(15,4) NOT NULL,
    credit_currency varchar NOT NULL,
    amount_in_base_currency numeric(15,4) NOT NULL,
    debtor_id varchar NOT NULL,
    credit_type varchar NOT NULL,
    reference_number varchar
);

ALTER TABLE credit ADD CONSTRAINT credit_pkey PRIMARY KEY (credit_id);
ALTER TABLE credit ADD CONSTRAINT fk_credit_debtor FOREIGN KEY (debtor_id) REFERENCES debtor(debtor_id);


CREATE TABLE debit (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    debit_date date NOT NULL,
    deleted boolean,
    posttax_amount numeric(15,4) NOT NULL,
    debit_id varchar NOT NULL,
    reference_number varchar NOT NULL UNIQUE,
    reference_id varchar UNIQUE,
    settlement_status varchar NOT NULL,
    currency varchar NOT NULL,
    debtor_id varchar NOT NULL,
    pretax_amount numeric(15,4) NOT NULL,
    tax_amount numeric(15,4) NOT NULL,
    unsettled_amount numeric(15,4)
);

ALTER TABLE debit ADD CONSTRAINT debit_pkey PRIMARY KEY (debit_id);
ALTER TABLE debit ADD CONSTRAINT fk_debit_debtor FOREIGN KEY (debtor_id) REFERENCES debtor(debtor_id);


CREATE TABLE settlement (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    settlement_id integer,
    settlement_date date NOT NULL,
    deleted boolean,
    credit_id varchar NOT NULL,
    currency varchar NOT NULL,
    settled_via varchar NOT NULL,
    debit_id varchar NOT NULL,
    amount numeric(15,4) NOT NULL,
    reference_number varchar
);

ALTER TABLE settlement ADD CONSTRAINT settlement_pkey PRIMARY KEY (debit_id, settlement_id);
ALTER TABLE settlement ADD CONSTRAINT fk_settlement_credit FOREIGN KEY (credit_id) REFERENCES credit(credit_id);

-- downgrade
ALTER TABLE settlement DROP CONSTRAINT fk_settlement_credit;
ALTER TABLE settlement DROP CONSTRAINT settlement_pkey;
DROP TABLE settlement;

ALTER TABLE debit DROP CONSTRAINT fk_debit_debtor;
ALTER TABLE debit DROP CONSTRAINT debit_pkey;
DROP TABLE debit;

ALTER TABLE credit DROP CONSTRAINT fk_credit_debtor;
ALTER TABLE credit DROP CONSTRAINT credit_pkey;
DROP TABLE credit;

ALTER TABLE debtor DROP CONSTRAINT debtor_pkey;
DROP INDEX tenant_level_debtor_uc;
DROP INDEX hotel_level_debtor_uc;
DROP TABLE debtor;
