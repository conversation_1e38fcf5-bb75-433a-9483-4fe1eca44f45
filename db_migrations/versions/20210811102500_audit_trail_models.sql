-- revision: '20210811102500_audit_trail_models'
-- down_revision: '20201020142214_debit_template_url'

-- upgrade
create TABLE credit_audit_trail (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    audit_id character varying NOT NULL,
    credit_id character varying NOT NULL,
    hotel_id character varying,
    "user" character varying,
    user_type character varying,
    request_id character varying,
    debtor_name character varying,
    debtor_code character varying,
    credit_mode character varying,
    credit_currency character varying,
    credit_amount numeric(15,4),
    credit_date DATE,
    application character varying,
    deleted boolean,
    PRIMARY KEY (audit_id));

create TABLE settlement_audit_trail (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    audit_id character varying NOT NULL,
    credit_id varchar NOT NULL,
    hotel_id character varying,
    "user" character varying,
    user_type character varying,
    request_id character varying,
    debtor_name character varying,
    debtor_code character varying,
    payment_reference_number character varying,
    settled_debit_count character varying,
    settlements JSONB,
    application character varying,
    deleted boolean,
    PRIMARY KEY (audit_id));

create TABLE debit_audit_trail (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    audit_id character varying NOT NULL,
    debit_id character varying NOT NULL,
    hotel_id character varying,
    "user" character varying,
    user_type character varying,
    request_id character varying,
    debtor_name character varying,
    debtor_code character varying,
    reference_number character varying,
    pretax_amount numeric(15,4),
    tax_amount numeric(15,4),
    application character varying,
    deleted boolean,
    PRIMARY KEY (audit_id));
-- downgrade

drop table credit_audit_trail;
drop table settlement_audit_trail;
drop table debit_audit_trail;