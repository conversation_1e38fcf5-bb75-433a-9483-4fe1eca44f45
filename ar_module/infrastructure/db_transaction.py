from functools import wraps

from treebo_commons.multitenancy.sqlalchemy import db_engine

from ar_module.infrastructure.database.base_repository import handle_database_exception


def unit_of_work(func):
    """
    Handle DB commits
    :param func:
    :return:
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        """
        Wrapper
        :param args:
        :param kwargs:
        :return:
        """
        try:
            r_val = func(*args, **kwargs)
            db_engine.get_session().commit()
            return r_val
        except Exception as e:
            db_engine.get_session().rollback()
            handle_database_exception(e)
            raise e

    return wrapper
