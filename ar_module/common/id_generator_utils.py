import datetime
import random
import secrets


def random_id_generator(prefix=None):
    """

    Returns: a random generated prefixed number

    """
    created_at = datetime.datetime.utcnow()
    part1 = created_at.strftime("%d%m")
    part2 = created_at.strftime("%H%M")

    number = random.randint(0, 999999999)
    part4 = int(number % 10000)
    number = (number - part4) / 10000
    part3 = int(number % 10000)
    part3 += created_at.month * 10
    if prefix:
        parts = [
            prefix,
            part1,
            part2,
            str(part3).rjust(2, "0") + str(part4).rjust(3, "0"),
        ]
    else:
        parts = [part1, part2, str(part3).rjust(2, "0") + str(part4).rjust(3, "0")]
    return "-".join(parts)


def get_random_string(
    length=12,
    allowed_chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
):
    """
    Return a securely generated random string.
    The bit length of the returned value can be calculated with the formula:
        log_2(len(allowed_chars)^length)
    For example, with default `allowed_chars` (26+26+10), this gives:
      * length: 12, bit length =~ 71 bits
      * length: 22, bit length =~ 131 bits
    """
    return "".join(secrets.choice(allowed_chars) for i in range(length))
