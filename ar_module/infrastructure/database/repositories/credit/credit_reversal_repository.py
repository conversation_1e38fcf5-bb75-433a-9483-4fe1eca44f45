from sqlalchemy import or_

from ar_module.domain.aggregates.credit_aggregate import CreditAggregate
from ar_module.domain.constants import CreditStatus
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.credit_model import (
    CreditReversalMappingModel,
)
from ar_module.infrastructure.database.repositories.credit.db_adaptors.credit_reversal_adaptor import (
    CreditReversalAdaptor,
)
from object_registry import register_instance


@register_instance()
class CreditReversalRepository(BaseRepository):
    credit_reversal_adaptor = CreditReversalAdaptor()

    def from_aggregate(self, aggregate: CreditAggregate = None):
        credit_reversals = aggregate.get_credit_reversals()
        credit_reversal_models = [
            self.credit_reversal_adaptor.to_db_entity(reversal)
            for reversal in credit_reversals
        ]
        return credit_reversal_models

    def save_all(self, aggregate: CreditAggregate):
        credit_reversal_model = self.from_aggregate(aggregate)
        self._save_all(credit_reversal_model)
        self.flush_session()

    def update_all(self, aggregate: CreditAggregate):
        credit_reversal_model = self.from_aggregate(aggregate)
        self._update_all(credit_reversal_model)
        self.flush_session()

    def get_credit_reversal_details(self, credit_ids):
        credit_reversal_models = (
            self.query(CreditReversalMappingModel)
            .filter(
                or_(
                    CreditReversalMappingModel.refund_credit_id.in_(credit_ids),
                    CreditReversalMappingModel.payment_credit_id.in_(credit_ids),
                ),
                CreditReversalMappingModel.deleted.is_(False),
                CreditReversalMappingModel.status == CreditStatus.CREATED,
            )
            .all()
        )
        return [
            self.credit_reversal_adaptor.to_domain_entity(db_model)
            for db_model in credit_reversal_models
        ]
