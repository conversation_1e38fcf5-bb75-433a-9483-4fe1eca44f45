from treebo_commons.utils import dateutils

from ar_module.application.dtos.invoice_dto import InvoiceDto
from ar_module.common.id_generator_utils import random_id_generator
from ar_module.domain.aggregates.debit_aggregate import DebitAggregate
from ar_module.domain.constants import DEFAULT_CREDIT_SETTINGS, DebitType
from ar_module.domain.entities.debit import Debit


class DebitFactory:
    @staticmethod
    def create_new_debit(debit_data: dict, debtor_data=None):
        debit_id = random_id_generator(prefix="DBT")
        due_date = None
        if debtor_data and debit_data.get("debit_date"):
            credit_period = (
                int(debtor_data.credit_period)
                if debtor_data.credit_period
                else DEFAULT_CREDIT_SETTINGS["credit_period"]
            )
            due_date = dateutils.add(debit_data.get("debit_date"), days=credit_period)
        debit = Debit(
            debit_id=debit_id,
            debtor_id=debit_data.get("debtor_id"),
            debit_date=debit_data.get("debit_date"),
            due_date=debit_data.get("due_date") or due_date,
            debit_amount=debit_data.get("debit_amount"),
            reference_number=debit_data.get("reference_number"),
            debit_type=debit_data.get("debit_type") or DebitType.CREDIT_INVOICE,
        )
        debit_aggregate = DebitAggregate(debit=debit, settlements=[])
        return debit_aggregate

    @staticmethod
    def create_from_invoice(invoice_dto: InvoiceDto):
        debit_id = random_id_generator(prefix="DBT")
        debit = Debit(
            debit_id=debit_id,
            debtor_id=invoice_dto.debtor_id,
            debit_date=invoice_dto.invoice_date,
            debit_amount=invoice_dto.invoice_amount,
            reference_number=invoice_dto.invoice_number,
            debit_type=DebitType.SPOT_CREDIT_INVOICE
            if invoice_dto.is_spot_credit
            else DebitType.CREDIT_INVOICE,
            reference_id=invoice_dto.invoice_id,
            debit_template_url=invoice_dto.invoice_url,
            due_date=invoice_dto.due_date,
            remarks=invoice_dto.remarks,
            auto_settled_via_credit=invoice_dto.auto_settled_via_credit,
            booking_reference_number=invoice_dto.booking_reference_number,
            tenant_id=invoice_dto.tenant_id,
            hotel_id=invoice_dto.vendor_id,
            bill_to_debtor_code=invoice_dto.bill_to_debtor_code,
            invoice_meta=invoice_dto.invoice_meta,
        )
        return DebitAggregate(debit=debit, settlements=[])

    @staticmethod
    def create_from_debit_dto(debit_dto):
        debit_id = random_id_generator(prefix="DBT")
        debit = Debit(
            debit_id=debit_id,
            debtor_id=debit_dto.debtor_id,
            debit_date=debit_dto.debit_date,
            due_date=debit_dto.due_date,
            debit_amount=debit_dto.debit_amount,
            reference_number=debit_dto.reference_number,
            debit_type=debit_dto.debit_type or DebitType.CREDIT_INVOICE,
            reference_id=debit_dto.reference_id,
            recorded_via=debit_dto.recorded_via,
            tenant_id=debit_dto.tenant_id,
        )
        debit_aggregate = DebitAggregate(debit=debit, settlements=[])
        return debit_aggregate
