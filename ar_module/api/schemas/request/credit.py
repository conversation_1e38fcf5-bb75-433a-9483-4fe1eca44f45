from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from marshmallow.validate import OneOf
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas.base_schema import CreditReversalDetailsSchema
from ar_module.api.schemas.request.settlement import SettlementRequestSchema
from ar_module.api.schemas.validators import (
    UserDefinedEnumValidator,
    validate_positive_integer,
)
from ar_module.application.dtos.credit_dto import CreditDto
from ar_module.domain.constants import (
    CREDIT_PAYMENT_TYPES,
    ARModuleConfigs,
    CreditType,
    SortOrders,
)


class CreditRequestSchema(Schema):
    debtor_id = fields.String(required=True)
    credit_type = fields.String(
        validate=[OneOf([CreditType.PAYMENT] + CREDIT_PAYMENT_TYPES)]
    )
    date = fields.Date()
    reference_number = fields.String(allow_none=True)
    amount_in_base_currency = MoneyField(validate=validate_positive_integer)
    amount_in_credit_currency = MoneyField(validate=validate_positive_integer)
    mode_of_credit = fields.String()
    settlements = fields.Nested(SettlementRequestSchema, many=True)
    approval_document = fields.String()

    @post_load
    def create_object(self, data):
        return CreditDto(
            debtor_id=data.get("debtor_id"),
            credit_type=data.get("credit_type"),
            date=data.get("date"),
            amount_in_base_currency=data.get("amount_in_base_currency"),
            reference_number=data.get("reference_number").strip()
            if data.get("reference_number")
            else None,
            mode_of_credit=data.get("mode_of_credit"),
            amount_in_credit_currency=data.get("amount_in_credit_currency"),
            settlements=data.get("settlements"),
            approval_document=data.get("approval_document"),
        )


class CreditSearchSchema(Schema):
    debtor_id = fields.String(
        required=False, description="Debtor ID for which credits needs to be fetched."
    )
    credit_id = fields.String(
        required=False, description="Credit ID for which credits needs to be fetched."
    )
    credit_types = fields.String(description="Comma-separated list of credit types")
    reference_number = fields.String(
        description="Filter credits based on credit reference number, which can be payment_reference_number, "
        "or credit_note_number"
    )
    has_unused_credit = fields.Boolean(
        description="When true, returns only those credits for which there are some unused credit amount. When false, "
        "excludes those credits with unused credit amount"
    )
    used_to_auto_settle_debit = fields.Boolean(
        description="When true, returns only those credits which were used to auto settle debits. When false, "
        "excludes those credits which were used to auto settle debits",
        required=False,
    )
    from_date = fields.Date(required=False)
    to_date = fields.Date(required=False)
    limit = fields.Integer(description="Number of results required", allow_none=True)
    offset = fields.Integer(
        description="Offset from which results are required", allow_none=True
    )
    sort_order = fields.String(
        validate=OneOf(
            SortOrders.all(),
            error="Sort Order is not valid. Allowed values are 'asc' and 'desc'",
        ),
        description="Sort order for the results. Allowed values are 'asc' and 'desc'",
        allow_none=True,
    )
    show_cancelled_credits = fields.Boolean(
        description="When true, will show cancelled credits as well"
    )
    should_send_approval_document_url = fields.Boolean(
        description="When true, will send approval document url as well"
    )
    should_send_credit_transaction_details = fields.Boolean(
        description="When true, will send refund details of this"
    )

    @post_load
    def parse_credit_types(self, data, **kwargs):
        if "credit_types" in data:
            data["credit_types"] = data["credit_types"].split(",")
            allowed_types = set(CreditType.all() + CREDIT_PAYMENT_TYPES)
            if not all(item in allowed_types for item in data["credit_types"]):
                raise ValidationError(
                    f"Invalid credit type. Allowed types: {allowed_types}"
                )
        return data

    @validates_schema
    def validate_data(self, data):
        if bool(data.get("from_date")) ^ bool(data.get("to_date")):
            raise ValidationError(
                "Date Range search should contain both from_date and to_date"
            )


class CreditReversalRequestSchema(Schema):
    debtor_id = fields.String(required=True)
    credit_type = fields.String(
        required=True, validate=[OneOf([CreditType.CREDIT_REVERSAL])]
    )
    date = fields.Date()
    reference_number = fields.String(allow_none=True)
    mode_of_credit = fields.String()
    amount_in_base_currency = MoneyField(validate=validate_positive_integer)
    amount_in_credit_currency = MoneyField(validate=validate_positive_integer)
    remarks = fields.String(
        description="Remarks on why refund is passed", required=False, allow_none=True
    )
    credit_reversals = fields.List(
        fields.Nested(CreditReversalDetailsSchema), required=True
    )


class CreditCancelRequestSchema(Schema):
    cancellation_reason = fields.String(
        validate=UserDefinedEnumValidator(ARModuleConfigs.PAYMENT_CANCELLATION_ENUM),
        required=True,
        error_messages={
            "null": "Payment mode may not be null.",
            "required": "Please provide payment mode.",
            "validator_failed": "'{input}' is not a valid choice for Payment Mode",
        },
    )
