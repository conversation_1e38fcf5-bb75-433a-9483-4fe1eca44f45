-- revision: '20231207105658_add_new_audit_trail_table'
-- down_revision: '20231128162955_add_status_column_in_credit_table'

-- upgrade
CREATE TABLE audit_trail (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    audit_id character varying NOT NULL,
    hotel_id character varying,
    "user" character varying,
    user_type character varying,
    request_id character varying,
    deleted boolean,
    audit_type character varying,
    audit_payload JSONB,
    PRIMARY KEY (audit_id));

-- downgrade
DROP TABLE audit_trail;
