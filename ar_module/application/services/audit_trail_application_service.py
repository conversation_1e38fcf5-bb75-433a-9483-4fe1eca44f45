import logging

from marshmallow import ValidationError
from treebo_commons.utils import dateutils

from ar_module.application.dtos.settlement_dto import SettlementDto
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.domain.constants import AuditType
from ar_module.domain.dtos.audit_trail_dtos import (
    CancelCreditReversalAuditTrailDTO,
    CreateCreditReversalAuditTrailDTO,
    CreateCreditReversalMappedAuditTrailDTO,
    CreditAuditTrailDTO,
    DebitAuditTrailDTO,
    DebtorCommunicationAuditTrailDTO,
    SettlementAuditTrailDTO,
)
from ar_module.domain.factories.audit_trail_factories import AuditTrailFactory
from ar_module.infrastructure.database.repositories.audit_trail.audit_trail_repository import (
    AuditTrailRepository,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[AuditTrailRepository, TenantSettings])
class AuditTrailService(object):
    def __init__(
        self, audit_trail_repo: AuditTrailRepository, tenant_settings: TenantSettings
    ):
        self.audit_trail_repo = audit_trail_repo
        self.tenant_settings = tenant_settings

    def search_audits(
        self, filter_criteria=None, get_total_count=False, user_data=None
    ):
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        hotel_id = filter_criteria.get("hotel_id") or user_data.hotel_id
        if is_hotel_level_ar_configured:
            if not hotel_id:
                raise ValidationError(
                    "Hotel id is mandatory if ar is configured on hotel level"
                )
            filter_criteria["hotel_id"] = hotel_id
        else:
            filter_criteria["hotel_id"] = filter_criteria.get("hotel_id")
        audit_aggregates = self.audit_trail_repo.search_audits(
            filter_criteria=filter_criteria
        )
        total_audits = (
            self.audit_trail_repo.get_audit_trail_count(filter_criteria=filter_criteria)
            if get_total_count
            else None
        )
        return audit_aggregates, total_audits

    def create_credit_audit_trail(
        self,
        debtor_aggregate,
        credit_aggregate,
        hotel_id,
        audit_type,
        user_data=None,
        debit_aggregates=None,
    ):
        mapped_invoices = None
        if debit_aggregates:
            mapped_invoices = [
                debit_aggregate.debit.reference_number
                for debit_aggregate in debit_aggregates
            ]
            mapped_invoices = ",".join(mapped_invoices)
        audit_payload = CreditAuditTrailDTO(
            debtor_aggregate, credit_aggregate, mapped_invoices
        ).to_json()
        audit_trail_aggregate = AuditTrailFactory.create_audit_trail(
            user_data.user if user_data else None,
            user_data.user_type if user_data else None,
            hotel_id,
            audit_type,
            audit_payload,
        )
        self.audit_trail_repo.save(audit_trail_aggregate)

    def create_debit_audit_trail(
        self,
        debit_aggregate,
        debtor_aggregate_billed_le,
        hotel_id,
        audit_type,
        debtor_aggregate_booker_le=None,
        booking_id=None,
        user_data=None,
    ):
        audit_payload = DebitAuditTrailDTO(
            debit_aggregate,
            debtor_aggregate_billed_le,
            debtor_aggregate_booker_le,
            booking_id,
        ).to_json()
        audit_trail_aggregate = AuditTrailFactory.create_audit_trail(
            user_data.user if user_data else None,
            user_data.user_type if user_data else None,
            hotel_id,
            audit_type,
            audit_payload,
        )
        self.audit_trail_repo.save(audit_trail_aggregate)

    def create_settlement_audit_trail(
        self,
        debtor_aggregate,
        debit_aggregate,
        settlement,
        credit_aggregate,
        hotel_id,
        audit_type,
        user_data=None,
        is_settlement_cancelled_due_to_refund=False,
    ):
        settlement = SettlementDto(
            reference_number=debit_aggregate.debit.reference_number,
            debit_date=dateutils.date_to_ymd_str(debit_aggregate.debit.debit_date),
            applied_amount=float(settlement.amount.amount),
            debit_id=debit_aggregate.debit.debit_id,
        ).to_json()
        audit_payload = SettlementAuditTrailDTO(
            debtor_aggregate,
            credit_aggregate,
            settlement,
            is_settlement_cancelled_due_to_refund,
        ).to_json()
        audit_trail_aggregate = AuditTrailFactory.create_audit_trail(
            user_data.user if user_data else None,
            user_data.user_type if user_data else None,
            hotel_id,
            audit_type,
            audit_payload,
        )
        self.audit_trail_repo.save(audit_trail_aggregate)

    def create_debtor_communication_audit_trail(
        self,
        communication_type,
        debtor_aggregate,
        audit_type,
        user_data=None,
    ):
        audit_payload = DebtorCommunicationAuditTrailDTO(
            communication_type,
            debtor_aggregate,
        ).to_json()
        audit_trail_aggregate = AuditTrailFactory.create_audit_trail(
            user_data.user if user_data else None,
            user_data.user_type if user_data else None,
            user_data.hotel_id if user_data.hotel_id else None,
            audit_type,
            audit_payload,
        )
        self.audit_trail_repo.save(audit_trail_aggregate)

    def create_credit_reversal_audit_trail(
        self, credit_aggregate, debtor_aggregate, hotel_id, user_data=None
    ):
        user = user_data.user if user_data else None
        user_type = user_data.user_type if user_data else None

        audit_trail_aggregates = [
            AuditTrailFactory.create_audit_trail(
                user,
                user_type,
                hotel_id,
                AuditType.CREDIT_REVERSAL_CREATED,
                CreateCreditReversalAuditTrailDTO(
                    credit_aggregate, debtor_aggregate
                ).to_json(),
            )
        ]

        audit_trail_aggregates.extend(
            AuditTrailFactory.create_audit_trail(
                user,
                user_type,
                hotel_id,
                AuditType.CREDIT_REVERSAL_MAPPED,
                audit_payload,
            )
            for audit_payload in CreateCreditReversalMappedAuditTrailDTO(
                credit_aggregate, debtor_aggregate
            ).to_json()
        )

        self.audit_trail_repo.save_all(audit_trail_aggregates)

    def cancel_credit_reversal_audit_trail(
        self,
        credit_aggregate,
        debtor_aggregate,
        hotel_id,
        user_data=None,
    ):
        user = user_data.user if user_data else None
        user_type = user_data.user_type if user_data else None

        audit_trail_aggregates = [
            AuditTrailFactory.create_audit_trail(
                user,
                user_type,
                hotel_id,
                AuditType.CREDIT_REVERSAL_CANCELLED,
                CancelCreditReversalAuditTrailDTO(
                    credit_aggregate, debtor_aggregate
                ).to_json(),
            )
        ]

        audit_trail_aggregates.extend(
            AuditTrailFactory.create_audit_trail(
                user,
                user_type,
                hotel_id,
                AuditType.CREDIT_REVERSAL_UNMAPPED,
                audit_payload,
            )
            for audit_payload in CreateCreditReversalMappedAuditTrailDTO(
                credit_aggregate, debtor_aggregate
            ).to_json()
        )

        self.audit_trail_repo.save_all(audit_trail_aggregates)
