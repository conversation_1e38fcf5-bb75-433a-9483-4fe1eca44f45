import logging
import os
import uuid

from marshmallow import ValidationError
from treebo_commons.money import Money
from treebo_commons.utils import dateutils

from ar_module.application.dtos.receivable_report_dto import (
    DetailedReceivableReportDto,
    MonthWiseReceivableReportDto,
    MonthWiseSummaryReportDto,
    ReceivableReportAndURLDto,
    ReceivableReportDto,
    ReceivableReportDtoV2,
    SummarisedReceivableReportDto,
)
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.common.utils import get_month_wise_date_range_breakup
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)
RECEIVABLE_REPORT_FOLDER_NAME = "receivable_reports/"


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        CatalogServiceClient,
        DebtorService,
        TenantSettings,
    ]
)
class ReceivableReportApplicationService(object):
    SUMMARISED_RECEIVABLE_REPORT_COLUMNS = [
        "start_date",
        "end_date",
        "total_gross_receivables",
        "total_net_receivables",
        "total_unapplied_payments",
        "debtors_count",
        "days_of_sales_os",
        "aor",
    ]

    DETAILED_RECEIVABLE_REPORT_COLUMNS = [
        "debtor_code",
        "debtor_type",
        "debtor_name",
        "total_credits",
        "total_provisional_credits",
        "unapplied_payment",
        "gross_receivables",
        "net_receivables",
        "oldest_due",
    ]

    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        catalog_client: CatalogServiceClient,
        debtor_service: DebtorService,
        tenant_settings: TenantSettings,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.catalog_client = catalog_client
        self.debtor_service = debtor_service
        self.tenant_settings = tenant_settings

    def get_receivable_report_v1(self, request_data, user_data=None):
        debtor_aggregates = self.debtor_service.search_debtors(
            hotel_id=request_data.hotel_id,
            debtor_code=request_data.debtor_code,
            debtor_type=request_data.debtor_type,
            user_data=user_data,
        )
        debtors = [aggregate.debtor for aggregate in debtor_aggregates]

        if not debtors:
            return []
        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        if hotel_level_accounts_receivable and not request_data.hotel_id:
            raise ValidationError(
                "Hotel id is mandatory if ar is configured on hotel level"
            )
        base_currency = self.tenant_settings.get_chain_base_currency()

        if hotel_level_accounts_receivable:
            base_currency = self.catalog_client.get_hotel_base_currency(
                request_data.hotel_id
            )
        if request_data.max_balance:
            request_data.min_balance = Money(
                request_data.min_balance.amount, base_currency
            )
            request_data.max_balance = Money(
                request_data.max_balance.amount, base_currency
            )
        month_wise_date_breakup = get_month_wise_date_range_breakup(
            request_data.start_date, request_data.end_date
        )
        summarised_report = self.get_summarised_receivable_report(
            debtors, month_wise_date_breakup, base_currency
        )
        summarised_report_download_url = (
            self.generate_report_download_url(
                report_data=summarised_report,
                column_values=self.SUMMARISED_RECEIVABLE_REPORT_COLUMNS,
                level="summarised",
            )
            if summarised_report
            else None
        )
        detailed_report_download_url = None
        detailed_report = []
        if request_data.include_detail:
            detailed_report = self._get_detailed_receivable_report(
                debtors,
                base_currency,
                end_date=request_data.reference_date,
                filter_criteria=request_data,
            )
            detailed_report_download_url = (
                self.generate_report_download_url(
                    report_data=detailed_report,
                    column_values=self.DETAILED_RECEIVABLE_REPORT_COLUMNS,
                    level="detailed",
                )
                if detailed_report
                else None
            )

        return ReceivableReportDtoV2(
            detailed_report=detailed_report,
            detailed_report_download_url=detailed_report_download_url,
            summarised_report=summarised_report,
            summarised_report_download_url=summarised_report_download_url,
        )

    def get_summarised_receivable_report(
        self, debtors, month_wise_date_breakup, base_currency
    ):
        month_wise_summarised_receivable_reports = []
        for date_range in month_wise_date_breakup:
            (
                debtor_wise_credit_summary,
                debtor_wise_debit_summary,
                debtor_wise_oldest_debit_age,
            ) = self.get_summary_report_for_debtors(
                debtors,
                start_date=date_range.get("month_start_date"),
                end_date=date_range.get("month_end_date"),
            )
            if not debtor_wise_credit_summary:
                summarised_receivable_report_dtos = SummarisedReceivableReportDto()
                month_wise_summarised_receivable_reports.append(
                    MonthWiseSummaryReportDto(
                        start_date=date_range.get("month_start_date"),
                        end_date=date_range.get("month_end_date"),
                        summary=summarised_receivable_report_dtos,
                    )
                )
                continue
            debtor_ids = []
            total_unapplied_payments = gross_receivables = net_receivables = Money(
                0, base_currency
            )
            for debtor_id, credit_summary in debtor_wise_credit_summary.items():
                debit_summary = debtor_wise_debit_summary.get(debtor_id)
                if not debit_summary:
                    continue
                debtor_ids.append(debtor_id)
                total_unapplied_payments += credit_summary[2]
                gross_receivables += debit_summary[1]
                net_receivables += debit_summary[0]
            if not debtor_ids:
                summarised_receivable_report_dtos = SummarisedReceivableReportDto(
                    debtors_count=len(debtor_ids) if debtor_ids else 0,
                    total_unapplied_payments=total_unapplied_payments,
                    total_gross_receivables=gross_receivables,
                    total_net_receivables=gross_receivables - total_unapplied_payments,
                    days_of_sales_os=0,
                    age_of_receivables=[
                        {"age": "0-30", "receivables": 0},
                        {"age": "60-30", "receivables": 0},
                        {"age": "60-90", "receivables": 0},
                        {"age": "90-180", "receivables": 0},
                        {"age": "greater_than_365", "receivables": 0},
                    ],
                )
                month_wise_summarised_receivable_reports.append(
                    MonthWiseSummaryReportDto(
                        start_date=date_range.get("month_start_date"),
                        end_date=date_range.get("month_end_date"),
                        summary=summarised_receivable_report_dtos,
                    )
                )
                continue

            daily_of_sales_os = self.calculate_daily_of_sales_os(
                date=date_range.get("month_end_date"),
                debtor_ids=debtor_ids,
                base_currency=base_currency,
                total_net_receivables=net_receivables,
            )

            age_of_receivables = self.calculate_age_of_receivables(
                debtor_ids,
                date=date_range.get("month_end_date"),
                base_currency=base_currency,
            )
            summarised_receivable_report_dtos = SummarisedReceivableReportDto(
                debtors_count=len(debtor_ids) if debtor_ids else 0,
                total_unapplied_payments=total_unapplied_payments,
                total_gross_receivables=gross_receivables,
                total_net_receivables=gross_receivables - total_unapplied_payments,
                days_of_sales_os=daily_of_sales_os,
                age_of_receivables=age_of_receivables,
            )
            month_wise_summarised_receivable_reports.append(
                MonthWiseSummaryReportDto(
                    start_date=date_range.get("month_start_date"),
                    end_date=date_range.get("month_end_date"),
                    summary=summarised_receivable_report_dtos,
                )
            )

        return month_wise_summarised_receivable_reports

    def calculate_age_of_receivables(self, debtor_ids, date, base_currency):
        # todo : Figure out a better way, optimize
        age_of_receivables = []
        debtor_wise_debit_summary = self.debit_repo.get_debit_summary(
            debtor_ids, start_date=None, end_date=date
        )
        reference_date_gross_receivables = self._sum_gross_receivables(
            base_currency, debtor_wise_debit_summary
        )

        debtor_wise_debit_summary_0_30 = self.debit_repo.get_debit_summary(
            debtor_ids, start_date=dateutils.subtract(date, days=30), end_date=date
        )
        debtor_wise_debit_summary_0_30_gross_receivables = self._sum_gross_receivables(
            base_currency, debtor_wise_debit_summary_0_30
        )
        receivables = (
            debtor_wise_debit_summary_0_30_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(dict(age="0-30", receivables=receivables * 100))

        debtor_wise_debit_summary_30_60 = self.debit_repo.get_debit_summary(
            debtor_ids,
            start_date=dateutils.subtract(date, days=60),
            end_date=dateutils.subtract(date, days=30),
        )
        debtor_wise_debit_summary_30_60_gross_receivables = self._sum_gross_receivables(
            base_currency, debtor_wise_debit_summary_30_60
        )
        receivables = (
            debtor_wise_debit_summary_30_60_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(dict(age="30-60", receivables=receivables * 100))

        debtor_wise_debit_summary_60_90 = self.debit_repo.get_debit_summary(
            debtor_ids,
            start_date=dateutils.subtract(date, days=90),
            end_date=dateutils.subtract(date, days=60),
        )
        debtor_wise_debit_summary_60_90_gross_receivables = self._sum_gross_receivables(
            base_currency, debtor_wise_debit_summary_60_90
        )
        receivables = (
            debtor_wise_debit_summary_60_90_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(dict(age="60-90", receivables=receivables * 100))

        debtor_wise_debit_summary_90_180 = self.debit_repo.get_debit_summary(
            debtor_ids,
            start_date=dateutils.subtract(date, days=180),
            end_date=dateutils.subtract(date, days=90),
        )
        debtor_wise_debit_summary_90_180_gross_receivables = (
            self._sum_gross_receivables(base_currency, debtor_wise_debit_summary_90_180)
        )
        receivables = (
            debtor_wise_debit_summary_90_180_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(dict(age="90-180", receivables=receivables * 100))

        debtor_wise_debit_summary_180_365 = self.debit_repo.get_debit_summary(
            debtor_ids,
            start_date=dateutils.subtract(date, days=365),
            end_date=dateutils.subtract(date, days=180),
        )
        debtor_wise_debit_summary_180_365_gross_receivables = (
            self._sum_gross_receivables(
                base_currency, debtor_wise_debit_summary_180_365
            )
        )
        receivables = (
            debtor_wise_debit_summary_180_365_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(dict(age="180-365", receivables=receivables * 100))

        debtor_wise_debit_summary_greater_than_365 = self.debit_repo.get_debit_summary(
            debtor_ids, end_date=dateutils.subtract(date, days=365)
        )
        debtor_wise_debit_summary_greater_than_365days_gross_receivables = (
            self._sum_gross_receivables(
                base_currency, debtor_wise_debit_summary_greater_than_365
            )
        )
        receivables = (
            debtor_wise_debit_summary_greater_than_365days_gross_receivables.amount
            / reference_date_gross_receivables.amount
            if reference_date_gross_receivables
            else 0
        )
        age_of_receivables.append(
            dict(age="greater_than_365", receivables=receivables * 100)
        )

        return age_of_receivables

    def _sum_gross_receivables(self, base_currency, debtor_wise_debit_summary):
        total_gross_receivables = Money(0, base_currency)
        for debtor_id, amount in debtor_wise_debit_summary.items():
            total_gross_receivables += amount[1]
        return total_gross_receivables

    def calculate_daily_of_sales_os(
        self, date, debtor_ids, base_currency, total_net_receivables
    ):
        start_date = dateutils.subtract(date, days=90)
        debtor_wise_debit_summary = self.debit_repo.get_debit_summary(
            debtor_ids, start_date, date
        )
        if not debtor_wise_debit_summary:
            return 0
        total_debits = Money(0, base_currency)
        for debtor_id, amount in debtor_wise_debit_summary.items():
            total_debits += amount[0]
        if not total_debits:
            return total_debits.amount
        daily_credit_sales_amount = total_debits / 90
        return total_net_receivables.amount / daily_credit_sales_amount.amount

    def get_receivable_report(self, start_date, end_date, hotel_id):
        debtor_aggregates = self.debtor_service.search_debtors(hotel_id=hotel_id)
        debtors = [aggregate.debtor for aggregate in debtor_aggregates]
        month_wise_date_breakup = get_month_wise_date_range_breakup(
            start_date, end_date
        )
        month_wise_report = []
        base_currency = self.tenant_settings.get_chain_base_currency()
        for date_range in month_wise_date_breakup:
            detailed_reports = self._get_detailed_receivable_report(
                debtors,
                base_currency,
                start_date=date_range.get("month_start_date"),
                end_date=date_range.get("month_end_date"),
            )
            receivable_report_dto = ReceivableReportDto(detailed_reports)
            month_wise_report.append(
                MonthWiseReceivableReportDto(
                    start_date=date_range.get("month_start_date"),
                    end_date=date_range.get("month_end_date"),
                    receivable_report=receivable_report_dto,
                )
            )

        summarised_report_download_url = self.generate_summarsied_receivable_report(
            month_wise_report
        )
        detailed_report_download_url = self.generate_detailed_receivable_report(
            month_wise_report
        )

        detailed_report = [
            monthly_report.receivable_report.detailed_report
            for monthly_report in month_wise_report
            if monthly_report.receivable_report.detailed_report
        ]
        flatten_detailed_report = [
            item for sublist in detailed_report for item in sublist
        ]

        summaries = []
        for monthly_report in month_wise_report:
            summaries.append(
                MonthWiseSummaryReportDto(
                    start_date=monthly_report.start_date,
                    end_date=monthly_report.end_date,
                    summary=monthly_report.receivable_report.summarised_report,
                )
            )
        return ReceivableReportAndURLDto(
            month_wise_summary=summaries,
            detailed_report=flatten_detailed_report,
            summarised_report_download_url=summarised_report_download_url,
            detailed_report_download_url=detailed_report_download_url,
        )

    def _get_detailed_receivable_report(
        self,
        debtors,
        base_currency,
        start_date=None,
        end_date=None,
        filter_criteria=None,
    ):
        (
            debtor_wise_credit_summary,
            debtor_wise_debit_summary,
            debtor_wise_oldest_debit_age,
        ) = self.get_summary_report_for_debtors(debtors, start_date, end_date)
        debtor_ids = [debtor.debtor_id for debtor in debtors]
        debtors = self.debtor_repo.load_all(debtor_ids=debtor_ids)
        detailed_receivable_report_dtos = []
        for debtor in debtors:
            debit_summary = debtor_wise_debit_summary.get(debtor.debtor_id)
            credit_summary = debtor_wise_credit_summary.get(debtor.debtor_id)
            if not debit_summary and not credit_summary:
                continue
            oldest_due = debtor_wise_oldest_debit_age.get(debtor.debtor_id, 0)
            if filter_criteria.max_oldest_due_age and not (
                filter_criteria.min_oldest_due_age
                <= oldest_due
                <= filter_criteria.max_oldest_due_age
            ):
                continue
            total_credits, total_provisional_credits, unapplied_payment = (
                credit_summary
                if credit_summary
                else (
                    Money(0, base_currency),
                    Money(0, base_currency),
                    Money(0, base_currency),
                )
            )
            net_receivables, gross_receivables = (
                debit_summary
                if debit_summary
                else (Money(0, base_currency), Money(0, base_currency))
            )
            net_receivables = gross_receivables - unapplied_payment
            if filter_criteria.max_balance and not (
                filter_criteria.min_balance
                <= net_receivables
                <= filter_criteria.max_balance
            ):
                continue
            detailed_receivable_report_dtos.append(
                DetailedReceivableReportDto(
                    debtor.debtor_code,
                    debtor.debtor_name,
                    debtor.debtor_id,
                    total_credits,
                    total_provisional_credits,
                    unapplied_payment,
                    gross_receivables,
                    net_receivables,
                    oldest_due,
                    debtor_type=debtor.debtor_type,
                )
            )
        return detailed_receivable_report_dtos

    def get_summary_report_for_debtors(self, debtors, start_date, end_date):
        debtor_ids = [debtor.debtor_id for debtor in debtors]
        debtor_wise_credit_summary = self.credit_repo.get_credit_summary(
            debtor_ids, start_date, end_date
        )
        debtor_wise_debit_summary = self.debit_repo.get_debit_summary(
            debtor_ids, start_date, end_date
        )
        debtor_wise_oldest_debit_age = (
            self.debit_repo.get_age_of_oldest_unsettled_debit(debtor_ids)
        )
        return (
            debtor_wise_credit_summary,
            debtor_wise_debit_summary,
            debtor_wise_oldest_debit_age,
        )

    @staticmethod
    def generate_receivable_report_file_name(
        extension="csv", identifier=None, level=None
    ):
        if identifier is None:
            identifier = str(uuid.uuid4())

        file_name = f"{level}-collection-report-{identifier}.{extension}"
        return (
            os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/reports/") + file_name
        )

    @staticmethod
    def get_default_expiration_time():
        return 604800

    def generate_report_download_url(self, report_data, column_values, level):
        file_path = self.generate_receivable_report_file_name(level=level)

        with CsvWriter(file_path) as csv_writer:
            csv_writer.write_aggregates(report_data, column_values)
            presigned_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
                RECEIVABLE_REPORT_FOLDER_NAME,
                csv_writer.file_path,
                self.get_default_expiration_time(),
            )

        return presigned_url
