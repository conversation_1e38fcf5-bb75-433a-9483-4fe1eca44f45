import logging
from typing import List

from marshmallow import ValidationError
from treebo_commons.money import Money

from ar_module.domain.constants import CreditType, SettlementStatus
from ar_module.domain.entities.debit import Debit
from ar_module.domain.entities.settlement import Settlement

logger = logging.getLogger(__name__)


class DebitAggregate(object):
    def __init__(self, debit: Debit, settlements: List[Settlement]):
        self.debit = debit
        self.settlements = settlements

    @property
    def reference_id(self):
        return self.debit.reference_id

    def max_settlement_id(self):
        return max(
            (settlement.settlement_id for settlement in self.settlements), default=0
        )

    def has_settlement(self):
        return any(
            settlement for settlement in self.settlements if not settlement.deleted
        )

    def create_new_settlement(self, credit_aggregate, amount: Money, remarks=None):
        if self.debit.settlement_status == SettlementStatus.CANCELLED:
            raise ValidationError("Invoice against this debit is cancelled")

        new_settlement = Settlement(
            self.max_settlement_id() + 1,
            credit_aggregate.credit.credit_type,
            credit_aggregate.credit.reference_number,
            amount,
            credit_aggregate.credit.credit_id,
            credit_aggregate.credit.date,
            remarks=remarks,
        )
        self.debit.knock_off_settlement_amount(amount)
        self.settlements.append(new_settlement)
        self._update_settlement_status()
        return new_settlement

    def cancel(self):
        if self.has_settlement():
            return
        self.debit.settlement_status = SettlementStatus.CANCELLED

    def _update_settlement_status(self):
        if not self.debit.unsettled_amount:
            if self.has_tds_settlement():
                self.debit.settlement_status = SettlementStatus.PROVISIONALLY_SETTLED
            else:
                self.debit.settlement_status = SettlementStatus.SETTLED
        else:
            if self.debit.unsettled_amount == self.debit.debit_amount.posttax_amount:
                self.debit.settlement_status = SettlementStatus.UNSETTLED
            else:
                self.debit.settlement_status = SettlementStatus.PARTIALLY_SETTLED

    def has_tds_settlement(self):
        for settlement in self.settlements:
            if settlement.settled_via == CreditType.TDS and settlement.amount:
                return True
        return False

    def reverse_payment_to_unmap_settlement(self, credit_id, credit_amount):
        settlement_to_reverse = [
            st
            for st in self.settlements
            if st.credit_id == credit_id and not st.deleted
        ]
        if not settlement_to_reverse:
            raise ValidationError("Unable to find an settlement to unmap")
        settlement_to_reverse = settlement_to_reverse[0]
        if settlement_to_reverse.amount != credit_amount:
            raise ValidationError(
                "Credit amount should match, partial unmapping is not allowed"
            )
        settlement_to_reverse.delete()
        self.debit.reverse_settlement_amount(settlement_to_reverse.amount)
        self._update_settlement_status()
        return settlement_to_reverse

    def reverse_settlement_associated_with_given_payment(self, credit_id):
        settlement_to_reverse = [
            st
            for st in self.settlements
            if st.credit_id == credit_id and not st.deleted
        ]
        if not settlement_to_reverse:
            return
        settlement_to_reverse = settlement_to_reverse[0]
        settlement_to_reverse.delete()
        self.debit.reverse_settlement_amount(settlement_to_reverse.amount)
        self._update_settlement_status()
        return settlement_to_reverse

    def reverse_payment_settlement_to_accommodate_credit_note_settlement(
        self, credit_note_settlement_amount: Money = None, all_settlements=False
    ):
        """
        Reverses payment settlements applied in reverse order, until debit's unsettled_amount is more than
        credit_note_settlement_amount.

        If for reversed payment settlements, there is a related TDS settlement that was applied, that also gets reversed

        :param credit_note_settlement_amount:
        :param all_settlements:
        :return:
        """
        reversed_settlements = []
        payment_settlements = [
            s for s in self.settlements if s.settled_via == CreditType.PAYMENT
        ]

        for settlement in sorted(payment_settlements, key=lambda s: -s.settlement_id):
            settlement.delete()
            reversed_settlements.append(settlement)
            self.debit.reverse_settlement_amount(settlement.amount)
            if all_settlements:
                self.cancel()
            else:
                self._update_settlement_status()

            related_tds_settlement = self._fetch_tds_settlement_for_reference_number(
                settlement.reference_number
            )
            if related_tds_settlement:
                related_tds_settlement.delete()
                reversed_settlements.append(related_tds_settlement)
                self.debit.reverse_settlement_amount(related_tds_settlement.amount)

            if all_settlements:
                continue

            if credit_note_settlement_amount and self.debit.can_settle_amount(
                credit_note_settlement_amount
            ):
                return reversed_settlements

        return reversed_settlements

    def _fetch_tds_settlement_for_reference_number(self, settlement_reference_number):
        return next(
            (
                s
                for s in self.settlements
                if s.settled_via == CreditType.TDS
                and s.reference_number == settlement_reference_number
            ),
            None,
        )

    @property
    def debtor_id(self):
        return self.debit.debtor_id

    @property
    def debit_id(self):
        return self.debit.debit_id

    @property
    def tenant_id(self):
        return self.debit.tenant_id

    def update_debit_template_url(self, url):
        self.debit.debit_template_url = url

    def remove_settlement(self, settlement_id):
        for settlement in self.settlements:
            if settlement.settlement_id == settlement_id:
                self.settlements.remove(settlement)
                break
