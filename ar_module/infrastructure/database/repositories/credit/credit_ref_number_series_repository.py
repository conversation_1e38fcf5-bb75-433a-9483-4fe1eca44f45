from ar_module.domain.aggregates.debtor_aggregate import DebtorAggregate
from ar_module.domain.constants import CreditType
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.credit_model import (
    CreditReferenceNumberSeriesModel,
)
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from object_registry import register_instance


@register_instance()
class CreditRefNumberSeriesRepository(BaseRepository):
    def from_aggregate(self, aggregate=None):
        pass

    def to_aggregate(self, **kwargs):
        pass

    def get_next_credit_reference_number(
        self,
        debtor_aggregate: DebtorAggregate,
        credit_date,
        credit_type=CreditType.PAYMENT,
    ):
        debtor_id = debtor_aggregate.debtor_id
        sequence = self.get_for_update(
            CreditReferenceNumberSeriesModel,
            debtor_id=debtor_id,
            credit_type=credit_type,
            credit_date=credit_date,
        )
        if not sequence:
            locked_debtor = self.get_for_update(
                DebtorModel, debtor_id=debtor_id, nowait=False
            )
            sequence = self.get_for_update(
                CreditReferenceNumberSeriesModel,
                debtor_id=debtor_id,
                credit_type=credit_type,
                credit_date=credit_date,
                nowait=False,
            )
            if not sequence:
                sequence_number = 1
                new_sequence = CreditReferenceNumberSeriesModel(
                    debtor_id=debtor_id,
                    credit_type=credit_type,
                    credit_date=credit_date,
                    last_sequence_number=sequence_number,
                )
                self._save(new_sequence)
            else:
                sequence_number = sequence.last_sequence_number + 1
                sequence.last_sequence_number = sequence_number
                self._update(sequence)
        else:
            sequence_number = sequence.last_sequence_number + 1
            sequence.last_sequence_number = sequence_number
            self._update(sequence)

        credit_reference_number = self._generate_credit_reference_number(
            debtor_aggregate, credit_type, credit_date, sequence_number
        )

        self.flush_session()
        return credit_reference_number

    def _generate_credit_reference_number(
        self, debtor_aggregate, credit_type, credit_date, sequence_number
    ):
        return "{debtor_code}{prefix}{credit_date_mmddyy}{credit_serial_number}".format(
            debtor_code=debtor_aggregate.debtor_code,
            prefix=self._get_prefix(credit_type),
            credit_date_mmddyy=credit_date.strftime("%d%m%y"),
            credit_serial_number=str(sequence_number).zfill(3),
        )

    @staticmethod
    def _get_prefix(credit_type):
        return credit_type[0].upper()
