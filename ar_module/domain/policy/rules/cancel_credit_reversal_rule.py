from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.cancel_credit_reversal_facts import (
    CancelCreditReversalFacts,
)
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CancelCreditReversalRule(BaseRule):
    def allow(self, facts: CancelCreditReversalFacts, privileges=None):
        if PrivilegeCode.CANCEL_CREDIT_REVERSAL not in privileges:
            raise PolicyAuthException(
                error=PolicyError.CANCEL_CREDIT_REVERSAL_NOT_ALLOWED
            )
        return True
