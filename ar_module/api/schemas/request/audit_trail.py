from marshmallow import Schema, fields

from ar_module.domain.constants import SortOrders


class AuditTrailSearchSchema(Schema):
    hotel_id = fields.String(
        required=False,
        description="Hotel ID for which audit trail needs to be fetched..",
    )
    from_date = fields.DateTime(required=False)
    to_date = fields.DateTime(required=False)
    limit = fields.Integer(description="Number of results required", allow_none=True)
    offset = fields.Integer(
        description="Offset from which results are required", allow_none=True
    )
    audit_types = fields.String(required=False, description="Audit types to be fetched")
    sort_order = (
        fields.String(
            validate=SortOrders.all(),
            error="Sort Order is not valid. Allowed values are 'asc' and 'desc'",
            required=False,
        ),
    )
