from typing import Dict

from sqlalchemy import Column, DateTime, Integer, String
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base


class JobModel(Base):
    __tablename__ = "job"

    job_name = Column(String)
    data = Column(String)
    hotel_id = Column(String)
    eta = Column(DateTime(timezone=True))
    generated_at = Column(DateTime(timezone=True))
    job_id = Column(String, primary_key=True)
    status = Column(String)
    picked_at = Column(DateTime(timezone=True))
    failure_message = Column(String)
    total_tries = Column(Integer)

    def mapping_dict(self) -> Dict:
        return {
            "job_name": self.job_name,
            "data": self.data,
            "hotel_id": self.hotel_id,
            "eta": self.eta,
            "generated_at": self.generated_at,
            "job_id": self.job_id,
            "status": self.status,
            "picked_at": self.picked_at,
            "failure_message": self.failure_message,
            "total_tries": self.total_tries,
        }
