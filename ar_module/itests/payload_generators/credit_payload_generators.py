def create_new_credit_payload(
    amount_in_base_currency="INR 500",
    amount_in_credit_currency="INR 500",
    date=None,
    settlements=None,
):
    payload = {
        "amount_in_base_currency": amount_in_base_currency,
        "amount_in_credit_currency": amount_in_credit_currency,
        "credit_type": "payment",
        "date": str(date),
        "debtor_id": "string",
        "mode_of_credit": "bank_transfer",
        "reference_number": "testP290921123",
    }
    if settlements:
        payload["settlements"] = settlements
    return payload
