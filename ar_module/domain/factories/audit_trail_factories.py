import uuid

from treebo_commons.utils import dateutils

from ar_module.domain.aggregates.audit_trail_aggregates import AuditTrailAggregate
from ar_module.domain.entities.audit_trail import AuditTrail


class AuditTrailFactory(object):
    @staticmethod
    def create_audit_trail(
        user, user_type, hotel_id, audit_type, audit_payload, request_id=None
    ):
        audit_id = uuid.uuid4().hex

        audit_trail = AuditTrail(
            audit_id,
            user,
            user_type,
            hotel_id,
            audit_type,
            audit_payload,
            dateutils.current_datetime(),
            request_id=request_id,
        )
        return AuditTrailAggregate(audit_trail=audit_trail)
