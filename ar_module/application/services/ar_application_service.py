import logging
from typing import List

from thsc.crs.entities.booking import Booking
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from ar_module.application.consumers.constants import (
    DebtorTypes,
    InvoiceBillToType,
    InvoiceChargeType,
)
from ar_module.application.dtos.credit_dto import CreditDto
from ar_module.application.dtos.credit_note_dto import CreditNoteDto
from ar_module.application.dtos.debtor_dto import DebtorDTO
from ar_module.application.dtos.invoice_dto import InvoiceDto, InvoiceUpdateData
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.user_service import UserService
from ar_module.common.tenant_utils import get_tenant_id
from ar_module.domain.aggregates.debit_aggregate import DebitAggregate
from ar_module.domain.constants import (
    DEFAULT_CREDIT_SETTINGS,
    ARModuleConfigs,
    ARPaymentMode,
    AuditType,
    CreditType,
    InvoiceStatusToConsume,
)
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.credit_factory import CreditFactory
from ar_module.domain.factories.debit_factory import DebitFactory
from ar_module.domain.factories.debtor_factory import DebtorFactory
from ar_module.domain.value_objects.amount import Amount
from ar_module.domain.value_objects.invoice_meta import InvoiceMeta
from ar_module.exceptions import CorporateDetailsNotFound, InvalidSettlementException
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from ar_module.infrastructure.external_clients.cashier_client import CashierClient
from ar_module.infrastructure.external_clients.crs_client import CrsClient
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditRepository,
        DebitRepository,
        DebtorRepository,
        UserService,
        DebtorService,
        AuditTrailService,
        CashierClient,
        TenantSettings,
    ]
)
class AccountReceivableApplicationService(object):
    def __init__(
        self,
        credit_repo: CreditRepository,
        debit_repo: DebitRepository,
        debtor_repo: DebtorRepository,
        user_service: UserService,
        debtor_service: DebtorService,
        audit_trail_service: AuditTrailService,
        cashier_client: CashierClient,
        tenant_settings: TenantSettings,
    ):
        self.credit_repo = credit_repo
        self.debit_repo = debit_repo
        self.debtor_repo = debtor_repo
        self.cashier_client = cashier_client
        self.audit_trail_service = audit_trail_service
        self.user_service = user_service
        self.debtor_service = debtor_service
        self.tenant_settings = tenant_settings

    @unit_of_work
    def process_crs_invoices_event(self, invoices):
        tenant_id = get_tenant_id()
        cancelled_invoices = [
            invoice
            for invoice in invoices
            if invoice.get("status") == InvoiceStatusToConsume.CANCELLED
        ]
        # TODO: Remove API call in loop to CRS for each event.
        uncancelled_btc_invoices = [
            invoice
            for invoice in invoices
            if invoice.get("status") != InvoiceStatusToConsume.CANCELLED
            and self._is_credit_invoice(invoice)
        ]

        existing_debit_aggregates = self.debit_repo.load_for_invoices(
            invoice_ids=[
                invoice.get("invoice_id")
                for invoice in cancelled_invoices + uncancelled_btc_invoices
            ]
        )
        debit_grouped_by_invoice_id_debtor_code = {
            (aggregate.debit.reference_id, aggregate.debit.debtor_id): aggregate
            for aggregate in existing_debit_aggregates
        }

        if cancelled_invoices:
            self._handle_cancelled_invoices(
                {inv.get("invoice_id") for inv in cancelled_invoices},
                existing_debit_aggregates,
            )

        invoices = uncancelled_btc_invoices
        invoice_ids = [invoice.get("invoice_id") for invoice in invoices]

        if not invoices:
            return

        logger.info(
            "Processing %s Credit Invoices with invoice_ids : %s",
            len(invoices),
            " ,".join(invoice_ids),
        )

        hotel_level_accounts_receivable = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        for invoice in invoices:
            invoice_date = dateutils.ymd_str_to_date(invoice.get("invoice_date"))
            invoices_to_create = []
            # Ignore POS invoices
            if "booking_id" not in invoice.get("parent_info"):
                continue
            booking_id = invoice.get("parent_info").get("booking_id")
            booking = Booking.get(booking_id)
            user_info_map = invoice.get("user_info_map")
            vendor_details = invoice.get("vendor_details")
            if vendor_details.get("is_test"):
                logger.info("Received invoice of test hotel, hence skipping")
                return
            auto_settle_feature_enabled = self.tenant_settings.get_setting_value(
                ARModuleConfigs.AUTO_SETTLED_FEATURED_ENABLED
            )
            debtor_ids = set()
            vendor_id = invoice.get("vendor_details").get("vendor_id")
            hotel_id = vendor_id if hotel_level_accounts_receivable else None

            auto_settled_debit_debtor = None
            debtor = self._get_billed_debtor(
                booking,
                invoice,
                hotel_id,
            )
            debtor_of_booking_owner = None
            if auto_settle_feature_enabled:
                debtor_of_booking_owner = self._get_debtor_of_booking_owner(
                    booking, hotel_id
                )
            invoice_debtor_key = (invoice.get("invoice_id"), debtor.debtor_id)
            if invoice_debtor_key in debit_grouped_by_invoice_id_debtor_code.keys():
                self._process_existing_invoices(
                    debit_grouped_by_invoice_id_debtor_code,
                    debtor,
                    debtor_of_booking_owner,
                    invoice,
                    invoice_debtor_key,
                )
            else:
                debtor_ids.add(debtor.debtor_id)
                # If booking booker and bill entity are different,
                # then create debit for booker and bill and auto-settle debit associated with bill entity
                if (
                    debtor_of_booking_owner
                    and debtor_of_booking_owner.debtor_code != debtor.debtor_code
                ):
                    debtor_ids.add(debtor_of_booking_owner.debtor_id)
                    auto_settled_debit_debtor = debtor

            self._process_new_invoices(
                auto_settled_debit_debtor,
                debtor_of_booking_owner,
                booking,
                debit_grouped_by_invoice_id_debtor_code,
                debtor,
                debtor_ids,
                invoice,
                invoice_date,
                invoices_to_create,
                user_info_map,
                vendor_details,
            )

    def _process_new_invoices(
        self,
        auto_settled_debit_debtor,
        debtor_of_booking_owner,
        booking,
        debit_grouped_by_invoice_id_debtor_code,
        debtor,
        debtor_ids,
        invoice,
        invoice_date,
        invoices_to_create,
        user_info_map,
        vendor_details,
    ):
        for debtor_id in debtor_ids:
            invoice_debtor_key = (invoice.get("invoice_id"), debtor_id)
            if invoice_debtor_key in debit_grouped_by_invoice_id_debtor_code.keys():
                continue

            bill_to_debtor_code = debtor.debtor_code
            if auto_settled_debit_debtor:
                bill_to_debtor_code = auto_settled_debit_debtor.debtor_code
            remarks = (
                f"Auto Transferred to another debtor. Debtor Code: {debtor_of_booking_owner.debtor_code}"
                if auto_settled_debit_debtor
                and debtor_of_booking_owner
                and auto_settled_debit_debtor.debtor_id == debtor_id
                else None
            )
            credit_period = (
                int(debtor.credit_period)
                if debtor.credit_period
                else DEFAULT_CREDIT_SETTINGS["credit_period"]
            )
            invoices_to_create.append(
                self._create_invoice_dto(
                    auto_settled_debit_debtor,
                    bill_to_debtor_code,
                    booking,
                    credit_period,
                    debtor_id,
                    invoice,
                    invoice_date,
                    remarks,
                    user_info_map,
                    vendor_details,
                )
            )
        if invoices_to_create:
            self._handle_invoice_creation(
                auto_settled_debit_debtor,
                debtor_of_booking_owner,
                debit_grouped_by_invoice_id_debtor_code,
                debtor,
                invoice,
                invoices_to_create,
            )

    def _process_existing_invoices(
        self,
        debit_grouped_by_invoice_id_debtor_code,
        debtor,
        debtor_of_booking_owner,
        invoice,
        invoice_debtor_key,
    ):
        logger.info(
            "Found Existing invoice, hence checking for updatable fields: %s",
            invoice.get("invoice_id"),
        )
        exiting_debit_aggregate = debit_grouped_by_invoice_id_debtor_code.get(
            invoice_debtor_key
        )
        invoices_to_update = [
            InvoiceUpdateData(
                debit_aggregate=exiting_debit_aggregate,
                invoice_url=invoice.get("signed_url"),
            )
        ]
        if (
            debtor_of_booking_owner
            and debtor_of_booking_owner.debtor_code != debtor.debtor_code
            and (invoice.get("invoice_id"), debtor_of_booking_owner.debtor_id)
            in debit_grouped_by_invoice_id_debtor_code.keys()
        ):
            exiting_debit_aggregate_of_booking_owner = (
                debit_grouped_by_invoice_id_debtor_code.get(
                    (invoice.get("invoice_id"), debtor_of_booking_owner.debtor_id)
                )
            )
            invoices_to_update.append(
                InvoiceUpdateData(
                    debit_aggregate=exiting_debit_aggregate_of_booking_owner,
                    invoice_url=invoice.get("signed_url"),
                )
            )
        self._handle_invoice_update(invoices_to_update)

    def _handle_invoice_creation(
        self,
        auto_settled_debit_debtor,
        debtor_of_booking_owner,
        debit_grouped_by_invoice_id_debtor_code,
        debtor,
        invoice,
        invoices_to_create,
    ):
        debit_aggregates = [
            DebitFactory.create_from_invoice(invoice_dto)
            for invoice_dto in invoices_to_create
            if (invoice_dto.invoice_number, invoice_dto.debtor_id)
            not in debit_grouped_by_invoice_id_debtor_code.keys()
        ]
        if auto_settled_debit_debtor:
            debit_aggregate = next(
                debit_aggregate
                for debit_aggregate in debit_aggregates
                if debit_aggregate.debtor_id == auto_settled_debit_debtor.debtor_id
                and debit_aggregate.reference_id == invoice.get("invoice_id")
            )
            credit_aggregate = self._auto_settle_debit_via_credit(
                debit_aggregate, auto_settled_debit_debtor
            )
            if credit_aggregate:
                logger.info("Saving Credit")
                self.credit_repo.save(credit_aggregate)
            self.audit_trail_service.create_debit_audit_trail(
                debit_aggregate,
                auto_settled_debit_debtor,
                debtor.hotel_id,
                AuditType.DEBIT_AUTO_SETTLED,
                debtor_aggregate_booker_le=debtor_of_booking_owner,
                booking_id=invoice.get("parent_info").get("booking_id"),
                user_data=None,
            )
        logger.info("Saving %s Debits", len(debit_aggregates))
        self.debit_repo.save_all(debit_aggregates)

    def _create_invoice_dto(
        self,
        auto_settled_debit_debtor,
        bill_to_debtor_code,
        booking,
        credit_period,
        debtor_id,
        invoice,
        invoice_date,
        remarks,
        user_info_map,
        vendor_details,
    ):
        return InvoiceDto(
            invoice_id=invoice.get("invoice_id"),
            debtor_id=debtor_id,
            invoice_date=invoice_date,
            invoice_amount=Amount(
                pretax_amount=Money(invoice.get("pretax_amount"), CurrencyType.INR),
                posttax_amount=Money(invoice.get("posttax_amount"), CurrencyType.INR),
                tax_amount=Money(invoice.get("tax_amount"), CurrencyType.INR),
            ),
            invoice_number=invoice.get("invoice_number"),
            vendor_id=invoice.get("vendor_details").get("vendor_id"),
            invoice_url=invoice.get("signed_url"),
            status=invoice.get("status"),
            booking_reference_number=invoice.get("parent_info", {}).get(
                "reference_number"
            ),
            bill_to_debtor_code=bill_to_debtor_code,
            due_date=dateutils.add(invoice_date, days=credit_period),
            remarks=remarks,
            is_spot_credit=invoice.get("is_spot_credit"),
            auto_settled_via_credit=True
            if auto_settled_debit_debtor
            and auto_settled_debit_debtor.debtor_id == debtor_id
            else False,
            invoice_meta=self._get_invoice_meta_details(
                booking, user_info_map, vendor_details
            ),
            tenant_id=get_tenant_id(),
        )

    def _handle_invoice_update(self, update_data_list: List[InvoiceUpdateData]):
        aggregates_to_update = []
        for update_data in update_data_list:
            debit_aggregate = update_data.debit_aggregate
            if (
                update_data.invoice_url
                and debit_aggregate.debit.debit_template_url != update_data.invoice_url
            ):
                debit_aggregate.update_debit_template_url(update_data.invoice_url)
                aggregates_to_update.append(debit_aggregate)
        if aggregates_to_update:
            self.debit_repo.update_all(aggregates_to_update)

    def _handle_cancelled_invoices(
        self, cancelled_invoice_ids, existing_debit_aggregates
    ):
        debit_against_cancelled_invoices = [
            debit_aggregate
            for debit_aggregate in existing_debit_aggregates
            if debit_aggregate.reference_id in cancelled_invoice_ids
        ]
        cancelled_debits_without_settlement = [
            debit_aggregate
            for debit_aggregate in debit_against_cancelled_invoices
            if not debit_aggregate.has_settlement()
        ]
        if cancelled_debits_without_settlement:
            logger.info(
                "Cancelling Unsettled Debit against invoice_id: %s",
                [
                    debit_aggregate.reference_id
                    for debit_aggregate in cancelled_debits_without_settlement
                ],
            )
        for debit_aggregate in cancelled_debits_without_settlement:
            debit_aggregate.cancel()
        self.debit_repo.update_all(cancelled_debits_without_settlement)
        self._cancel_settled_debits(debit_against_cancelled_invoices)

    def _cancel_settled_debits(self, debit_aggregates):
        cancelled_debits_with_settlement = [
            debit_aggregate
            for debit_aggregate in debit_aggregates
            if debit_aggregate.has_settlement()
        ]

        if cancelled_debits_with_settlement:
            logger.info(
                "Cancelling partially or fully settled Debit against invoice_id: %s",
                [
                    debit_aggregate.reference_id
                    for debit_aggregate in cancelled_debits_with_settlement
                ],
            )

        credit_aggregates_with_reversed_settlements = []
        for debit_aggregate in cancelled_debits_with_settlement:
            updated_credit_aggregates = (
                self._reverse_all_settlements_to_accommodate_credit_note(
                    debit_aggregate
                )
            )
            if updated_credit_aggregates:
                credit_aggregates_with_reversed_settlements.extend(
                    updated_credit_aggregates
                )

        if cancelled_debits_with_settlement:
            self.debit_repo.update_all(cancelled_debits_with_settlement)

        if credit_aggregates_with_reversed_settlements:
            logger.info(
                "Credit with reversed settlements, due to cancelled invoices: %s",
                [agg.credit_id for agg in credit_aggregates_with_reversed_settlements],
            )
            self.credit_repo.update_all(credit_aggregates_with_reversed_settlements)

    @staticmethod
    def _is_credit_invoice(invoice):
        charge_types = invoice.get("allowed_charge_types")
        return InvoiceChargeType.CREDIT in charge_types

    @staticmethod
    def is_btb_credit_invoice(invoice):
        charge_types = invoice.get("allowed_charge_types")
        bill_to_type = invoice.get("bill_to_type")
        return (
            InvoiceChargeType.CREDIT in charge_types
            and bill_to_type == InvoiceBillToType.COMPANY
        )

    def _get_billed_debtor(self, booking, invoice, hotel_id):
        billed_entity_id = int(
            invoice.get("billed_entity_account").get("billed_entity_id")
        )
        if not self.is_btb_credit_invoice(invoice):
            bill_to_customer_id = invoice.get("bill_to").get("customer_id")
            debtor = self._get_or_create_debtor_from_non_btb_invoice(
                booking, bill_to_customer_id, billed_entity_id, hotel_id
            )
            return debtor
        bill_to_sh_code = invoice.get("bill_to").get("external_ref_id")
        if bill_to_sh_code:
            debtor_dto = DebtorDTO(
                debtor_name=invoice.get("bill_to").get("name"),
                reference_id=bill_to_sh_code,
                hotel_id=hotel_id,
            )
        else:
            debtor_dto = CrsClient.get_debtor_detail_for_billed_entity_id(
                booking, billed_entity_id, hotel_id=hotel_id
            )
        return self._get_debtor_info_for_debtor_code(
            booking, debtor_dto.debtor_code, hotel_id
        )

    def _get_debtor_of_booking_owner(self, booking, hotel_id):
        booker_billed_entity_id = self._get_booker_billed_entity_id(booking)
        if not booker_billed_entity_id:
            return None
        debtor_dto = CrsClient.get_debtor_detail_for_billed_entity_id(
            booking, int(booker_billed_entity_id), hotel_id=hotel_id
        )
        return self._get_debtor_info_for_debtor_code(
            booking, debtor_dto.debtor_code, hotel_id
        )

    @staticmethod
    def _get_booker_billed_entity_id(booking):
        booking_owner_id = booking.booking_owner_id
        for customer in booking.customers:
            if customer.customer_id == booking_owner_id:
                return customer.company_billed_entity_id or customer.billed_entity_id
        return None

    def _get_or_create_debtor_from_non_btb_invoice(
        self, booking, bill_to_customer_id, billed_entity_id, hotel_id
    ):
        if bill_to_customer_id:
            debtor_dto = CrsClient.get_debtor_detail(
                booking.booking_id, bill_to_customer_id, hotel_id=hotel_id
            )
        else:
            debtor_dto = CrsClient.get_debtor_detail_for_billed_entity_id(
                booking, billed_entity_id, hotel_id=hotel_id
            )
        debtor_dto.set_debtor_code(DebtorTypes.B2C, booking.reference_number)
        debtor_aggregates = self.debtor_repo.search_debtors(
            DebtorSearchQuery(
                debtor_code=debtor_dto.debtor_code, hotel_id=debtor_dto.hotel_id
            )
        )
        if debtor_aggregates:
            debtor = debtor_aggregates[0].debtor
        else:
            credit_settings = (
                self.tenant_settings.get_setting_value(ARModuleConfigs.CREDIT_SETTINGS)
                or {}
            )
            debtor_dto.credit_limit = (
                credit_settings.get("credit_limit")
                or DEFAULT_CREDIT_SETTINGS["credit_limit"]
            )
            debtor_dto.credit_period = (
                credit_settings.get("credit_period")
                or DEFAULT_CREDIT_SETTINGS["credit_period"]
            )
            debtor_aggregate = DebtorFactory.create_new_debtor_from_debtor_dto(
                debtor_dto
            )
            logger.info(
                "Create new debtor with debtor_code: %s and debtor_name: %s",
                debtor_aggregate.debtor.debtor_code,
                debtor_aggregate.debtor.debtor_name,
            )
            self.debtor_repo.save(debtor_aggregate)
            debtor = debtor_aggregate.debtor

        return debtor

    def _get_debtor_info_for_debtor_code(self, booking, debtor_code, hotel_id):
        debtor_aggregates = self.debtor_repo.search_debtors(
            DebtorSearchQuery(debtor_code=debtor_code, hotel_id=hotel_id)
        )
        if debtor_aggregates and debtor_aggregates[0].debtor_code == debtor_code:
            return debtor_aggregates[0].debtor
        else:
            logger.exception(
                f"Not able to find debtor with code: {debtor_code} for booking: {booking.booking_id}"
            )
            raise CorporateDetailsNotFound(booking.booking_id)

    @staticmethod
    def _auto_settle_debit_via_credit(debit_aggregate, debtor):
        credit_dto = CreditDto(
            debtor_id=debtor.debtor_id,
            credit_type=CreditType.PAYMENT,
            date=dateutils.current_date(),
            amount_in_base_currency=debit_aggregate.debit.debit_amount.posttax_amount,
            reference_number=debit_aggregate.debit.reference_number,
            reference_id=debit_aggregate.debit.reference_id,
            mode_of_credit=ARPaymentMode.AUTO_TRANSFER,
            amount_in_credit_currency=debit_aggregate.debit.debit_amount.posttax_amount,
            settlements=[],
            used_to_auto_settle_debit=True,
            tenant_id=debit_aggregate.tenant_id,
        )
        credit_aggregate = CreditFactory.create_new_credit(credit_dto=credit_dto)
        settlement = debit_aggregate.create_new_settlement(
            credit_aggregate,
            debit_aggregate.debit.debit_amount.posttax_amount,
            debit_aggregate.debit.remarks,
        )
        credit_aggregate.knock_off_settlement(settlement)
        return credit_aggregate

    @unit_of_work
    def process_credit_notes_event(
        self, credit_notes, should_raise_for_invoice_missing=False
    ):
        """
        - For each credit note, we need to create 1 Credit entity.
        - Each credit note, technically can have line items against multiple invoices. So we need to fetch all
        DebitAggregates for those invoices, and create appropriate Settlements in them

        :param credit_notes:
        :param should_raise_for_invoice_missing:
        :return:
        """
        credit_aggregates = []
        debit_aggregates, invoices = self._load_existing_debits_and_invoices(
            credit_notes
        )

        for credit_note in credit_notes:
            logger.info(f"Processing Credit Note: {credit_note.get('credit_note_id')}")
            vendor_details = credit_note.get("vendor_details")
            if vendor_details.get("is_test"):
                logger.info("Received invoice of test hotel, hence skipping")
                return
            if self.credit_repo.exists(
                credit_type=CreditType.CREDIT_NOTE,
                reference_id=credit_note.get("credit_note_id"),
            ):
                logger.info(
                    f"Found, hence Skipping Credit Note: {credit_note.get('credit_note_id')}"
                )
                continue

            invoice_ids = {
                line_item.get("invoice_id")
                for line_item in credit_note.get("credit_note_line_items")
            }
            current_debit_aggregates = [
                aggregate
                for aggregate in debit_aggregates
                if aggregate.debit.reference_id in invoice_ids
            ]
            debit_reference_ids = [
                aggregate.debit.reference_id
                for aggregate in debit_aggregates
                if aggregate.debit.reference_id in invoice_ids
            ]

            if len(set(debit_reference_ids)) != len(invoice_ids) or not invoice_ids:
                # Debit entry is not yet created for some invoices in this Credit Note. Skip it, because we won't be
                # able to map invoice to `CreditAggregate` created against this Credit Note later.
                if should_raise_for_invoice_missing:
                    raise Exception(
                        "Invoice id missing in Cn"
                        if not invoice_ids
                        else "Invoice not yet ingested"
                    )
                continue

            non_auto_settled_debit_aggregates = [
                aggregate
                for aggregate in current_debit_aggregates
                if not aggregate.debit.auto_settled_via_credit
            ]
            auto_settled_debit_aggregates = [
                aggregate
                for aggregate in current_debit_aggregates
                if aggregate.debit.auto_settled_via_credit
            ]

            if non_auto_settled_debit_aggregates:
                credit_aggregates.append(
                    self._create_settlements_for_debits(
                        non_auto_settled_debit_aggregates, credit_note
                    )
                )
            if auto_settled_debit_aggregates:
                remarks = f"{auto_settled_debit_aggregates[0].debit.remarks} Initially. Now settling via Credit Note"
                auto_settled_debit_aggregates[0].debit.remarks = remarks
                credit_aggregates.append(
                    self._create_settlements_for_debits(
                        auto_settled_debit_aggregates, credit_note
                    )
                )

        if credit_aggregates:
            logger.info("Creating %s credits from credit_note", len(credit_aggregates))
            self.credit_repo.save_all(credit_aggregates)

        if debit_aggregates:
            self.debit_repo.update_all(debit_aggregates)

    def _create_settlements_for_debits(self, debit_aggregates, credit_note):
        credit_note_dto = CreditNoteDto(
            debtor_id=debit_aggregates[0].debit.debtor_id,
            credit_note_id=credit_note.get("credit_note_id"),
            credit_note_number=credit_note.get("credit_note_number"),
            credit_note_date=dateutils.ymd_str_to_date(
                credit_note.get("credit_note_date")
            ),
            credit_note_amount=Money(
                credit_note.get("posttax_amount"), CurrencyType.INR
            ),
            line_items=credit_note.get("credit_note_line_items"),
            vendor_id=credit_note.get("vendor_details").get("vendor_id"),
            tenant_id=get_tenant_id(),
        )

        credit_aggregate = CreditFactory.create_from_credit_note(credit_note_dto)

        logger.info(
            f"Creating Settlement for Credit Note: {credit_note.get('credit_note_id')}"
        )
        credit_aggregates_with_reversed_settlements = (
            self._create_settlements_for_credit_note(
                credit_aggregate, credit_note_dto, debit_aggregates
            )
        )

        self.credit_repo.update_all(credit_aggregates_with_reversed_settlements)
        return credit_aggregate

    def _load_existing_debits_and_invoices(self, credit_notes):
        invoice_ids = set()
        for credit_note in credit_notes:
            invoice_ids.update(
                {
                    line_item.get("invoice_id")
                    for line_item in credit_note.get("credit_note_line_items")
                }
            )

        debit_aggregates = self.debit_repo.load_for_invoices(
            invoice_ids=list(invoice_ids)
        )
        invoices = [CrsClient.get_invoice(invoice_id) for invoice_id in invoice_ids]
        return debit_aggregates, invoices

    def _create_settlements_for_credit_note(
        self, credit_aggregate, credit_note_dto, debit_aggregates: List[DebitAggregate]
    ):
        """
        Creates multiple settlements for given credit_aggregate (of CreditType.CREDIT_NOTE), against debit_aggregates
        of invoices against which this credit note was generated

        If some debit_aggregate is found to already have been settled via Payment, then first all those Payment
        Settlements will be reversed, and corresponding Payment Credit will have it's unused_credit_amount increased,
        before applying Credit Note related settlement on that debit aggregate

        :param credit_aggregate:
        :param credit_note_dto:
        :param debit_aggregates:
        :return: All the credit aggregates (of CreditType.Payment) for which unused_credit_amount gets increased,
        and their respective settlements gets reversed as a process of applying credit note settlement to the debit (
        invoice)
        """
        updated_credit_aggregates = []
        grouped_debit_aggregates = {
            aggregate.debit.reference_id: aggregate for aggregate in debit_aggregates
        }
        for invoice_id, amount in credit_note_dto.invoice_wise_amounts.items():
            debit_aggregate = grouped_debit_aggregates.get(invoice_id)
            if not debit_aggregate:
                continue

            try:
                logger.info(
                    f"Trying to Create Settlement for Credit Note: {credit_note_dto.credit_note_id}"
                )
                settlement = debit_aggregate.create_new_settlement(
                    credit_aggregate,
                    amount,
                    remarks="Credit note issued against this invoice",
                )
                credit_aggregate.knock_off_settlement(settlement)

            except InvalidSettlementException:
                logger.info(
                    f"Exception while creating Settlement for Credit Note: {credit_note_dto.credit_note_id}"
                )
                updated_credit_aggregates.extend(
                    self._reverse_settlements_to_accommodate_credit_note(
                        debit_aggregate, amount
                    )
                )
                logger.info(
                    f"Again Create Settlement for Credit Note: {credit_note_dto.credit_note_id}"
                )
                settlement = debit_aggregate.create_new_settlement(
                    credit_aggregate,
                    amount,
                    remarks="Credit note issued against this invoice",
                )
                credit_aggregate.knock_off_settlement(settlement)

        return updated_credit_aggregates

    def _reverse_all_settlements_to_accommodate_credit_note(self, debit_aggregate):
        reversed_settlements = debit_aggregate.reverse_payment_settlement_to_accommodate_credit_note_settlement(
            all_settlements=True
        )
        credit_aggregates_for_reversed_settlements = (
            self.credit_repo.load_all_for_update(
                [settlement.credit_id for settlement in reversed_settlements]
            )
        )
        grouped_credit_aggregates = {
            aggregate.credit_id: aggregate
            for aggregate in credit_aggregates_for_reversed_settlements
        }

        for settlement in reversed_settlements:
            reversed_credit_aggregate = grouped_credit_aggregates.get(
                settlement.credit_id
            )
            reversed_credit_aggregate.reverse_settlement_amount(settlement.amount)
            if reversed_credit_aggregate.credit.used_to_auto_settle_debit:
                reversed_credit_aggregate.cancel()
            if reversed_credit_aggregate.is_tds_credit():
                reversed_credit_aggregate.delete()

        return credit_aggregates_for_reversed_settlements

    def _reverse_settlements_to_accommodate_credit_note(
        self, debit_aggregate, credit_note_amount
    ):
        reversed_settlements = debit_aggregate.reverse_payment_settlement_to_accommodate_credit_note_settlement(
            credit_note_amount
        )
        credit_aggregates_for_reversed_settlements = (
            self.credit_repo.load_all_for_update(
                [settlement.credit_id for settlement in reversed_settlements]
            )
        )
        grouped_credit_aggregates = {
            aggregate.credit_id: aggregate
            for aggregate in credit_aggregates_for_reversed_settlements
        }

        for settlement in reversed_settlements:
            reversed_credit_aggregate = grouped_credit_aggregates.get(
                settlement.credit_id
            )
            reversed_credit_aggregate.reverse_settlement_amount(settlement.amount)
            if reversed_credit_aggregate.credit.used_to_auto_settle_debit:
                reversed_credit_aggregate.cancel()
            if reversed_credit_aggregate.is_tds_credit():
                reversed_credit_aggregate.delete()

        return credit_aggregates_for_reversed_settlements

    @unit_of_work
    def create_update_user_debtor_info_using_profile_data(self, poc_user, debtor_dto):
        user_model = (
            self.user_service.create_or_update_user(poc_user) if poc_user else None
        )
        self.debtor_service.create_or_update_debtor_user_debtor_mapping_from_profile(
            debtor_dto, user_model
        )

    @staticmethod
    def _get_invoice_meta_details(booking, user_info_map, vendor_details):
        if not user_info_map:
            return None
        primary_guests = []
        for _, customer_info in user_info_map.items():
            if customer_info["is_primary"]:
                primary_guests.append(
                    f"{customer_info['name']['first_name'] or ''} {customer_info['name']['last_name'] or ''}".strip()
                )
        return InvoiceMeta(
            ", ".join(primary_guests),
            vendor_details.get("hotel_name"),
            vendor_details.get("hotel_id"),
            booking.actual_checkin_date or booking.checkin_date,
            booking.actual_checkout_date or booking.checkout_date,
        )
