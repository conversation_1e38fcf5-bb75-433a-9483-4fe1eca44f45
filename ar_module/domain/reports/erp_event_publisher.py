import logging

from kombu import Exchange, Producer
from ths_common.exceptions import CRSException
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from ar_module.infrastructure.consumers.consumer_config import (
    InterfaceExchangePublisherConfig,
)
from ar_module.infrastructure.messaging.queue_service import BaseQueueService
from object_registry import register_instance

logger = logging.getLogger(__name__)


class ERPEvent:
    def __init__(self, body, routing_key, event_type, event_id, batch_id, source):
        self.body = body
        self.routing_key = routing_key
        self.event_type = event_type
        self.event_id = event_id
        self.batch_id = batch_id
        self.source = source


@register_instance()
class ERPEventPublisher(BaseQueueService):
    def _setup_entities(self):
        config = InterfaceExchangePublisherConfig()
        self._erp_event_exchange = Exchange(
            config.exchange_name, type=config.exchange_type, durable=True
        )
        self._tenant_wise_producers = dict()
        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._erp_event_exchange
            )

    def publish(self, event):
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()

        payload = self.get_event_payload(event)
        routing_key = self.get_event_routing_key(event)
        logger.debug(f"Publishing event {payload}")

        if not self._tenant_wise_producers[tenant_id]:
            raise CRSException(
                description=f"RMQ Producer not configured for tenant_id: {tenant_id}"
            )
        self._publish(self._tenant_wise_producers[tenant_id], payload, routing_key)

    @staticmethod
    def get_event_payload(event):
        return {
            "body": event.body,
            "event_type": event.event_type,
            "event_id": event.event_id,
            "batch_id": event.batch_id,
            "source": event.source,
        }

    @staticmethod
    def get_event_routing_key(event):
        return event.routing_key
