from ar_module.domain.entities.user import User
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.user_model import UserModel


class UserAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: User, **kwargs):
        # noinspection PyArgumentList
        return UserModel(
            user_id=domain_entity.user_id,
            email=domain_entity.email,
            phone_number=domain_entity.phone_number,
            first_name=domain_entity.first_name,
            last_name=domain_entity.last_name,
        )

    def to_domain_entity(self, db_entity: UserModel, **kwargs):
        return User(
            user_id=db_entity.user_id,
            email=db_entity.email,
            phone_number=db_entity.phone_number,
            first_name=db_entity.first_name,
            last_name=db_entity.last_name,
        )
