-- revision: '20200917201727_credit_refnumb_sequence_model'
-- down_revision: '20200916093711_initial_models'

-- upgrade
CREATE TABLE credit_reference_number_series (
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    modified_at timestamp with time zone DEFAULT now() NOT NULL,
    series_id SERIAL,
    last_sequence_number integer NOT NULL DEFAULT 0,
    credit_date date NOT NULL,
    deleted boolean,
    credit_type varchar NOT NULL,
    debtor_id varchar NOT NULL
);

ALTER TABLE credit_reference_number_series
    ADD CONSTRAINT credit_reference_number_series_pkey PRIMARY KEY (series_id);

ALTER TABLE credit_reference_number_series
    ADD CONSTRAINT credit_reference_number_series_uc UNIQUE (debtor_id, credit_type, credit_date);

-- downgrade
ALTER TABLE credit_reference_number_series DROP CONSTRAINT credit_reference_number_series_uc;
ALTER TABLE credit_reference_number_series DROP CONSTRAINT credit_reference_number_series_pkey;
DROP TABLE credit_reference_number_series;
