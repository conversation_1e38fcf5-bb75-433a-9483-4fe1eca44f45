import logging

from flask import current_app
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from ar_module.application.consumers.decorator import consumer_middleware
from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.common.slack_alert_helper import Slack<PERSON><PERSON><PERSON>
from ar_module.domain.constants import InvoiceStatusToConsume
from ar_module.exceptions import DebitReferenceNumberCollision
from ar_module.infrastructure.consumers.base_consumer import BaseRMQConsumer
from ar_module.infrastructure.consumers.consumer_config import CRSConfig
from object_registry import locate_instance

logger = logging.getLogger(__name__)


class CRSConsumer(BaseRMQConsumer):
    INVOICE_ENTITY_NAME = "invoice"
    CREDIT_NOTE_ENTITY_NAME = "credit_note"

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        super().__init__(CRSConfig(tenant_id))
        self.tenant_id = tenant_id
        logger.info(
            "Listening to RMQ on host: %s from queue: %s", self.connection, self.queue
        )
        self.accounts_receivable_service = locate_instance(
            AccountReceivableApplicationService
        )

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        """
        dict(message_id=event_id, generated_at=generated_at, events=[{"entity_name": "credit_note", "payload": ...}],
            user_action=integration_event_dto.user_action, event_type=event_type.value)
        :param body:
        :param message:
        :return:
        """
        message.ack()
        logger.info("Received Credit Note or Invoice Event. Message Acked")

        if not any(
            event.get("entity_name")
            in (self.CREDIT_NOTE_ENTITY_NAME, self.INVOICE_ENTITY_NAME)
            for event in body.get("events")
        ):
            return

        credit_notes, invoices = [], []
        with current_app.test_request_context():
            for event in body.get("events"):
                # Every event is dict(entity_name=<entity_name>, payload=<payload>)
                if event.get("entity_name") == self.CREDIT_NOTE_ENTITY_NAME:
                    credit_notes.append(event.get("payload"))
                elif event.get("entity_name") == self.INVOICE_ENTITY_NAME:
                    logger.debug(
                        "Invoice status: %s", event.get("payload").get("status")
                    )
                    if event.get("payload").get("status") in {
                        InvoiceStatusToConsume.GENERATED,
                        InvoiceStatusToConsume.LOCKED,
                        InvoiceStatusToConsume.CANCELLED,
                    }:
                        invoices.append(event.get("payload"))

            try:
                if not invoices:
                    logger.debug("No invoice found to be processed by AR Module")
                else:
                    logger.info("Processing Invoice Event")
                    self.accounts_receivable_service.process_crs_invoices_event(
                        invoices
                    )
            except DebitReferenceNumberCollision as collision_exc:
                invoice_ids = [invoice.get("invoice_id") for invoice in invoices]
                logger.error(
                    "Exception occurred while processing Invoice event: {0}: {1}".format(
                        invoice_ids, str(collision_exc)
                    )
                )

            except Exception as exc:
                invoice_ids = [invoice.get("invoice_id") for invoice in invoices]
                logger.exception(
                    "Exception occurred while processing Invoice event: {0}".format(
                        invoice_ids
                    )
                )
                SlackAlert.send_alert(
                    "Exception occurred while processing invoice event: {0} => {1}".format(
                        invoice_ids, str(exc)
                    ),
                    tenant_id=self.tenant_id,
                )

            try:
                if not credit_notes:
                    logger.debug("No credit note found to be processed by AR Module")
                else:
                    logger.info("Processing Credit Note Event")
                    self.accounts_receivable_service.process_credit_notes_event(
                        credit_notes
                    )
            except Exception as exc:
                credit_note_ids = [
                    credit_note.get("credit_note_id") for credit_note in credit_notes
                ]
                logger.exception(
                    "Exception occurred while processing Credit Note event: {0}".format(
                        credit_note_ids
                    )
                )
                SlackAlert.send_alert(
                    "Exception occurred while processing credit note event: {0} => {1}".format(
                        credit_note_ids, str(exc)
                    ),
                    tenant_id=self.tenant_id,
                )

        logger.info("Completed processing Credit Note or Invoice Event")
