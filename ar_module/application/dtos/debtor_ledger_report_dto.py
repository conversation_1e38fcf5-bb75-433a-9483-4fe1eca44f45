class SummarisedDebtorLedgerReportDto(object):
    def __init__(
        self,
        total_debits,
        total_credits,
        total_provisional_credits,
        unapplied_payment_balance,
        gross_receivables,
        net_receivables,
    ):
        self.total_debits = total_debits
        self.total_credits = total_credits
        self.total_provisional_credits = total_provisional_credits
        self.unapplied_payment_balance = unapplied_payment_balance
        self.gross_receivables = gross_receivables
        self.net_receivables = net_receivables


class DetailedDebtorLedgerReportDto(object):
    def __init__(
        self,
        debtor_code,
        debtor_name,
        date,
        reference_number,
        entry_type,
        amount,
        settlement_date=None,
        debtor_net_balance=None,
        settlement_reference_number=None,
        settlement_status=None,
        debtor_type=None,
        sub_type=None,
    ):
        self.debtor_code = debtor_code
        self.debtor_name = debtor_name
        self.debtor_type = debtor_type
        self.date = date
        self.reference_number = reference_number
        self.entry_type = entry_type
        self.settlement_date = settlement_date
        self.amount = amount
        self.debtor_net_balance = debtor_net_balance
        self.settlement_reference_number = settlement_reference_number
        self.settlement_status = settlement_status
        self.sub_type = sub_type


class DebtorLedgerReportDto(object):
    def __init__(
        self,
        summary,
        detailed,
        summarised_report_download_url,
        detailed_report_download_url,
        limit=None,
        offset=None,
        total=None,
    ):
        self.summary = summary
        self.detailed = detailed
        self.summarised_report_download_url = summarised_report_download_url
        self.detailed_report_download_url = detailed_report_download_url
        self.limit = limit
        self.offset = offset
        self.total = total


class DebtorLedgerSummaryDto(object):
    def __init__(self, total_credits, total_unsettled_debits, total_unused_credits):
        self.total_credits = total_credits
        self.total_unsettled_debits = total_unsettled_debits
        self.total_unused_credits = total_unused_credits
