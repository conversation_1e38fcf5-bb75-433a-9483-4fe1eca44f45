from ar_module.domain.aggregates.debtor_aggregate import DebtorAggregate
from ar_module.domain.constants import SortOrders
from ar_module.exceptions import AggregateNotFound, HotelIDHeaderMisMatch
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from ar_module.infrastructure.database.repositories.debtor.db_adaptor.debtor_adaptor import (
    DebtorAdaptor,
)
from ar_module.infrastructure.database.repositories.user.db_adaptor.user_adaptor import (
    UserAdaptor,
)
from object_registry import register_instance


@register_instance()
class DebtorRepository(BaseRepository):
    debtor_adaptor = DebtorAdaptor()
    user_adaptor = UserAdaptor()

    def from_aggregate(self, aggregate: DebtorAggregate = None):
        debtor = aggregate.debtor
        debtor_model = self.debtor_adaptor.to_db_entity(debtor)
        return debtor_model

    def search_debtors(self, debtor_search_query):
        debtor_query = self.query(DebtorModel)
        debtor_query = self._apply_filters(debtor_query, debtor_search_query)
        if debtor_search_query.sort_order:
            if debtor_search_query.sort_order == SortOrders.ASC:
                debtor_query = debtor_query.order_by(DebtorModel.debtor_name.asc())
            else:
                debtor_query = debtor_query.order_by(DebtorModel.debtor_name.desc())
        if (
            debtor_search_query.limit is not None
            and debtor_search_query.offset is not None
        ):
            debtor_query = debtor_query.limit(debtor_search_query.limit).offset(
                debtor_search_query.offset
            )

        debtor_models = debtor_query.all()
        return [
            DebtorAggregate(self.debtor_adaptor.to_domain_entity(debtor_model))
            for debtor_model in debtor_models
        ]

    def count_debtors(self, debtor_search_query):
        debtor_query = self.query(DebtorModel)
        debtor_query = self._apply_filters(debtor_query, debtor_search_query)
        return debtor_query.count()

    @staticmethod
    def _apply_filters(debtor_query, debtor_search_query):
        if debtor_search_query.user_accessible_debtors_id:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_id.in_(
                    debtor_search_query.user_accessible_debtors_id
                )
            )

        if debtor_search_query.debtor_code:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_code == debtor_search_query.debtor_code
            )

        if debtor_search_query.debtor_id:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_id == debtor_search_query.debtor_id
            )

        if debtor_search_query.debtor_type:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_type == debtor_search_query.debtor_type
            )

        if debtor_search_query.debtor_name:
            escaped_query = debtor_search_query.debtor_name.replace("%", "\%")
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_name.ilike(escaped_query + "%")
            )

        if debtor_search_query.debtor_ids:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_id.in_(debtor_search_query.debtor_ids)
            )

        if debtor_search_query.debtor_codes:
            debtor_query = debtor_query.filter(
                DebtorModel.debtor_code.in_(debtor_search_query.debtor_codes)
            )

        if debtor_search_query.hotel_id:
            debtor_query = debtor_query.filter(
                DebtorModel.hotel_id == debtor_search_query.hotel_id
            )

        if debtor_search_query.query:
            q1 = DebtorModel.debtor_name.ilike(debtor_search_query.query + "%")
            q1 = q1 | DebtorModel.debtor_code.ilike(debtor_search_query.query + "%")
            debtor_query = debtor_query.filter(q1)
        return debtor_query

    def save(self, debtor_aggregate):
        debtor_model = self.debtor_adaptor.to_db_entity(debtor_aggregate.debtor)
        self._save(debtor_model)
        self.flush_session()

    def load(self, debtor_id, hotel_id=None, is_hotel_level_ar_configured=None):
        debtor_model = (
            self.query(DebtorModel).filter(DebtorModel.debtor_id == debtor_id).first()
        )
        if not debtor_model:
            raise AggregateNotFound("DebtorModel", debtor_id)
        debtor_aggregate = DebtorAggregate(
            self.debtor_adaptor.to_domain_entity(debtor_model)
        )

        if (
            is_hotel_level_ar_configured
            and hotel_id
            and debtor_aggregate.hotel_id != hotel_id
        ):
            raise HotelIDHeaderMisMatch()

        return debtor_aggregate

    def load_all(self, debtor_ids=None):
        debtor_models = self.query(DebtorModel)
        if debtor_ids:
            debtor_models = debtor_models.filter(DebtorModel.debtor_id.in_(debtor_ids))

        debtor_models = debtor_models.all()
        return [
            DebtorAggregate(self.debtor_adaptor.to_domain_entity(debtor_model))
            for debtor_model in debtor_models
        ]

    def update(self, aggregate: DebtorAggregate):
        debtor_model = self.from_aggregate(aggregate)
        self._update(debtor_model)
        self.flush_session()

    def update_debtor_and_user_debtor_mapping(self, debtor_model, user_model):
        if user_model and user_model not in debtor_model.users:
            # TODO: add application layer support for multiple user association
            # SH profile payload if have multiple f_poc we should prepare
            # multiple user mode and override the existing list
            debtor_model.users = [user_model]
        elif not user_model:
            # Remove mapping of users if user_model is None
            debtor_model.users = []
        self._update(debtor_model)
        self.flush_session()

    def save_debtor_and_user_debtor_mapping(self, debtor_aggregate, user_model):
        debtor_model = self.debtor_adaptor.to_db_entity(debtor_aggregate.debtor)
        if user_model:
            # pylint: disable=no-member
            debtor_model.users.append(user_model)
            # pylint: enable=no-member
        self._save(debtor_model)
        self.flush_session()
