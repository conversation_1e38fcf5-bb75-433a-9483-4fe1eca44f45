class SettlementCreditDetailsDto:
    def __init__(self, settlement_data, credit_data, debit_data):
        self.amount = settlement_data.amount
        self.credit_id = settlement_data.credit_id
        self.payment_reference_number = settlement_data.reference_number
        self.invoice_reference_number = debit_data.reference_number
        self.settled_via = settlement_data.settled_via
        self.settlement_date = settlement_data.settlement_date
        self.settlement_id = settlement_data.settlement_id
        self.debit_id = (
            settlement_data.debit_id if hasattr(settlement_data, "debit_id") else None
        )
        self.mode_of_credit = credit_data.mode_of_credit if credit_data else None
        self.remarks = settlement_data.remarks
