import logging

from ths_common.value_objects import UserData
from treebo_commons.request_tracing.context import request_context

from ar_module.application.dtos.credit_dto import SettlementDtoV2
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.settlement_service import SettlementService
from ar_module.application.utils import is_payment_mode_supported_in_csv_uploads
from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from ar_module.async_job.handlers.bulk_data_ingestors.csv_processor import (
    BaseCsvProcessor,
)
from ar_module.async_job.handlers.bulk_data_ingestors.settlements.settlement_schema import (
    SettlementUploadSchema,
)
from ar_module.domain.constants import (
    CreditStatus,
    SettlementUploaderAction,
    SortOrders,
    UserType,
)
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.external_clients.notification_service_client import (
    EmailConfigDto,
    NotificationServiceClient,
)
from object_registry import locate_instance, register_instance

CREDIT_UPLOAD_RESULT_S3_FOLDER_NAME = "ar-settlement-upload-results/"
PROCESS_NAME = "settlement-csv-upload"

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        NotificationServiceClient,
        DebitRepository,
        CreditRepository,
        DebtorService,
        TenantSettings,
    ]
)
class SettlementUploadHandler(BaseCsvProcessor, BaseJobHandler):
    def __init__(
        self,
        notification_service_client: NotificationServiceClient,
        debit_repo: DebitRepository,
        credit_repo: CreditRepository,
        debtor_service: DebtorService,
        tenant_settings: TenantSettings,
    ):
        BaseCsvProcessor.__init__(
            self,
            notification_service_client,
            SettlementUploadSchema,
            CREDIT_UPLOAD_RESULT_S3_FOLDER_NAME,
            PROCESS_NAME,
        )
        self.settlement_service = locate_instance(SettlementService)
        self.debit_repo = debit_repo
        self.credit_repo = credit_repo
        self.debtor_service = debtor_service
        self.tenant_settings = tenant_settings

    def execute_job(self, job_id, job_data):
        file_url = job_data.get("file_url")
        is_hotel_level_ar_configured = (
            self.settlement_service.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        user_data = job_data.get("user_data")
        user_data = UserData(
            user_type=user_data.get("user_type") or UserType.BACKEND_SYSTEM,
            user=user_data.get("user") or UserType.BACKEND_SYSTEM,
            hotel_id=user_data.get("hotel_id"),
            user_auth_id=user_data.get("user_auth_id"),
        )
        if is_hotel_level_ar_configured:
            request_context.hotel_id = user_data.hotel_id
        base_currency = self.tenant_settings.get_chain_base_currency()
        self.process_file(
            file_url,
            is_hotel_level_ar_configured=is_hotel_level_ar_configured,
            user_data=user_data,
            ack_email_config=self.get_email_config_for_ack(job_data),
            base_currency=base_currency,
        )
        request_context.hotel_id = None

    @staticmethod
    def get_email_config_for_ack(job_data):
        if not job_data.get("ack_email"):
            return None
        return EmailConfigDto(
            subject=f"{PROCESS_NAME} results",
            receivers=[job_data.get("ack_email")],
        )

    def ingest_data(
        self,
        settlement_dto: SettlementDtoV2,
        ack_record,
        is_hotel_level_ar_configured=False,
        user_data=None,
        **kwargs,
    ):
        try:
            debit = self.debit_repo.get_debit_by_reference_number(
                settlement_dto.invoice_number, auto_settled_via_credit=False
            )
            if not settlement_dto.credit_id:
                settlement_dto.credit_id = self._find_eligible_credit_id(
                    settlement_dto, debit.debtor_id
                )
            credit = self.credit_repo.load(credit_id=settlement_dto.credit_id)
            is_payment_mode_supported_in_csv_uploads(credit.credit.mode_of_credit)
            settlement_dto.debit_id = debit.debit.debit_id

            debtor_aggregate = self._do_debtor_policy_check_on_debtor(
                credit, user_data, is_hotel_level_ar_configured
            )

            assert (
                credit.debtor_id == debit.debtor_id
            ), "Debtor id of credit and debit should match"
            if settlement_dto.action == SettlementUploaderAction.MAPPING:
                settlements = (
                    self.settlement_service.map_credit_and_debit_to_create_settlement(
                        [settlement_dto],
                        settlement_dto.credit_id,
                        debtor_aggregate,
                        user_data,
                        only_reverse_settlements_under_same_debit=True,
                    )
                )
            else:
                settlements = self.settlement_service.unmap_settlement(
                    user_data, settlement_dto, debtor_aggregate
                )
            return settlements, None
        except Exception as e:
            logger.exception(e)
            return None, str(e)

    def _do_debtor_policy_check_on_debtor(
        self, credit, user_data, is_hotel_level_ar_configured
    ):
        query = DebtorSearchQuery(
            debtor_id=credit.debtor_id,
            hotel_id=user_data.hotel_id,
        )
        debtor_aggregates, _ = self.debtor_service.get_debtors(
            query,
            user_data=user_data,
            hotel_level_accounts_receivable=is_hotel_level_ar_configured,
        )
        if not debtor_aggregates:
            raise Exception(
                f"Don't have access to Debtor {credit.debtor_id} Hotel: {user_data.hotel_id}"
            )
        return debtor_aggregates[0]

    def _find_eligible_credit_id(self, settlement_dto: SettlementDtoV2, debtor_id):
        filters = dict(
            sort_order=SortOrders.ASC,
            reference_number=settlement_dto.payment_id,
            debtor_id=debtor_id,
        )
        credit_search_query = CreditSearchQuery(**filters)
        credit_aggregates = self.credit_repo.load_credits(credit_search_query)
        credit_aggregates = [
            credit_aggregate
            for credit_aggregate in credit_aggregates
            if credit_aggregate.credit.status != CreditStatus.CANCELLED
        ]
        if not credit_aggregates:
            raise Exception("Unable to find an given Payment under debtor")
        for credit_aggregate in credit_aggregates:
            if credit_aggregate.credit.unused_credit_amount >= settlement_dto.amount:
                return credit_aggregate.credit.credit_id
        return credit_aggregates[0].credit_id
