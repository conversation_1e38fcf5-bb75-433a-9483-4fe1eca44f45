import csv
import datetime
import logging
import os
import uuid

import click
from flask.cli import with_appcontext
from ths_common.value_objects import EmailAttachment
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from ar_module.application.consumers.constants import DebtorTypes, POCsDesignation
from ar_module.application.dtos.debtor_dto import DebtorDTO
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.application.services.reporting.csv_writer import CsvWriter
from ar_module.domain.constants import DEFAULT_CREDIT_SETTINGS, ARModuleConfigs
from ar_module.infrastructure.external_clients.authentication.authn_service_client import (
    AuthNClient,
)
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.company_profile_service_client import (
    CompanyProfileServiceClient,
)
from ar_module.infrastructure.external_clients.notification_service_client import (
    NotificationEmailIds,
    NotificationSenderName,
    NotificationServiceClient,
)
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("ingest_debtor")
@click.option(
    "--file_path",
    help="file path of csv having debtor info",
    default=None,
)
@click.option(
    "--email",
    help="EMail to receive details run report",
    default=None,
)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=None,
)
@with_appcontext
@inject(
    company_profile_client=CompanyProfileServiceClient,
    tenant_settings=TenantSettings,
    auth_client=AuthNClient,
    accounts_receivable_service=AccountReceivableApplicationService,
    notification_service_client=NotificationServiceClient,
)
def ingest_debtor(
    company_profile_client,
    tenant_settings,
    auth_client,
    accounts_receivable_service,
    notification_service_client,
    file_path,
    email,
    tenant_id=TenantClient.get_default_tenant(),
):
    click.echo("Tenant ID: %s" % tenant_id)
    request_context.tenant_id = tenant_id
    parent_ent_lvl_debtor_creation_enabled = tenant_settings.get_setting_value(
        ARModuleConfigs.PARENT_ENTITY_LEVEL_DEBIT_CREATION
    )
    sub_ent_lvl_debtor_creation_enabled = tenant_settings.get_setting_value(
        ARModuleConfigs.SUB_ENTITY_LEVEL_DEBIT_CREATION
    )
    information_log = []
    with open(file_path, "r") as csv_file:
        reader = csv.DictReader(csv_file)
        for index, row_data in enumerate(reader):
            debtor_code = row_data.get("debtor_code")
            message = ""
            try:
                profile_response = (
                    company_profile_client.get_company_profile_on_company_code(
                        None, debtor_code, return_both_entities=True
                    )
                )
                parent_entities = profile_response.get("parent_entities")
                sub_entities = profile_response.get("sub_entities")
                if not (parent_entities or sub_entities):
                    result = _create_debtor(
                        accounts_receivable_service,
                        auth_client,
                        None,
                        row_data,
                    )
                    message += result
                    information_log.append(
                        dict(debtor_code=debtor_code, message=message)
                    )
                    continue
                if (parent_ent_lvl_debtor_creation_enabled and parent_entities) or (
                    sub_ent_lvl_debtor_creation_enabled and not sub_entities
                ):
                    for profile_data in parent_entities:
                        result = _create_debtor(
                            accounts_receivable_service,
                            auth_client,
                            profile_data,
                            row_data,
                        )
                        message += result
                if sub_ent_lvl_debtor_creation_enabled and sub_entities:
                    for profile_data in sub_entities:
                        result = _create_debtor(
                            accounts_receivable_service,
                            auth_client,
                            profile_data,
                            row_data,
                        )
                        message += result
            except Exception as err:
                message += f"Debtor creation failed for {debtor_code}: error {str(err)}"
                logger.exception(err)
            information_log.append(dict(debtor_code=debtor_code, message=message))
            click.echo(f"{index} : {debtor_code}: {message}")
        if email:
            _generate_csv_upload_results_and_send_email(
                notification_service_client, information_log, email
            )


def _generate_csv_upload_results_and_send_email(
    notification_service_client, data_list, email
):
    column_values = data_list[0].keys()
    file_path, file_name = _generate_file_name()
    with CsvWriter(file_path) as csv_writer:
        csv_writer.write_aggregates(data_list, column_values)
        pre_signed_url = AwsServiceClient.upload_file_to_s3_and_get_presigned_url(
            "ar-debtor-upload-results/",
            csv_writer.file_path,
            604800,
        )
    email_attachments = [EmailAttachment(url=pre_signed_url, filename=file_name)]
    # pylint: disable=no-member
    notification_service_client.email(
        body_html="Hi <br><br>PFA<br><br>",
        subject="Bulk upload of debtor",
        sender=NotificationEmailIds.NOREPLY.value,
        recievers=[email],
        attachments=email_attachments,
        sender_name=NotificationSenderName.TREEBO_HOTELS.value,
        raise_on_failure=False,
        cc_list=[],
    )
    # pylint: enable=no-member


def _generate_file_name(extension="csv"):
    identifier = str(uuid.uuid4())
    file_name = f"{datetime.datetime.now().strftime('%Y-%m-%d')}-debtor-bulk-upload-{identifier}.{extension}"
    return (
        os.environ.get("TEMPORARY_REPORTS_DIRECTORY", "/tmp/") + file_name
    ), file_name


def _create_debtor(
    accounts_receivable_service,
    auth_client,
    profile_data,
    csv_data,
):
    f_poc_email = _find_f_poc_email(profile_data, csv_data.get("f_poc"))
    message = ""
    poc_user = None
    if not f_poc_email:
        message += "Unable to create Debtor User mapping, POC email not found in csv and SH profile"
    else:
        poc_user = auth_client.get_user_by_email(f_poc_email)
        if not poc_user:
            message += f"Unable to create Debtor User mapping, Unable to find user in Auth db {f_poc_email}"
    if profile_data:
        credit_period_from_csv = csv_data.get("credit_period")
        if credit_period_from_csv:
            _populate_credit_period(profile_data, credit_period_from_csv)
        debtor_dto = DebtorDTO.create_from_sh_profile_data(profile_data)
    elif csv_data.get("debtor_name"):
        debtor_dto = _create_debtor_from_csv_data(csv_data)
    else:
        raise Exception(
            "Profile not found both parent_entities and sub_entities are missing/ Debtor name is also missing"
        )

    accounts_receivable_service.create_update_user_debtor_info_using_profile_data(
        poc_user, debtor_dto
    )
    return "Successfully Created Debtor" + message


def _create_debtor_from_csv_data(csv_data):
    credit_limit, credit_period, btc_enabled = (
        DEFAULT_CREDIT_SETTINGS["credit_limit"],
        DEFAULT_CREDIT_SETTINGS["credit_period"],
        False,
    )
    debtor_dto = DebtorDTO(
        debtor_code=csv_data.get("debtor_code"),
        debtor_name=csv_data.get("debtor_name"),
        credit_limit=int(csv_data["credit_limit"])
        if csv_data.get("credit_limit")
        else credit_limit,
        hotel_id=csv_data.get("hotel_id"),
        credit_period=int(csv_data["credit_period"])
        if csv_data.get(credit_period)
        else credit_period,
        btc_enabled=btc_enabled,
        debtor_type=csv_data.get("debtor_type") or DebtorTypes.B2B,
    )
    return debtor_dto


def _find_f_poc_email(profile_data, f_poc_email_from_csv):
    if profile_data and profile_data.get("point_of_contacts"):
        for poc in profile_data["point_of_contacts"]:
            if poc["designation"] == POCsDesignation.FINANCE_POC:
                return (
                    poc["email_ids"][0]
                    if poc.get("email_ids")
                    else f_poc_email_from_csv
                )
    return f_poc_email_from_csv


def _populate_credit_period(profile_data, credit_period_from_csv):
    if not profile_data.get("credit_settings"):
        profile_data["credit_settings"] = {"credit_period": int(credit_period_from_csv)}
    elif not profile_data["credit_settings"].get("credit_period"):
        profile_data["credit_settings"]["credit_period"] = int(credit_period_from_csv)
