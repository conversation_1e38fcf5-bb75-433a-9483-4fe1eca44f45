import logging
import os
from urllib.parse import unquote, urlparse

import boto3 as boto3
from botocore.client import Config
from botocore.exceptions import ClientError
from ths_common.exceptions import DownstreamSystemFailure

from ar_module.infrastructure.external_clients.core.base_client import (
    BaseExternalClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class AwsServiceClient(BaseExternalClient):
    """
    Amazon Web Services Client
    """

    def get_domain(self):
        # Stub to confirm to abstract implementation BaseExternalClient
        raise NotImplementedError("get_domain for AWS client is invalid")

    @staticmethod
    def get_region():
        return os.environ.get("AWS_REGION")

    @staticmethod
    def get_S3_region():
        return os.environ.get("AWS_S3_REGION")

    @staticmethod
    def get_s3_bucket_name():
        return os.environ.get("AWS_S3_BUCKET_NAME")

    @classmethod
    def get_client(cls, service_name, aws_access_key_id, aws_secret_access_key):
        session = boto3.session.Session()
        return session.client(
            service_name,
            region_name=cls.get_S3_region(),
            config=Config(signature_version="s3v4"),
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
        )

    @classmethod
    def get_s3_client(cls, aws_access_key_id=None, aws_secret_access_key=None):
        return cls.get_client("s3", aws_access_key_id, aws_secret_access_key)

    @classmethod
    def upload_file_to_s3_and_get_presigned_url(
        cls, s3_directory, file_path, expires_in
    ):
        key_name = cls.upload_file_to_s3(file_path, s3_directory)
        return cls.get_presigned_url(key_name, expires_in)

    @classmethod
    def upload_file_to_s3(cls, file_path: str, s3_directory):
        s3_client = cls.get_s3_client()
        try:
            object_name = file_path.split("/")[-1]
            key_name = s3_directory + object_name
            s3_client.upload_file(file_path, cls.get_s3_bucket_name(), key_name)
        except FileNotFoundError:
            logger.exception("File not found at: {0}".format(file_path))
            raise
        except ClientError as e:
            logging.error(e)
            raise DownstreamSystemFailure(message="Failed to upload file to S3")
        except Exception:
            logger.exception("Unknown error occurred while upload file to S3")
            raise

        return key_name

    @classmethod
    def get_presigned_url(cls, key_name: str, link_expires_in=None):
        s3_client = cls.get_s3_client(
            aws_access_key_id=os.environ.get("AWS_PRESIGNEDURL_KEY"),
            aws_secret_access_key=os.environ.get("AWS_PRESIGNEDURL_SECRET"),
        )
        try:
            return s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": cls.get_s3_bucket_name(), "Key": key_name},
                ExpiresIn=link_expires_in,
            )
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to get signed url for file.",
                extra_payload={"exception": e},
            )

    @classmethod
    def download_file_from_s3(cls, s3_url, file_obj):
        url = urlparse(s3_url)
        key_name = url.path.lstrip("/")
        s3_client = cls.get_s3_client()
        try:
            s3_client.download_fileobj(cls.get_s3_bucket_name(), key_name, file_obj)
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to download file from s3.",
                extra_payload={"exception": e},
            )

    @classmethod
    def get_presigned_url_from_s3_url(cls, s3_url: str, link_expires_in=None):
        try:
            url = urlparse(s3_url)
            url = url.path.lstrip("/")
            url = unquote(url)
        except Exception as e:
            raise DownstreamSystemFailure(
                message="Unable to parse url for s3 link: {}".format(s3_url),
                extra_payload={"exception": e},
            )
        return cls.get_presigned_url(url, link_expires_in)
