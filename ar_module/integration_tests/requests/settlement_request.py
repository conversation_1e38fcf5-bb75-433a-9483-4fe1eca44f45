import json

from ar_module.integration_tests.builders import create_settlement_builder
from ar_module.integration_tests.config.request_uris import (
    CREATE_SETTLEMENT_URI,
    GET_SETTLEMENT_URI,
)
from ar_module.integration_tests.config.sheet_names import CREATE_SETTLEMENT_SHEET_NAME
from ar_module.integration_tests.mockers import (
    mock_applicable_roles_for_payment_mode,
    mock_get_roles,
    mock_get_tenant_configs,
    mock_is_hotel_level_accounts_receivable_configured,
)
from ar_module.integration_tests.requests.base_request import BaseRequest
from ar_module.integration_tests.utilities.common_utils import del_none


class SettlementRequest(BaseRequest):
    def create_settlement_request(
        self,
        client,
        test_case_id,
        debit_id,
        credit_id,
        status_code,
        user_type,
        pay_mode,
        sheet_name=CREATE_SETTLEMENT_SHEET_NAME,
        hotel_id=None,
        config_value=False,
    ):
        request_json = json.dumps(
            del_none(
                create_settlement_builder.CreateSettlementBuilder(
                    sheet_name, test_case_id, debit_id
                ).__dict__
            )
        )
        uri = CREATE_SETTLEMENT_URI.format(credit_id)
        with mock_get_tenant_configs(
            config_value
        ), mock_is_hotel_level_accounts_receivable_configured(config_value):
            with mock_get_roles(), mock_applicable_roles_for_payment_mode(pay_mode):
                response = self.request_processor(
                    client,
                    "POST",
                    uri,
                    status_code,
                    request_json,
                    user_type=user_type,
                    hotel_id=hotel_id,
                )
        return response

    def get_settlement_request(
        self, client, credit_id, status_code, user_type, config_value=False
    ):
        uri = GET_SETTLEMENT_URI.format(credit_id=credit_id)
        with mock_get_tenant_configs(
            config_value
        ), mock_get_roles(), mock_is_hotel_level_accounts_receivable_configured(
            config_value
        ):
            response = self.request_processor(
                client, "GET", uri, status_code, user_type=user_type
            )
        return response
