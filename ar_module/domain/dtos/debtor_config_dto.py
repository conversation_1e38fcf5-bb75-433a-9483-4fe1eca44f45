class DebtorConfigDto:
    def __init__(
        self,
        debtor_code=None,
        payment_mode=None,
        allowed_payment_modes=None,
        settlement_frequency=None,
        create_auto_debit=True,
        **kwargs
    ):
        self.debtor_code = debtor_code
        self.payment_mode = payment_mode
        self.allowed_payment_modes = allowed_payment_modes
        self.settlement_frequency = settlement_frequency
        self.create_auto_debit = create_auto_debit
        self._allowed_payment_modes = {
            item["value"] for item in self.allowed_payment_modes or []
        }

    def has_access_to_payment(self, payment_mode):
        return payment_mode in self._allowed_payment_modes
