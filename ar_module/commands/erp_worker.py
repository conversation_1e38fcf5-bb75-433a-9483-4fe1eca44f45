# coding=utf-8
"""Click commands."""

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient

from ar_module.core.threadlocal import consumer_context
from ar_module.domain.reports.erp_event_consumer import ERPServiceConsumer


@click.command("start_erp_worker")
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
def start_erp_worker(tenant_id=TenantClient.get_default_tenant()):
    # TODO: Figure out a way to only setup database session registry for this tenant in db_engine.
    click.echo("Tenant ID: %s" % tenant_id)
    consumer_context.set_tenant_id(tenant_id)
    consumer = ERPServiceConsumer(tenant_id)
    consumer.start_consumer()
