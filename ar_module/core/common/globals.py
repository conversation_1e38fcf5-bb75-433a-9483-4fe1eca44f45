from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id


class GlobalContext:
    def __init__(self):
        self.tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self.privileges_as_dict = None
        self.role_privilege_dtos = None
        self.user_data = None

    def set_tenant_id(self, tenant_id):
        self.tenant_id = tenant_id

    def set_user_data(self, user_data):
        self.user_data = user_data

    def get_current_seller_id(self):
        if not self.user_data:
            return None
        return self.user_data.seller_id

    def get_current_hotel_id(self):
        if not self.user_data:
            return None
        return self.user_data.hotel_id

    def get_user_type(self):
        if self.user_data:
            return self.user_data.user_type

    def clear(self):
        self.tenant_id = None
        self.privileges_as_dict = None
        self.role_privilege_dtos = None
        self.user_data = None


global_context = GlobalContext()
