werkzeug==0.16.0
Flask==1.0
Flask-Admin==1.4.2
SQLAlchemy==1.3.2
Flask-SQLAlchemy==2.1
Flask-Migrate==2.0.2
flasgger==0.8.0
flask-swagger-ui==3.6.0
requests==2.18.4
kombu==3.0.35
transitions==0.6.8
aenum==2.1.2
python-dateutil==2.7.2

# Generating GraphViz dot files
sadisplay==0.4.6
# Convert dot files to png file
graphviz==0.5.2

psycopg2==2.7.5
gevent==22.10.1
gunicorn==19.8.0
psycogreen==1.0

# Documentation
Sphinx==1.5.2
sphinxcontrib-httpdomain==1.5.0
sphinx-rtd-theme

logstash-formatter==0.5.16

newrelic==4.20.1.121

#Utils
attrs==21.4.0
deepdiff==3.3.0
eventsourcing==4.0.0
apispec==0.31.0
jsonpickle==0.9.6
simplejson==3.17.6
jsonschema==2.6.0
Jinja2==2.11.3
MarkupSafe==1.1.1
XlsxWriter==3.1.9

# Reporting
boto3==1.9.171

# Google's phonenumber library
phonenumbers==8.10.17

# Segment analytics
analytics-python==1.2.3

# Keep this at the end, since this library uses a modified version of marshmallow library.
# If there is some library after this, which installs marshmallow, then importing this will fail with:
# ImportError: cannot import name 'SchemaJit'
toastedmarshmallow==2.15.2.post1
marshmallow==2.13.5

PyYAML==5.3.1
