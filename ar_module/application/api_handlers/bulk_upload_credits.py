import logging

from ar_module.application.api_handlers.base_file_upload_api_handler import (
    BaseFileUploadAPIHandler,
)
from ar_module.async_job.job.job_constants import JobName
from ar_module.async_job.job_scheduler_service import JobSchedulerService
from ar_module.core.common.globals import global_context
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.facts.create_credit_facts import CreateCreditFacts
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.external_clients.authentication.authn_service_client import (
    AuthNClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[JobSchedulerService, UserRepository, AuthNClient])
class BulkUploadCreditsHandler(BaseFileUploadAPIHandler):
    def __init__(
        self,
        job_scheduler_service: JobSchedulerService,
        user_repo: UserRepository,
        auth_client: AuthNClient,
    ):
        super(BulkUploadCreditsHandler, self).__init__(
            job_scheduler_service, user_repo, auth_client
        )

    def is_authorised(self, user_data):
        RuleEngine.action_allowed(
            action="create_credit",
            facts=CreateCreditFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

    def get_job_name(self):
        return JobName.BULK_UPLOAD_OF_CREDITS
