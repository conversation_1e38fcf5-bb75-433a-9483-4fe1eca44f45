# Dockerfile

# FROM directive instructing base image to build upon
FROM python:3.6.6-alpine

ARG req_file=requirements.txt
RUN mkdir -p /usr/src/app /usr/src/doc /var/log/ar_module/ /tmp/reports/
WORKDIR /usr/src/app

COPY requirements /usr/src/app/requirements

RUN apk add --update bash \
    && rm -rf /var/cache/apk/* \
    && apk add --no-cache py3-psycopg2 \
    && apk add --no-cache --virtual .build-deps \
    build-base postgresql-dev libffi-dev \
    && find /usr/local \
        \( -type d -a -name test -o -name tests \) \
        -o \( -type f -a -name '*.pyc' -o -name '*.pyo' \) \
        -exec rm -rf '{}' + \
    && runDeps="$( \
        scanelf --needed --nobanner --recursive /usr/local \
                | awk '{ gsub(/,/, "\nso:", $2); print "so:" $2 }' \
                | sort -u \
                | xargs -r apk info --installed \
                | sort -u \
    )" \
    && apk add --virtual .rundeps $runDeps \
    && pip3 install -r $req_file \
    && apk del .build-deps

COPY . /usr/src/app/

ENV PYTHONPATH $PYTHONPATH:/usr/src/app/

RUN chmod +x /usr/src/app/gunicorn.sh
RUN chmod +x /usr/src/app/ar_module/workers/async_job_worker.sh

# EXPOSE port 8000 to allow communication to/from server
EXPOSE 8000
