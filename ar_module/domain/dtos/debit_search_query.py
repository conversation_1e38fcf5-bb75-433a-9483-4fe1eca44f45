class DebitSearchQuery:
    def __init__(
        self,
        debtor_id=None,
        has_unsettled_amount=None,
        reference_id=None,
        reference_numbers=None,
        debit_date=None,
        hotel_id=None,
        only_manual_debits=None,
        auto_settled_via_credit=None,
        debit_ids=None,
        debtor_ids=None,
        from_date=None,
        to_date=None,
        over_due_date_filter=None,
        only_over_due_invoice=None,
        non_over_due_date_filter=None,
        only_non_over_due_invoice=None,
        include_cancelled=False,
        settlement_status=None,
        limit=None,
        offset=None,
        sort_order=None,
        **kwargs,
    ):
        self.debtor_id = debtor_id
        self.debit_date = debit_date
        self.has_unsettled_amount = has_unsettled_amount
        self.reference_numbers = (
            reference_numbers.split(",") if reference_numbers else None
        )
        self.reference_id = reference_id
        self.hotel_id = hotel_id
        self.only_manual_debits = only_manual_debits
        self.from_date = from_date
        self.to_date = to_date
        self.auto_settled_via_credit = auto_settled_via_credit
        self.debit_ids = debit_ids
        self.debtor_ids = debtor_ids
        self.only_over_due_invoice = only_over_due_invoice
        self.only_non_over_due_invoice = only_non_over_due_invoice
        self.include_cancelled = include_cancelled
        self.settlement_status = settlement_status
        self.settlement_status = settlement_status
        self.over_due_date_filter = over_due_date_filter
        self.sort_order = sort_order
        self.limit = limit
        self.offset = offset


class OverDueDateFilter:
    def __init__(self, from_date=None, to_date=None):
        self.from_date = from_date
        self.to_date = to_date
