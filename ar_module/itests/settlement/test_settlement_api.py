import json

from ar_module.application.consumers.constants import DebtorTypes
from ar_module.integration_tests.mockers import (
    mock_applicable_roles_for_payment_mode,
    mock_get_roles,
    mock_get_tenant_configs,
)
from ar_module.itests.api_wrappers.credit_wrapper import create_credit
from ar_module.itests.api_wrappers.debit_wrapper import create_debit
from ar_module.itests.api_wrappers.debtor_wrapper import create_debtor
from ar_module.itests.payload_generators.settlement_payload_generator import (
    create_new_settlements_payload,
)


def test_create_settlements_in_existing_credit(
    client_,
    create_debtor_payload,
    create_debit_payload,
    create_credit_payload_with_settlements,
    config_value=True,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    credit_id = credit_response["data"]["credit"]["credit_id"]
    pay_mode = credit_response["data"]["credit"]["mode_of_credit"]
    assert credit_id is not None
    new_debit_ids = [
        create_debit(client_, create_debit_payload, debtor_id) for i in range(3)
    ]
    payload = json.dumps({"data": create_new_settlements_payload(new_debit_ids)})

    url = f"/ar/v1/credits/{credit_id}/settlements"
    with mock_get_tenant_configs(config_value):
        with mock_get_roles(), mock_applicable_roles_for_payment_mode(pay_mode):
            response = client_.post(
                url,
                data=payload,
                content_type="application/json",
                headers={"X-User-Type": "super-admin"},
            )

    settlement_data = json.loads(response.data.decode("utf-8"))
    assert response.status_code == 200
    assert len(settlement_data["data"]["settlements"]) == 3


def test_get_settlements_using_multiple_query_parameters(
    client_,
    create_debtor_payload,
    create_debit_payload,
    create_credit_payload_with_settlements,
    config_value=True,
):
    debtor_id = create_debtor(
        client_, create_debtor_payload, debtor_type=DebtorTypes.B2C
    )
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    reference_number = credit_response["data"]["credit"]["reference_number"]

    url = f"/ar/v1/settlements?payment_reference_number={reference_number}"
    with mock_get_roles(), mock_get_tenant_configs(config_value):
        response = client_.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 200
    settlement_data = json.loads(response.data.decode("utf-8"))
    assert len(settlement_data["data"]["settlements"]) == 2
    assert (
        settlement_data["data"]["settlements"][0]["payment_reference_number"]
        == reference_number
    )

    credit_id = credit_response["data"]["credit"]["credit_id"]
    url = f"/ar/v1/settlements?credit_id={credit_id}"
    with mock_get_roles(), mock_get_tenant_configs(config_value):
        response = client_.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    assert response.status_code == 200
    settlement_data = json.loads(response.data.decode("utf-8"))
    assert len(settlement_data["data"]["settlements"]) > 0
    assert settlement_data["data"]["settlements"][0]["credit_id"] == credit_id


def test_get_settlements_using_debit_id(
    client_,
    create_debtor_payload,
    create_debit_payload,
    create_credit_payload_with_settlements,
    config_value=True,
):
    debtor_id = create_debtor(client_, create_debtor_payload)
    debit_id = create_debit(client_, create_debit_payload, debtor_id)
    credit_response = create_credit(
        client_, create_credit_payload_with_settlements, debtor_id, debit_id
    )
    credit_id = credit_response["data"]["credit"]["credit_id"]
    assert credit_id is not None

    url = f"/ar/v1/debits/{debit_id}/settlements"
    with mock_get_roles(), mock_get_tenant_configs(config_value):
        response = client_.get(
            url,
            content_type="application/json",
            headers={"X-User-Type": "super-admin"},
        )
    settlement_data = json.loads(response.data.decode("utf-8"))
    assert response.status_code == 200
    assert len(settlement_data["data"]["settlements"]) > 0
