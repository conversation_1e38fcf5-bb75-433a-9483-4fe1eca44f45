import pytest

from ar_module.integration_tests.config.common_config import (
    CREATE_DEBTOR,
    CREATE_MULTIPLE_DEBTOR,
    ERROR_CODES,
    FDM,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.debtor.validations.validation_get_debtor import (
    ValidationGetDebtor,
)


class TestGetDebtor(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, user_type, skip_message, query_param",
        [
            (
                "GetDebtor_01",
                CREATE_DEBTOR,
                "fetch debtor using debtor_code",
                200,
                SUPER_ADMIN,
                None,
                "debtor_code=Debtor1",
            ),
            (
                "GetDebtor_02",
                CREATE_DEBTOR,
                "fetch debtor using debtor_name",
                200,
                SUPER_ADMIN,
                None,
                "debtor_name=Debtor-1",
            ),
            (
                "GetDebtor_03",
                CREATE_DEBTOR,
                "fetch debtor using debtor_id",
                200,
                SUPER_ADMIN,
                None,
                "debtor_ids=",
            ),
            (
                "GetDebtor_04",
                CREATE_MULTIPLE_DEBTOR,
                "fetch multiple debtor using debtor_ids",
                200,
                SUPER_ADMIN,
                None,
                "debtor_ids=",
            ),
            (
                "GetDebtor_05",
                CREATE_DEBTOR,
                "fetch debtor as an FDM",
                403,
                FDM,
                None,
                "debtor_code=Debtor1",
            ),
            (
                "GetDebtor_06",
                CREATE_MULTIPLE_DEBTOR,
                "fetch debtor using different debtor_name and debtor_code",
                200,
                SUPER_ADMIN,
                None,
                "debtor_code=Debtor1&debtor_name=Debtor-2",
            ),
        ],
    )
    @pytest.mark.regression
    def test_get_debtor(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        user_type,
        skip_message,
        query_param,
    ):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[0]
        self.debtor_request.debtor_ids.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        if test_case_id in ("GetDebtor_03", "GetDebtor_04"):
            query_param += ",".join(self.debtor_request.debtor_ids)

        response = self.debtor_request.get_debtors_request(
            client_,
            status_code,
            hotel_id,
            user_type,
            config_value=False,
            query_param=query_param,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(client_, response, test_case_id, self.debtor_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client_, response, test_case_id, debtor_request):
        validation = ValidationGetDebtor(
            client_, response, test_case_id, debtor_request
        )
        validation.validate_response()
