import csv
import json
import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import Tenant<PERSON>lient
from treebo_commons.request_tracing.context import request_context

from ar_module.application.consumers.constants import DebtorTypes, POCsDesignation
from ar_module.application.dtos.debtor_dto import DebtorD<PERSON>
from ar_module.application.services.ar_application_service import (
    AccountReceivableApplicationService,
)
from ar_module.common.slack_alert_helper import Slack<PERSON>lert
from ar_module.domain.constants import DEFAULT_CREDIT_SETTINGS
from ar_module.infrastructure.external_clients.authentication.authn_service_client import (
    AuthNClient,
)
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("create_users_debtors_in_bulk")
@click.option(
    "--file_path",
    help="csv file with user debtors data",
    default=None,
)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    accounts_receivable_service=AccountReceivableApplicationService,
    authn_client=AuthNClient,
)
def create_users_debtors_in_bulk(
    accounts_receivable_service, authn_client, file_path, tenant_id
):
    request_context.tenant_id = tenant_id
    failed_for_debtors_with_reason = dict()
    with open(file_path, "r") as csvfile:
        next(csvfile)
        file_data = csv.reader(csvfile, delimiter=",")
        for row_data in file_data:
            try:
                poc_user = _get_poc_user_from_csv_data(
                    row_data, authn_client, failed_for_debtors_with_reason, tenant_id
                )
            except Exception as e:
                msg = "failed to form poc_user for debtor: {0} due to exception: {1}".format(
                    row_data[0], str(e)
                )
                failed_for_debtors_with_reason[row_data[0]] = msg
                continue
            if not poc_user:
                continue
            try:
                debtor_dto = _create_debtor_from_csv_data(row_data)
            except Exception as e:
                msg = "failed to form debtor DTO for debtor: {0} due to exception: {1}".format(
                    row_data[0], str(e)
                )
                failed_for_debtors_with_reason[row_data[0]] = msg
                continue
            try:
                accounts_receivable_service.create_update_user_debtor_info_using_profile_data(
                    poc_user, debtor_dto
                )
            except Exception as e:
                msg = "failed to migrate user debtor details for debtor: {0} due to exception: {1}".format(
                    row_data[0], str(e)
                )
                failed_for_debtors_with_reason[row_data[0]] = msg
        print(failed_for_debtors_with_reason)
        SlackAlert.send_alert(
            "Users Debtors Migration Failures: {0}".format(
                failed_for_debtors_with_reason
            ),
            tenant_id=tenant_id,
        )


def _create_debtor_from_csv_data(csv_row):
    debtor_code = csv_row[0]
    debtor_name = csv_row[1]
    credit_limit, credit_period, btc_enabled = (
        DEFAULT_CREDIT_SETTINGS["credit_limit"],
        DEFAULT_CREDIT_SETTINGS["credit_period"],
        False,
    )
    credit_settings = (
        json.loads(csv_row[2]) if csv_row[2] and csv_row[2] != "null" else {}
    )
    if credit_settings:
        credit_limit = credit_settings.get("credit_amount") or credit_limit
        credit_period = credit_settings.get("credit_period") or credit_period
        btc_enabled = credit_settings.get("btc_enabled") or btc_enabled

    return DebtorDTO(
        debtor_code=debtor_code,
        debtor_name=debtor_name,
        credit_limit=credit_limit,
        credit_period=credit_period,
        btc_enabled=btc_enabled,
        debtor_type=DebtorTypes.B2B,
    )


def _get_poc_user_from_csv_data(
    csv_row, authn_client, failed_for_debtors_with_reason, tenant_id
):
    pocs = dict()
    point_of_contacts = (
        json.loads(csv_row[3]) if csv_row[3] and csv_row[3] != "null" else {}
    )
    if point_of_contacts:
        for poc in point_of_contacts:
            pocs[poc["designation"]] = _make_poc(poc)
    finance_poc = pocs.get(POCsDesignation.FINANCE_POC)

    if not finance_poc or not finance_poc.get("email"):
        msg = "Finance poc info is incomplete for debtor: {0}".format(csv_row[0])
        failed_for_debtors_with_reason[csv_row[0]] = msg
        return None

    poc_email = finance_poc.get("email")
    try:
        poc_user = authn_client.get_user_by_email(poc_email)
    except Exception:
        msg = "No user found for email: {0}".format(poc_email)
        failed_for_debtors_with_reason[csv_row[0]] = msg
        return None
    return poc_user


def _make_poc(data):
    return dict(
        first_name=data.get("name"),
        phone_number=data["phone_number"].get("number")
        if data.get("phone_number")
        else "",
        email=data["email_ids"][0] if data.get("email_ids") else None,
        is_active=True,
        auth_id=None,
    )
