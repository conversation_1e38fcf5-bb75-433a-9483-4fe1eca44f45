from marshmallow import Schema, fields, post_load
from treebo_commons.money import Money
from treebo_commons.money.money_field import Money<PERSON>ield

from ar_module.api.schemas import validate_positive_integer
from ar_module.application.dtos.credit_dto import (
    CreditReversalCancellationDto,
    CreditReversalDetailsDto,
    CreditReversalDto,
)
from ar_module.common.schema_mixins import StripWhitespaceMixin
from ar_module.domain.constants import CreditType


class CreditReversalUploadSchema(Schema, StripWhitespaceMixin):
    debtor_code = fields.String(required=True)
    refund_mode = fields.String(required=True)
    refund_id = fields.String(required=True)
    refund_date = fields.Date(required=True, format="%Y-%m-%d")
    refund_amount = MoneyField(required=True, validate=validate_positive_integer)
    credit_id = fields.String(required=True, allow_none=False)

    @post_load
    def transform(self, data):
        refund_amount = data["refund_amount"]
        refund_amount = Money(
            refund_amount.amount,
            refund_amount.currency or self.context.get("base_currency"),
        )
        return CreditReversalDto(
            debtor_id=None,
            debtor_code=data["debtor_code"],
            credit_type=CreditType.CREDIT_REVERSAL,
            date=data["refund_date"],
            mode_of_credit=data["refund_mode"],
            reference_number=data["refund_id"],
            amount_in_credit_currency=refund_amount,
            amount_in_base_currency=refund_amount,
            credit_reversals=[
                CreditReversalDetailsDto(
                    amount_in_credit_currency=refund_amount,
                    amount_in_base_currency=refund_amount,
                    payment_credit_id=data["credit_id"],
                )
            ],
        )


class CreditReversalCancellationUploadSchema(Schema, StripWhitespaceMixin):
    credit_id = fields.String(required=True, allow_none=False)

    @post_load
    def transform(self, data):
        return CreditReversalCancellationDto(
            credit_id=data["credit_id"],
        )
