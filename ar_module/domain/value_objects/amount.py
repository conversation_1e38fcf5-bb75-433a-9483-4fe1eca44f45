from treebo_commons.money import Money
from treebo_commons.money.exceptions import CurrencyMismatchError


class Amount(object):
    def __init__(self, pretax_amount: Money, tax_amount: Money, posttax_amount: Money):
        self.pretax_amount = pretax_amount
        self.tax_amount = tax_amount
        self.posttax_amount = posttax_amount

    def __add__(self, other):
        assert isinstance(other, Amount), "Can only add Amount types"
        if self.pretax_amount.currency == other.pretax_amount.currency:
            return Amount(
                self.pretax_amount + other.pretax_amount,
                self.posttax_amount + other.posttax_amount,
                self.tax_amount + other.tax_amount,
            )
        else:
            raise CurrencyMismatchError("Adding two different currencies")
