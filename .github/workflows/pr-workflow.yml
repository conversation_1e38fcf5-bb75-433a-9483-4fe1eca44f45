name: release/main PR Workflow

on:
  push:
    branches: [release, main]
  pull_request:
    branches: [release, main]

jobs:
  build:
    runs-on: ubuntu-22.04

    services:
      postgres:
        image: postgres
        env:
          POSTGRES_DB: ar_module_test
          POSTGRES_USER: ar_module_test_user
          POSTGRES_PASSWORD: ar_module_test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v2
    - uses: webfactory/ssh-agent@v0.5.4
      with:
        ssh-private-key: |
          ${{ secrets.SSH_TREEBO_COMMONS_PRIVATE_KEY }}
          ${{ secrets.SSH_FLASKHEALTHCHECK_PRIVATE_KEY }}
          ${{ secrets.SSH_THSC_PRIVATE_KEY }}

    - name: Install Python 3.6.15 using pyenv
      run: |
        sudo apt update
        sudo apt install -y build-essential libssl-dev zlib1g-dev \
          libbz2-dev libreadline-dev libsqlite3-dev curl libncursesw5-dev \
          xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev git

        curl https://pyenv.run | bash

        source .github/scripts/pyenv-bootstrap.sh
        eval "$(pyenv virtualenv-init -)"

        pyenv install 3.6.15
        pyenv global 3.6.15

        python --version
        pip install --upgrade pip==18.1

    - name: Install dependencies
      run: |
        source .github/scripts/pyenv-bootstrap.sh
        cat << EOF > requirements/deploy.txt
        git+ssh://**************/treebo-noss/flaskhealthcheck.git@main#egg=flaskhealthcheck
        git+ssh://**************/treebo-noss/treebo-common.git@main#egg=treebo-commons
        git+ssh://**************/treebo-noss/prometheus.git@main#egg=thsc
        -r base.txt
        EOF
        pip install -r requirements/dev.txt

    - name: Black formatter check
      run: |
        source .github/scripts/pyenv-bootstrap.sh
        black ar_module --check

    - name: Isort check
      run: |
        source .github/scripts/pyenv-bootstrap.sh
        isort ar_module --profile=black --check

    - name: ITests with Pytest
      run: |
        source .github/scripts/pyenv-bootstrap.sh
        pytest -x ar_module/itests

    - name: Integration Tests with Pytest
      run: |
        source .github/scripts/pyenv-bootstrap.sh
        pytest -x ar_module/integration_tests/tests
