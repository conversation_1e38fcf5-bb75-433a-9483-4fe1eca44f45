from ar_module.common.tenant_utils import get_tenant_id


class B2CDebtorCode:
    def __init__(self, booking_reference_id: str, tenant_id: str, customer_id: str):
        self.booking_reference_id = booking_reference_id
        self.tenant_id = tenant_id
        self.customer_id = customer_id

    def _encode(self):
        return "{0}/{1}-{2}".format(
            self.booking_reference_id,
            self.tenant_id,
            self.customer_id,
        )

    @staticmethod
    def _decode(str_repr):
        booking_id_tenant_id, customer_id = str_repr.strip("-").rsplit("-", 1)
        booking_id_tenant_id_parts = booking_id_tenant_id.split("/")
        booking_id = booking_id_tenant_id_parts[0]
        tenant_id = (
            booking_id_tenant_id_parts[1]
            if len(booking_id_tenant_id_parts) > 1
            else get_tenant_id()
        )
        return B2CDebtorCode(booking_id, tenant_id, customer_id)

    @property
    def value(self):
        return self._encode()

    @classmethod
    def decode_from_str_repr(cls, str_repr: str):
        return cls._decode(str_repr)
