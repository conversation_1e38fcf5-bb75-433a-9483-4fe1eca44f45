version: "2"

services:
  accounts_receivable_crs_consumer:
    image: "docker-hub.treebo.com:5000/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/ar_module/"
    container_name: "ar_module_crs_consumer_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: ["/usr/src/app/ar_module/workers/crs_consumer_worker.sh", "${TENANT_ID}"]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}

  accounts_receivable_company_profile_consumer:
    image: "docker-hub.treebo.com:5000/${DOCKER_TAGNAME}:${VERSION}"
    env_file: "${ENV_FILE}"
    volumes:
      - "${HOST_LOG_ROOT}:/var/log/ar_module/"
    container_name: "ar_module_company_profile_consumer_${TENANT_ID}"
    restart: always
    hostname: "${HOSTNAME}"
    entrypoint: [ "/usr/src/app/ar_module/workers/company_profile_consumer_worker.sh", "${TENANT_ID}" ]
    tty: true
    logging:
      driver: "json-file"
      options:
        max-size: "200k"
        max-file: "10"
    environment:
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}