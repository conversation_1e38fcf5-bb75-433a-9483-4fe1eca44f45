from treebo_commons.money import Money

from ar_module.application.dtos.credit_dto import CreditDto
from ar_module.application.dtos.debit_dto import DebitDto
from ar_module.domain.constants import CreditType, DebitType, Modules
from ar_module.domain.value_objects.amount import Amount


class CommonAttributesForIngestion:
    def __init__(self, payment_mode, ta_ref_id):
        self.payment_mode = payment_mode
        self.ta_ref_id = ta_ref_id


class DebitDtoForBulkIngestion(DebitDto, CommonAttributesForIngestion):
    def __init__(
        self,
        debtor_id,
        debit_type,
        debit_date,
        due_date,
        debit_amount,
        reference_number,
        reference_id,
        payment_mode,
        ta_ref_id,
        tenant_id=None,
    ):
        DebitDto.__init__(
            self,
            debtor_id,
            debit_type,
            debit_date,
            due_date,
            debit_amount,
            reference_number,
            reference_id,
            Modules.CRS_MODULE,
            tenant_id=tenant_id,
        )
        CommonAttributesForIngestion.__init__(self, payment_mode, ta_ref_id)

    @staticmethod
    def create_from_financial_data(data, base_currency, tenant_id=None):
        amount = Money(data.get("payment_amount"), base_currency)
        tax_amount = Money(0, base_currency)
        amount = Amount(
            pretax_amount=amount,
            tax_amount=tax_amount,
            posttax_amount=amount,
        )
        return DebitDtoForBulkIngestion(
            debtor_id=None,
            debit_type=data.get("payment_type") or DebitType.PAYMENT,
            debit_date=data.get("posted_date"),
            due_date=None,
            reference_id=data.get("reference_id"),
            debit_amount=amount,
            reference_number=data.get("payment_reference_number"),
            ta_ref_id=data.get("ta_ref_id"),
            payment_mode=data.get("payment_mode"),
            tenant_id=tenant_id,
        )


class CreditDtoForBulkIngestion(CreditDto, CommonAttributesForIngestion):
    def __init__(
        self,
        debtor_id,
        credit_type,
        date,
        amount_in_base_currency: Money,
        reference_number,
        amount_in_credit_currency: Money,
        reference_id=None,
        tenant_id=None,
        payment_mode=None,
        ta_ref_id=None,
    ):
        CreditDto.__init__(
            self,
            debtor_id,
            credit_type,
            date,
            amount_in_base_currency,
            reference_number,
            amount_in_credit_currency,
            [],
            reference_id,
            payment_mode,
            Modules.CRS_MODULE,
            tenant_id=tenant_id,
        )
        CommonAttributesForIngestion.__init__(self, payment_mode, ta_ref_id)

    @staticmethod
    def create_from_financial_data(data, base_currency, tenant_id=None):
        amount = Money(data.get("payment_amount"), base_currency)
        return CreditDtoForBulkIngestion(
            debtor_id=None,
            credit_type=data.get("payment_type") or CreditType.PAYMENT,
            date=data.get("posted_date"),
            amount_in_base_currency=amount,
            reference_number=data.get("payment_reference_number"),
            reference_id=data.get("reference_id"),
            tenant_id=tenant_id,
            payment_mode=data.get("payment_mode"),
            amount_in_credit_currency=amount,
            ta_ref_id=data.get("ta_ref_id"),
        )
