from typing import List


class SettlementDto(object):
    def __init__(self, debit_id, reference_number, debit_date, applied_amount):
        self.debit_id = debit_id
        self.reference_number = reference_number
        self.debit_date = debit_date
        self.applied_amount = applied_amount

    def to_json(self):
        return dict(
            debit_id=self.debit_id,
            reference_number=self.reference_number,
            debit_date=self.debit_date,
            applied_amount=self.applied_amount,
        )


class SettlementsDto(object):
    def __init__(self, settlements: List[SettlementDto]):
        self.settlements = settlements

    def as_json(self):
        return [
            {
                "debit_id": settlement.debit_id,
                "reference_number": settlement.reference_number,
                "debit_date": str(settlement.debit_date),
                "applied_amount": str(settlement.applied_amount),
            }
            for settlement in self.settlements
        ]
