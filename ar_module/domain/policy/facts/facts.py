from treebo_commons.utils import dateutils


class Facts(object):
    def __init__(
        self,
        user_data=None,
        user_type=None,
        action_payload=None,
        current_time=None,
        **aggregates,
    ):
        self.current_time = (
            current_time if current_time else dateutils.current_datetime()
        )
        self.user_data = user_data
        self.user_type = user_type
        self.action_payload = action_payload
        self.aggregates = aggregates
