from typing import List


class SearchCollectionReportDto(object):
    def __init__(
        self,
        start_date,
        end_date,
        include_detail,
        hotel_id,
        debtor_code,
        min_paid_amount,
        max_paid_amount,
        debtor_type,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.include_detail = include_detail
        self.hotel_id = hotel_id
        self.debtor_code = debtor_code
        self.debtor_type = debtor_type
        self.min_paid_amount = min_paid_amount
        self.max_paid_amount = max_paid_amount


class SearchLedgerReportDto(object):
    def __init__(
        self,
        start_date,
        end_date,
        include_detail,
        hotel_id,
        debtor_code,
        min_amount,
        max_amount,
        settlement_status,
        settlement_date,
        entry_types,
        debtor_type,
        generate_download_urls,
        limit,
        offset,
        debtor_id,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.include_detail = include_detail
        self.hotel_id = hotel_id
        self.debtor_code = debtor_code
        self.debtor_type = debtor_type
        self.min_amount = min_amount
        self.max_amount = max_amount
        self.settlement_status = settlement_status
        self.settlement_date = settlement_date
        self.entry_types = entry_types
        self.generate_download_urls = generate_download_urls
        self.limit = limit
        self.offset = offset
        self.debtor_id = debtor_id


class DateRangeDto(object):
    def __init__(self, start_date, end_date):
        self.start_date = start_date
        self.end_date = end_date


class SearchMultipleDateRangeCollectionReportSchema(object):
    def __init__(self, hotel_id, date_ranges: List[DateRangeDto]):
        self.hotel_id = hotel_id
        self.date_ranges = date_ranges


class SearchReceivableReportDto(object):
    def __init__(
        self,
        start_date,
        end_date,
        include_detail,
        hotel_id,
        reference_date,
        debtor_code,
        min_balance,
        max_balance,
        min_oldest_due_age,
        max_oldest_due_age,
        debtor_type,
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.include_detail = include_detail
        self.hotel_id = hotel_id
        self.debtor_code = debtor_code
        self.debtor_type = debtor_type
        self.reference_date = reference_date
        self.min_balance = min_balance
        self.max_balance = max_balance
        self.min_oldest_due_age = min_oldest_due_age
        self.max_oldest_due_age = max_oldest_due_age
