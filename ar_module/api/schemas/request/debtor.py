from flask import request
from marshmallow import Schema, ValidationError, fields, validates_schema
from marshmallow.validate import OneOf

from ar_module.application.consumers.constants import DebtorTypes
from ar_module.domain.constants import SortOrders


class DebtorSearchSchema(Schema):
    debtor_code = fields.String(
        description="Exact debtor_code to search with. If none of the debtors have debtor_code matching exactly this "
        "value(case-sensitive), then result will be empty. Note: Trailing whitespaces are trimmed"
    )
    debtor_name = fields.String(
        description="Returns list of debtors with their name starting with this value. The value is used in "
        "case-insensitive manner. Trailing whitespaces are trimmed"
    )
    query = fields.String(
        description="Free text search string, which will be searched against following attributes in "
        "debtor: \n"
        " - Debtor Code: External booking reference number\n"
        " - Debtor Name\n"
    )
    hotel_id = fields.String()
    debtor_type = fields.String(
        description="Debtor type (b2b/b2c)",
        validate=OneOf([DebtorTypes.B2B, DebtorTypes.B2C]),
    )
    limit = fields.Integer(description="Number of results required", allow_none=True)
    offset = fields.Integer(
        description="Offset from which results are required", allow_none=True
    )
    debtor_ids = fields.String(
        description="Comma separated list of debtor ids to be searched", allow_none=True
    )
    only_fields = fields.String(
        description="Comma separated list of fields to be returned in response",
        allow_none=True,
    )
    sort_order = fields.String(
        validate=OneOf(
            SortOrders.all(),
            error="Sort Order is not valid. Allowed values are 'asc' and 'desc'",
        ),
        description="Sort order for the results. Allowed values are 'asc' and 'desc'",
        allow_none=True,
    )

    @validates_schema
    def validate_request_data(self, data):
        if (
            request.headers.get("X-Hotel-Id")
            and data.get("hotel_id")
            and request.headers["X-Hotel-Id"] != data["hotel_id"]
        ):
            raise ValidationError("Hotel id is different than the one sent in headers")
        if data.get("debtor_ids") and data.get("debtor_code"):
            raise ValidationError(
                "Only one of debtor_code or debtor_ids can be provided"
            )
        if data.get("query") == "":
            raise ValidationError("Field value cannot be blank")


class DebtorRequestSchema(Schema):
    debtor_code = fields.String(required=True)
    debtor_name = fields.String(required=True)
    debtor_type = fields.String(
        description="Debtor type (b2b/b2c)",
        validate=OneOf([DebtorTypes.B2B, DebtorTypes.B2C]),
        missing=DebtorTypes.B2B,
    )
    hotel_id = fields.String(allow_none=True)
    credit_limit = fields.Number(allow_none=True)
    credit_period = fields.Number(allow_none=True)
    btc_enabled = fields.Number(allow_none=True)

    @validates_schema
    def validate_hotel_id(self, data):
        if (
            request.headers.get("X-Hotel-Id")
            and data.get("hotel_id")
            and request.headers["X-Hotel-Id"] != data["hotel_id"]
        ):
            raise ValidationError("Hotel id is different than the one sent in headers")
