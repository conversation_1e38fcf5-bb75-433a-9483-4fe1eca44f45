from marshmallow import Schema, fields, pre_dump

from ar_module.domain.aggregates.debtor_aggregate import DebtorAggregate


class ProfileDetailsSchema(Schema):
    name = fields.String()
    email_ids = fields.List(fields.String())
    designation = fields.String()


class AddressSchema(Schema):
    state = fields.String()
    country = fields.String()
    pincode = fields.String()
    city = fields.String()
    address_line_1 = fields.String()
    address_line_2 = fields.String()


class DebtorResponseSchema(Schema):
    debtor_id = fields.String()
    debtor_code = fields.String()
    debtor_name = fields.String()
    user_profile_id = fields.String()
    debtor_type = fields.String()
    hotel_id = fields.String()
    profile_status = fields.String()
    credit_period = fields.String()
    credit_limit = fields.String()
    pocs = fields.Nested(ProfileDetailsSchema, many=True, allow_none=True)
    registered_address = fields.Nested(AddressSchema, allow_none=True)


class DebtorSearchResponseSchema(Schema):
    debtors = fields.Nested(DebtorResponseSchema, many=True)
    limit = fields.Integer(description="Number of results")
    offset = fields.Integer(description="Offset from which the results are fetched")
    total = fields.Integer(description="Total number of debtors for the query")

    def dump(self, obj, *, only_fields=None, **kwargs):
        if only_fields:  # Check if only_fields are provided in the request
            self.fields["debtors"].only = [f.strip() for f in only_fields.split(",")]
        return super().dump(obj, **kwargs)
