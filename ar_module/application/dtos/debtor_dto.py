from treebo_commons.request_tracing.context import get_current_tenant_id

from ar_module.application.consumers.constants import DebtorTypes
from ar_module.domain.constants import DEFAULT_CREDIT_SETTINGS
from ar_module.domain.dtos.debtor import B2CDebtorCode


class DebtorDTO(object):
    def __init__(
        self,
        debtor_name,
        reference_id=None,
        customer_id=None,
        user_profile_id=None,
        hotel_id=None,
        debtor_code=None,
        debtor_type=None,
        credit_period=None,
        credit_limit=None,
        btc_enabled=None,
        profile_status=None,
        pocs=None,
        registered_address=None,
    ):
        self.reference_id = reference_id
        self.debtor_name = debtor_name
        self.customer_id = customer_id
        self.user_profile_id = user_profile_id
        self.hotel_id = hotel_id
        self.debtor_type = debtor_type
        self.debtor_code = debtor_code or self.reference_id
        self.credit_period = credit_period
        self.credit_limit = credit_limit
        self.btc_enabled = btc_enabled
        self.profile_status = profile_status
        self.pocs = pocs
        self.registered_address = registered_address

    def set_debtor_code(
        self, debtor_type: DebtorTypes, booking_booking_reference_number=None
    ):
        self.debtor_type = debtor_type
        if debtor_type == DebtorTypes.B2B:
            self.debtor_code = self.reference_id
        else:
            tenant_id = get_current_tenant_id()
            self.debtor_code = B2CDebtorCode(
                booking_booking_reference_number,
                tenant_id,
                self.customer_id,
            ).value

    @staticmethod
    def create_from_sh_profile_data(sh_profile_data):
        debtor_code = sh_profile_data.get("superhero_company_code")
        debtor_name = sh_profile_data.get("legal_entity_name") or sh_profile_data.get(
            "trade_name"
        )
        hotel_id = sh_profile_data.get("hotel_id")
        credit_limit, credit_period, btc_enabled = (
            DEFAULT_CREDIT_SETTINGS["credit_limit"],
            DEFAULT_CREDIT_SETTINGS["credit_period"],
            False,
        )
        folio_rules = sh_profile_data.get("folio_rules") or []
        for rule in folio_rules:
            btc_enabled = btc_enabled or rule.get("is_btc_allowed", False)
        if sh_profile_data.get("credit_settings"):
            credit_limit = (
                sh_profile_data["credit_settings"].get("credit_amount") or credit_limit
            )
            credit_period = (
                sh_profile_data["credit_settings"].get("credit_period") or credit_period
            )
        pocs = sh_profile_data.get("point_of_contacts")
        registered_address = sh_profile_data.get("registered_address")
        profile_status = sh_profile_data.get("status")

        return DebtorDTO(
            debtor_code=debtor_code,
            debtor_name=debtor_name,
            credit_limit=credit_limit,
            hotel_id=hotel_id,
            credit_period=credit_period,
            btc_enabled=btc_enabled,
            debtor_type=DebtorTypes.B2B,
            pocs=pocs,
            registered_address=registered_address,
            profile_status=profile_status,
        )
