from datetime import datetime

from marshmallow import Schema, ValidationError, fields, post_load, validates_schema
from treebo_commons.money import Money
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas import UserDefinedEnumValidator, validate_positive_integer
from ar_module.application.dtos.credit_dto import CreditCancellationDto, CreditDto
from ar_module.application.utils import validate_payment_mode
from ar_module.common.schema_mixins import StripWhitespaceMixin
from ar_module.domain.constants import ARModuleConfigs, CreditType


class CreditUploadSchema(Schema):
    debtor_code = fields.String(required=True)
    payment_date = fields.String()
    payment_amount = MoneyField(validate=validate_positive_integer, required=True)
    payment_ref_id = fields.String(required=True)
    payment_mode = fields.String()

    @post_load
    def create_object(self, data):
        amount = data.get("payment_amount")
        amount = Money(
            amount.amount, amount.currency or self.context.get("base_currency")
        )
        payment_date = data.get("payment_date")
        return CreditDto(
            debtor_id=None,
            debtor_code=data.get("debtor_code"),
            credit_type=CreditType.PAYMENT,
            date=datetime.strptime(payment_date, "%d-%m-%Y").date()
            if payment_date
            else None,
            amount_in_base_currency=amount,
            reference_number=data.get("payment_ref_id").strip()
            if data.get("payment_ref_id")
            else None,
            mode_of_credit=data.get("payment_mode"),
            amount_in_credit_currency=amount,
            settlements=[],
        )

    @validates_schema(pass_original=True)
    def validate_data(self, data, original_data):
        debtor_code = data.get("debtor_code")
        if not debtor_code:
            raise ValidationError("Debtor code is Mandatory")
        validate_payment_mode(data.get("payment_mode"), debtor_code)
        payment_ref_id = data.get("payment_ref_id", "").strip()
        if not payment_ref_id:
            raise ValidationError("Payment ref id is mandatory")
        payment_date = original_data.get("payment_date")
        if payment_date:
            try:
                datetime.strptime(payment_date, "%d-%m-%Y").date()
            except Exception:
                raise ValidationError("Invalid Payment date")
        return data


class CreditCancellationUploadSchema(Schema, StripWhitespaceMixin):
    credit_id = fields.String(required=True)
    cancellation_reason = fields.String(
        required=True,
        validate=[UserDefinedEnumValidator(ARModuleConfigs.PAYMENT_CANCELLATION_ENUM)],
    )

    @post_load
    def transform(self, data):
        return CreditCancellationDto(
            cancellation_reason=data["cancellation_reason"],
            credit_id=data["credit_id"],
        )
