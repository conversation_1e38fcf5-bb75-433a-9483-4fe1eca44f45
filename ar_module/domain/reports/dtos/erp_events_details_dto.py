class CreditDetails:
    def __init__(self, credit_details):
        self.date_of_payment = credit_details.credit_date
        self.amount = credit_details.amount
        self.payment_mode = credit_details.mode_of_credit
        self.payment_ref_id = credit_details.reference_number
        self.customer_id = credit_details.debtor_code

    def to_json(self):
        return dict(
            date_of_payment=self.date_of_payment,
            amount=self.amount,
            payment_mode=self.payment_mode,
            payment_ref_id=self.payment_ref_id,
            customer_id=self.customer_id,
        )


class FolioDetails:
    def __init__(self, customer_id, folio_name, folio_address, nature_of_supply):
        self.customer_id = customer_id
        self.folio_name = folio_name
        self.folio_address = folio_address
        self.nature_of_supply = nature_of_supply

    def to_json(self):
        return dict(
            customer_id=self.customer_id,
            folio_name=self.folio_name,
            folio_address=self.folio_address,
            nature_of_supply=self.nature_of_supply,
        )


class HotelDetails:
    def __init__(self, hotel_id, hotel_state):
        self.hotel_id = hotel_id
        self.hotel_state = hotel_state

    def to_json(self):
        return dict(hotel_id=self.hotel_id, hotel_state=self.hotel_state)


class ERPArData:
    def __init__(self, payments, folio_details, hotel_details):
        self.payments = payments
        self.folio_details = folio_details
        self.hotel_details = hotel_details

    def to_json(self):
        return dict(
            payments=self.payments,
            folio_details=self.folio_details,
            hotel_details=self.hotel_details,
        )
