from marshmallow import Schema, fields
from treebo_commons.money.money_field import MoneyField

from ar_module.api.schemas.validators import validate_positive_integer


class CreditReversalDetailsSchema(Schema):
    payment_credit_id = fields.String(required=True)
    amount_in_base_currency = MoneyField(validate=validate_positive_integer)
    amount_in_credit_currency = MoneyField(validate=validate_positive_integer)
    remarks = fields.String(allow_none=True)
