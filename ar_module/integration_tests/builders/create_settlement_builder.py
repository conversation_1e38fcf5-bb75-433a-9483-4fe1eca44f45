from ar_module.integration_tests.builders.common_builder import Settlement
from ar_module.integration_tests.config.sheet_names import SETTLEMENT_DATA_SHEET_NAME
from ar_module.integration_tests.utilities import excel_utils
from ar_module.integration_tests.utilities.common_utils import (
    sanitize_blank,
    sanitize_test_data,
)


class CreateSettlementBuilder:
    def __init__(self, sheet_name, test_case_id, debit_id):
        test_data = excel_utils.get_test_case_data(sheet_name, test_case_id)[0]
        self.data = []
        if sanitize_blank(test_data["settlements"]) is not None:
            total_settlements = test_data["settlements"].split(",")
            for settlement_index, settlements in enumerate(total_settlements):
                settlements_data = excel_utils.get_test_case_data(
                    SETTLEMENT_DATA_SHEET_NAME, settlements
                )[0]
                self.data.append(
                    sanitize_test_data(
                        Settlement(
                            settlements_data, debit_id[settlement_index]
                        ).__dict__
                    )
                )
