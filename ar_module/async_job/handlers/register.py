from ar_module.async_job.handlers.bulk_data_ingestors.credit_reversals.cancel_credit_reversal_upload_handler import (
    CreditReversalCancellationUploadHandler,
)
from ar_module.async_job.handlers.bulk_data_ingestors.credit_reversals.credit_reversals_handler import (
    CreditReversalUploadHandler,
)
from ar_module.async_job.handlers.bulk_data_ingestors.credits.cancel_credits_upload_handler import (
    CreditCancellationUploadHandler,
)
from ar_module.async_job.handlers.bulk_data_ingestors.credits.credits_upload_handler import (
    CreditUploadHandler,
)
from ar_module.async_job.handlers.bulk_data_ingestors.frontdesk_payments.financial_data_ingestion_handler import (
    FinancialDataIngestionHandler,
)
from ar_module.async_job.handlers.bulk_data_ingestors.settlements.settlement_upload_handler import (
    SettlementUploadHandler,
)
from ar_module.async_job.handlers.reports.debtor_summary_report_handler import (
    DebtorSummaryReportHandler,
)
from ar_module.async_job.handlers.reports.finance_erp_report_handler import (
    FinanceERPReportHandler,
)
from ar_module.async_job.job.job_constants import JobName
from object_registry import locate_instance


def register_job_handlers(job_registry):
    job_registry.register(
        JobName.BULK_UPLOAD_OF_SETTLEMENTS,
        locate_instance(SettlementUploadHandler).handle,
    )

    job_registry.register(
        JobName.BULK_UPLOAD_OF_CREDITS, locate_instance(CreditUploadHandler).handle
    )

    job_registry.register(
        JobName.DEBTOR_SUMMARY_REPORTS,
        locate_instance(DebtorSummaryReportHandler).handle,
    )

    job_registry.register(
        JobName.BULK_INGESTION_OF_FINANCIAL_DATA,
        locate_instance(FinancialDataIngestionHandler).handle,
    )

    job_registry.register(
        JobName.BULK_PUSH_OF_PAYMENTS,
        locate_instance(FinanceERPReportHandler).handle,
    )
    job_registry.register(
        JobName.BULK_UPLOAD_OF_CREDIT_REVERSALS,
        locate_instance(CreditReversalUploadHandler).handle,
    )

    job_registry.register(
        JobName.BULK_UPLOAD_OF_CREDIT_CANCELLATION,
        locate_instance(CreditCancellationUploadHandler).handle,
    )
    job_registry.register(
        JobName.BULK_UPLOAD_OF_CREDIT_REVERSAL_CANCELLATION,
        locate_instance(CreditReversalCancellationUploadHandler).handle,
    )
