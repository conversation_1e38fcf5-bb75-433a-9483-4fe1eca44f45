def create_new_debtor_payload(
    debtor_type, debtor_name="AR_Module_test_12", debtor_code="testDTC", hotel_id=None
):
    payload = {
        "btc_enabled": 0,
        "credit_limit": 0,
        "credit_period": 0,
        "debtor_code": debtor_code,
        "debtor_name": debtor_name,
        "debtor_type": debtor_type,
    }
    if hotel_id:
        payload["hotel_id"] = hotel_id
    return payload
