from typing import List

import requests
from treebo_commons.money.constants import CurrencyType
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request
from treebo_commons.utils.config_value_parser import <PERSON><PERSON>anParser

from ar_module.application.hotel_settings.dtos.tenant_config_dto import TenantConfigDto
from ar_module.infrastructure.external_clients.service_registry_client import (
    ServiceRegistryClient,
)
from object_registry import register_instance


@register_instance()
class CatalogServiceClient(object):
    def __init__(self):
        self.cs_base_url = ServiceRegistryClient.get_catalog_service_url()

    @staticmethod
    def get_headers():
        headers = {"content-type": "application/json"}
        enrich_outgoing_request(headers)
        return headers

    def get_tenant_configs(self, hotel_id=None) -> List[TenantConfigDto]:
        url = self.cs_base_url + "/cataloging-service/api/v1/tenant-configs"
        params = {"property_id": hotel_id} if hotel_id else None
        response = requests.get(url=url, params=params, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        return [
            TenantConfigDto(
                config.get("config_name"),
                config.get("config_value"),
                config.get("value_type"),
            )
            for config in response.json()
        ]

    def is_hotel_level_accounts_receivable_configured(self):
        config_name_to_config_mapping = self.get_ar_config()
        ar_module_config = config_name_to_config_mapping.get(
            "ar_module.hotel_level_accounts_receivable"
        )
        return BooleanParser().parse(ar_module_config.get("config_value"))

    def get_all_payment_modes(self):
        url = self.cs_base_url + "/cataloging-service/api/v1/enums"
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        config_name_to_config_mapping = {
            enum["enum_name"]: enum for enum in response.json()
        }
        payment_modes = config_name_to_config_mapping.get("payment_mode")
        return [enum_value["value"] for enum_value in payment_modes["enum_values"]]

    def get_hotel_base_currency(self, hotel_id):
        url = (
            self.cs_base_url
            + "/cataloging-service/api/v1/properties/{hotel_id}".format(
                hotel_id=hotel_id
            )
        )
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        return CurrencyType(response.json().get("base_currency_code"))

    def get_ar_config(self, hotel_id=None):
        url = self.cs_base_url + "/cataloging-service/api/v1/tenant-configs"
        params = {"property_id": hotel_id} if hotel_id else None
        response = requests.get(url=url, params=params, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        config_name_to_config_mapping = {
            config["config_name"]: config for config in response.json()
        }
        return config_name_to_config_mapping

    def fetch_property(self, hotel_id):
        url = (
            self.cs_base_url
            + "/cataloging-service/api/v1/properties/{hotel_id}".format(
                hotel_id=hotel_id
            )
        )
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        return response.json()

    def is_cashiering_enabled(self, hotel_id):
        config_name_to_config_mapping = self.get_ar_config(hotel_id)
        cashiering_enabled_config = config_name_to_config_mapping.get(
            "cashiering_enabled"
        )
        if cashiering_enabled_config is None:
            return False
        return BooleanParser().parse(cashiering_enabled_config.get("config_value"))

    def get_hotel_state(self, hotel_id):
        url = (
            self.cs_base_url
            + "/cataloging-service/api/v1/properties/{hotel_id}".format(
                hotel_id=hotel_id
            )
        )
        response = requests.get(url=url, headers=self.get_headers())
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        return response.json().get("location").get("state").get("name")

    def get_enums(self, enum_names):
        url = self.cs_base_url + "/cataloging-service/api/v1/enums"
        url_params = dict()
        if enum_names:
            if isinstance(enum_names, list):
                enum_names = ",".join(enum_names)
            url_params["enum_names"] = enum_names
        response = requests.get(url=url, params=url_params)
        if not response.status_code == 200:
            raise Exception(
                "Catalog API Error. Status Code: {0}".format(response.status_code)
            )
        response_json = response.json()
        return response_json
