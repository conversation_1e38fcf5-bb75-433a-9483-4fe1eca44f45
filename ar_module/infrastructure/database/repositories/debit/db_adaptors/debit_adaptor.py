from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

from ar_module.domain.entities.debit import Debit
from ar_module.domain.value_objects.amount import Amount
from ar_module.domain.value_objects.invoice_meta import InvoiceMeta
from ar_module.infrastructure.database.base_db_to_domain_entity_adaptor import (
    BaseAdaptor,
)
from ar_module.infrastructure.database.models.debit_model import DebitModel


class DebitAdaptor(BaseAdaptor):
    def to_db_entity(self, domain_entity: Debit, **kwargs):
        # noinspection PyArgumentList
        return DebitModel(
            debit_id=domain_entity.debit_id,
            debtor_id=domain_entity.debtor_id,
            debit_date=domain_entity.debit_date,
            pretax_amount=domain_entity.debit_amount.pretax_amount.amount,
            tax_amount=domain_entity.debit_amount.tax_amount.amount,
            posttax_amount=domain_entity.debit_amount.posttax_amount.amount,
            currency=domain_entity.debit_amount.posttax_amount.currency.value,
            reference_number=domain_entity.reference_number,
            debit_type=domain_entity.debit_type,
            reference_id=domain_entity.reference_id,
            settlement_status=domain_entity.settlement_status,
            unsettled_amount=domain_entity.unsettled_amount.amount,
            debit_template_url=domain_entity.debit_template_url,
            due_date=domain_entity.due_date,
            remarks=domain_entity.remarks,
            auto_settled_via_credit=domain_entity.auto_settled_via_credit,
            booking_reference_number=domain_entity.booking_reference_number,
            hotel_id=domain_entity.hotel_id,
            bill_to_debtor_code=domain_entity.bill_to_debtor_code,
            recorded_via=domain_entity.recorded_via,
            invoice_meta=domain_entity.invoice_meta.serialize()
            if domain_entity.invoice_meta
            else None,
            tenant_id=domain_entity.tenant_id,
        )

    def to_domain_entity(self, db_entity, **kwargs):
        currency = (
            db_entity.currency
            if isinstance(db_entity.currency, CurrencyType)
            else CurrencyType(db_entity.currency)
        )
        debit_amount = Amount(
            pretax_amount=Money(db_entity.pretax_amount, currency),
            tax_amount=Money(db_entity.tax_amount, currency),
            posttax_amount=Money(db_entity.posttax_amount, currency),
        )
        return Debit(
            debit_id=db_entity.debit_id,
            debtor_id=db_entity.debtor_id,
            debit_date=db_entity.debit_date,
            debit_amount=debit_amount,
            reference_number=db_entity.reference_number,
            debit_type=db_entity.debit_type,
            reference_id=db_entity.reference_id,
            settlement_status=db_entity.settlement_status,
            unsettled_amount=Money(db_entity.unsettled_amount, currency),
            created_at=db_entity.created_at,
            debit_template_url=db_entity.debit_template_url,
            dirty=False,
            new=False,
            due_date=db_entity.due_date,
            remarks=db_entity.remarks,
            auto_settled_via_credit=db_entity.auto_settled_via_credit,
            booking_reference_number=db_entity.booking_reference_number,
            hotel_id=db_entity.hotel_id,
            bill_to_debtor_code=db_entity.bill_to_debtor_code,
            recorded_via=db_entity.recorded_via,
            invoice_meta=InvoiceMeta.load_from_dict(db_entity.invoice_meta)
            if db_entity.invoice_meta
            else None,
            tenant_id=db_entity.tenant_id,
        )
