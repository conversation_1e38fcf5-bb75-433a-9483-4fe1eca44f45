import pytest

from ar_module.integration_tests.config.common_config import (
    CREATE_DEBTOR,
    CREATE_SINGLE_DEBIT,
    ERROR_CODES,
    HOTEL_ID,
    SUCCESS_CODES,
    SUPER_ADMIN,
)
from ar_module.integration_tests.tests.base_test import BaseTest
from ar_module.integration_tests.tests.debit.validations.validation_create_debit import (
    ValidationCreateDebit,
)


class TestCreateDebit(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_message",
        [
            (
                "CreateDebit_01",
                CREATE_DEBTOR,
                "create debit for debtor with all valid entries",
                200,
                None,
            ),
            (
                "CreateDebit_02",
                CREATE_SINGLE_DEBIT,
                "create debit for reference number that already exists",
                400,
                "Not Handled",
            ),
            (
                "CreateDebit_03",
                CREATE_DEBTOR,
                "create debit without debit_date",
                400,
                None,
            ),
            (
                "CreateDebit_04",
                CREATE_DEBTOR,
                "create debit with debit_date less than current date",
                200,
                None,
            ),
            (
                "CreateDebit_05",
                CREATE_DEBTOR,
                "create debit with debit_date more than current date",
                400,
                None,
            ),
            (
                "CreateDebit_06",
                CREATE_DEBTOR,
                "create debit without reference number",
                400,
                None,
            ),
            (
                "CreateDebit_07",
                CREATE_DEBTOR,
                "create debit without debator_id",
                400,
                None,
            ),
            (
                "CreateDebit_08",
                CREATE_DEBTOR,
                "create debit without debit_amount",
                400,
                None,
            ),
            (
                "CreateDebit_09",
                CREATE_DEBTOR,
                "create debit without posttax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_10",
                CREATE_DEBTOR,
                "create debit without pretax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_11",
                CREATE_DEBTOR,
                "create debit without tax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_12",
                CREATE_DEBTOR,
                "create debit with negative posttax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_13",
                CREATE_DEBTOR,
                "create debit with negative pretax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_14",
                CREATE_DEBTOR,
                "create debit with negative tax_amount",
                400,
                "Not Handled",
            ),
            (
                "CreateDebit_15",
                CREATE_DEBTOR,
                "create debit with pretax_amount > posttax_amount",
                400,
                None,
            ),
            (
                "CreateDebit_16",
                CREATE_DEBTOR,
                "create debit with tax_amount > pretax_amount",
                200,
                None,
            ),
            (
                "CreateDebit_17",
                CREATE_DEBTOR,
                "create debit with invalid debator_id",
                403,
                None,
            ),
            (
                "CreateDebit_18",
                CREATE_SINGLE_DEBIT,
                "create multiple debit for same debtor_id",
                200,
                None,
            ),
            (
                "CreateDebit_19",
                CREATE_DEBTOR,
                "create debit with posttax_amount equal pretax amount",
                200,
                None,
            ),
            (
                "CreateDebit_20",
                CREATE_DEBTOR,
                "create a debit in which amounts are in diff currency",
                400,
                "Skip",
            ),
            (
                "CreateDebit_22",
                CREATE_DEBTOR,
                "create debit for debtor with all valid entries without hotel_id",
                200,
                None,
            ),
        ],
    )
    @pytest.mark.regression
    def test_create_debit(
        self,
        client_,
        test_case_id,
        previous_actions,
        tc_description,
        status_code,
        skip_message,
    ):
        if skip_message:
            pytest.skip(skip_message)

        hotel_id = HOTEL_ID[0] if test_case_id != "CreateDebit_22" else None

        self.debit_request.debit_id.clear()

        if previous_actions:
            self.common_request_caller(client_, previous_actions)

        response = self.debit_request.create_new_debit_request(
            client_,
            test_case_id,
            self.debtor_request.debtor_id,
            status_code,
            SUPER_ADMIN,
            hotel_id,
        )

        if status_code in ERROR_CODES:
            self.response_validation_negative_cases(response, test_case_id)
        elif status_code in SUCCESS_CODES:
            self.validation(response, test_case_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(response, test_case_id):
        validation = ValidationCreateDebit(test_case_id, response)
        validation.validate_response()
