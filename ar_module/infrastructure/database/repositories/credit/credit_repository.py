from datetime import time

from sqlalchemy import and_, func, or_
from ths_common.utils.common_utils import group_list
from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType
from treebo_commons.utils import dateutils

from ar_module.domain.aggregates.credit_aggregate import CreditAggregate
from ar_module.domain.constants import CreditStatus, CreditType, Modules, SortOrders
from ar_module.exceptions import AggregateNotFound
from ar_module.infrastructure.database.base_repository import BaseRepository
from ar_module.infrastructure.database.dtos.erp_events_detail_dtos import (
    ERPCreditDetailsDTO,
)
from ar_module.infrastructure.database.models.credit_model import CreditModel
from ar_module.infrastructure.database.models.debtor_model import DebtorModel
from ar_module.infrastructure.database.repositories.credit.db_adaptors.credit_adaptor import (
    CreditAdaptor,
)
from object_registry import register_instance


@register_instance()
class CreditRepository(BaseRepository):
    credit_adaptor = CreditAdaptor()

    def from_aggregate(self, aggregate: CreditAggregate = None):
        credit = aggregate.credit
        credit_model = self.credit_adaptor.to_db_entity(credit)
        return credit_model

    def save(self, aggregate: CreditAggregate):
        credit_model = self.from_aggregate(aggregate)
        self._save(credit_model)
        self.flush_session()

    def save_all(self, aggregates):
        credit_models = [self.from_aggregate(aggregate) for aggregate in aggregates]
        self._save_all(credit_models)
        self.flush_session()

    def update(self, aggregate: CreditAggregate):
        credit_model = self.from_aggregate(aggregate)
        self._update(credit_model)
        self.flush_session()

    def update_all(self, aggregates):
        credit_models = [self.from_aggregate(aggregate) for aggregate in aggregates]
        self._update_all(credit_models)
        self.flush_session()

    def exists(self, credit_type, reference_id):
        return bool(
            self.query(CreditModel)
            .filter(
                CreditModel.credit_type == credit_type,
                CreditModel.reference_id == reference_id,
                CreditModel.deleted.is_(False),
            )
            .first()
        )

    def get_credit_summary(
        self,
        debtor_ids,
        start_date=None,
        end_date=None,
        min_credit_amount=None,
        max_credit_amount=None,
        reference_date=None,
    ):
        """
        Returns Total Credits (CreditNote + Payment), Total Provisional Credits (TDS) and Total UnApplied Payments

        :param debtor_ids:
        :param reference_date:
        :param start_date:
        :param end_date:
        :return: total_credits, total_provisional_credits, unapplied_payment
        """
        credit_summary = (
            self.query(
                CreditModel.debtor_id,
                CreditModel.credit_type,
                CreditModel.base_currency,
                func.sum(CreditModel.amount_in_credit_currency).label("total_credits"),
                func.sum(CreditModel.unused_credit_amount).label(
                    "total_unused_credits"
                ),
            )
            .group_by(
                CreditModel.debtor_id,
                CreditModel.credit_type,
                CreditModel.base_currency,
            )
            .filter(
                CreditModel.status != CreditStatus.CANCELLED,
                CreditModel.deleted.is_(False),
            )
        )

        if debtor_ids:
            credit_summary = credit_summary.filter(
                CreditModel.debtor_id.in_(debtor_ids)
            )
        if start_date:
            credit_summary = credit_summary.filter(
                CreditModel.credit_date >= start_date
            )
        if end_date:
            credit_summary = credit_summary.filter(CreditModel.credit_date <= end_date)
        if min_credit_amount:
            credit_summary = credit_summary.filter(
                CreditModel.amount_in_base_currency >= min_credit_amount.amount
            )
        if max_credit_amount:
            credit_summary = credit_summary.filter(
                CreditModel.amount_in_base_currency <= max_credit_amount.amount
            )

        debtor_wise_credit_type_wise_summaries = group_list(credit_summary, "debtor_id")

        debtor_wise_summary = dict()
        for debtor_id, credit_summary in debtor_wise_credit_type_wise_summaries.items():
            base_currency = CurrencyType(credit_summary[0].base_currency)
            credit_type_wise_summary = {
                summary.credit_type: summary for summary in credit_summary
            }

            credit_note_total_credits = (
                credit_type_wise_summary.get(CreditType.CREDIT_NOTE).total_credits
                if credit_type_wise_summary.get(CreditType.CREDIT_NOTE)
                else 0
            )

            payment_total_credits = (
                credit_type_wise_summary.get(CreditType.PAYMENT).total_credits
                if credit_type_wise_summary.get(CreditType.PAYMENT)
                else 0
            )

            total_credits = payment_total_credits + credit_note_total_credits
            total_provisional_credits = (
                credit_type_wise_summary.get(CreditType.TDS).total_credits
                if credit_type_wise_summary.get(CreditType.TDS)
                else 0
            )
            unapplied_payment = (
                credit_type_wise_summary.get(CreditType.PAYMENT).total_unused_credits
                if credit_type_wise_summary.get(CreditType.PAYMENT)
                else 0
            )
            debtor_wise_summary[debtor_id] = (
                Money(total_credits, base_currency),
                Money(total_provisional_credits, base_currency),
                Money(unapplied_payment, base_currency),
            )
        return debtor_wise_summary

    def total_unused_credits(self, credit_search_query):
        query = self.query(func.sum(CreditModel.unused_credit_amount))
        query = self._apply_filters_on_credit_models(query, credit_search_query)
        return query.first()[0]

    def load_credits(self, credit_search_query, credits_only=False, yield_result=False):
        credit_models = self.query(CreditModel)
        credit_models = self._apply_filters_on_credit_models(
            credit_models, credit_search_query
        )
        if credits_only:
            return (
                credit_models.yield_per(1000) if yield_result else credit_models.all()
            )
        if credit_search_query.sort_order:
            if credit_search_query.sort_order == SortOrders.ASC:
                credit_models = credit_models.order_by(CreditModel.created_at.asc())
            else:
                credit_models = credit_models.order_by(CreditModel.created_at.desc())
        else:
            credit_models = credit_models.order_by(CreditModel.created_at.desc())
        if (
            credit_search_query.limit is not None
            and credit_search_query.offset is not None
        ):
            credit_models = credit_models.limit(credit_search_query.limit).offset(
                credit_search_query.offset
            )
        credit_models = credit_models.all()
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model))
            for db_model in credit_models
        ]

    def count_credits(self, credit_search_query):
        credit_models = self.query(CreditModel)
        credit_models = self._apply_filters_on_credit_models(
            credit_models, credit_search_query
        )
        return credit_models.count()

    @staticmethod
    def _apply_filters_on_credit_models(credit_models, credit_search_query):
        if credit_search_query.debtor_ids:
            credit_models = credit_models.filter(
                CreditModel.debtor_id.in_(credit_search_query.debtor_ids)
            )
        if credit_search_query.debtor_id:
            credit_models = credit_models.filter(
                CreditModel.debtor_id == credit_search_query.debtor_id
            )
        if credit_search_query.credit_id:
            credit_models = credit_models.filter(
                CreditModel.credit_id == credit_search_query.credit_id
            )
        if credit_search_query.credit_types:
            credit_models = credit_models.filter(
                CreditModel.credit_type.in_(credit_search_query.credit_types)
            )
        if credit_search_query.reference_number:
            credit_models = credit_models.filter(
                CreditModel.reference_number
                == credit_search_query.reference_number.strip()
            )
        if credit_search_query.has_unused_credit is not None:
            if bool(credit_search_query.has_unused_credit):
                credit_models = credit_models.filter(
                    CreditModel.unused_credit_amount > 0
                )
            else:
                credit_models = credit_models.filter(
                    CreditModel.unused_credit_amount <= 0
                )
        if credit_search_query.from_date and credit_search_query.to_date:
            credit_models = credit_models.filter(
                credit_search_query.from_date <= CreditModel.credit_date
            ).filter(credit_search_query.to_date >= CreditModel.credit_date)
        if credit_search_query.from_date is None and credit_search_query.to_date:
            credit_models = credit_models.filter(
                credit_search_query.to_date >= CreditModel.credit_date
            )
        if credit_search_query.show_cancelled_credits is not None and not bool(
            credit_search_query.show_cancelled_credits
        ):
            credit_models = credit_models.filter(
                CreditModel.status == CreditStatus.CREATED
            )
        if credit_search_query.used_to_auto_settle_debit is not None:
            credit_models = credit_models.filter(
                CreditModel.used_to_auto_settle_debit
                == credit_search_query.used_to_auto_settle_debit
            )
        if credit_search_query.credit_ids:
            credit_models = credit_models.filter(
                CreditModel.credit_id.in_(credit_search_query.credit_ids)
            )
        credit_models = credit_models.filter(CreditModel.deleted.is_(False))
        return credit_models

    def load_for_debtor_ids(
        self,
        debtor_ids,
        start_date=None,
        end_date=None,
        credit_types=None,
        min_credit_amount=None,
        max_credit_amount=None,
        exclude_mode_of_credit_none_types=None,
    ):
        query = self.query(CreditModel).filter(
            CreditModel.debtor_id.in_(debtor_ids), CreditModel.deleted.is_(False)
        )
        if start_date:
            query = query.filter(CreditModel.credit_date >= start_date)
        if end_date:
            query = query.filter(CreditModel.credit_date <= end_date)
        if credit_types:
            query = query.filter(CreditModel.credit_type.in_(credit_types))
        if min_credit_amount:
            query = query.filter(
                CreditModel.amount_in_base_currency >= min_credit_amount.amount
            )
        if max_credit_amount:
            query = query.filter(
                CreditModel.amount_in_base_currency <= max_credit_amount.amount
            )
        if exclude_mode_of_credit_none_types:
            query = query.filter(CreditModel.mode_of_credit.isnot(None))
        credit_models = query.all()
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model))
            for db_model in credit_models
        ]

    def load_for_update(self, credit_id):
        credit_model = self.filter(
            CreditModel,
            CreditModel.credit_id == credit_id,
            CreditModel.deleted.is_(False),
            for_update=True,
            nowait=False,
        ).first()
        if not credit_model:
            return None
        return CreditAggregate(self.credit_adaptor.to_domain_entity(credit_model))

    def load(self, credit_id):
        credit_model = self.filter(
            CreditModel,
            CreditModel.credit_id == credit_id,
            CreditModel.deleted.is_(False),
        ).first()
        if not credit_model:
            raise AggregateNotFound("Credit", f"credit_id {credit_id}")
        return CreditAggregate(self.credit_adaptor.to_domain_entity(credit_model))

    def load_all_for_update(self, credit_ids):
        credit_models = self.filter(
            CreditModel,
            CreditModel.credit_id.in_(credit_ids),
            CreditModel.deleted.is_(False),
            for_update=True,
            nowait=False,
        ).order_by(CreditModel.created_at)
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(credit_model))
            for credit_model in credit_models
        ]

    def load_all(self, credit_ids):
        credit_models = self.filter(
            CreditModel,
            CreditModel.credit_id.in_(credit_ids),
            CreditModel.deleted.is_(False),
        )
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(credit_model))
            for credit_model in credit_models
        ]

    def load_for_debtor_ids_by_credit_date(
        self,
        debtor_ids,
        credit_types=None,
        exclude_mode_of_credit_none_types=None,
        credit_date_start_date=None,
        credit_date_end_date=None,
    ):
        query = self.query(CreditModel).filter(CreditModel.debtor_id.in_(debtor_ids))
        if credit_types:
            query = query.filter(CreditModel.credit_type.in_(credit_types))
        if exclude_mode_of_credit_none_types:
            query = query.filter(CreditModel.mode_of_credit.isnot(None))
        if credit_date_start_date:
            query = query.filter(
                func.Date(CreditModel.credit_date) >= credit_date_start_date
            )
        if credit_date_end_date:
            query = query.filter(
                func.Date(CreditModel.credit_date) <= credit_date_end_date
            )
        query = query.filter(CreditModel.deleted.is_(False))

        credit_models = query.all()
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model))
            for db_model in credit_models
        ]

    def get_credit_data_for_erp_reports(self, hotel_id, business_date):
        business_date = dateutils.ymd_str_to_date(business_date)
        query = self.query(
            CreditModel.credit_date,
            CreditModel.amount_in_base_currency,
            CreditModel.mode_of_credit,
            CreditModel.reference_number,
            DebtorModel.debtor_code,
            DebtorModel.user_profile_id,
            DebtorModel.debtor_type,
        )
        query.join(DebtorModel, DebtorModel.debtor_id == CreditModel.debtor_id)
        query = query.filter(
            DebtorModel.debtor_id == CreditModel.debtor_id,
            CreditModel.reference_id.is_(None),
            CreditModel.credit_type == CreditType.PAYMENT,
            (
                (CreditModel.created_at >= business_date)
                & (CreditModel.created_at < dateutils.add(business_date, days=1))
            ),
            DebtorModel.hotel_id == hotel_id,
            CreditModel.deleted.is_(False),
        )
        credits = query.all()
        credit_details = [ERPCreditDetailsDTO(credit) for credit in credits]
        return credit_details

    def get_credits_by_reference_ids(self, reference_id):
        credit_models = self.filter(
            CreditModel,
            CreditModel.reference_id.in_(reference_id),
            CreditModel.deleted.is_(False),
        ).all()
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model))
            for db_model in credit_models
        ]

    def load_credits_and_reversal_added_on_given_date(self, date):
        created_at_min = dateutils.datetime_at_given_time(date, time.min)
        created_at_max = dateutils.datetime_at_given_time(date, time.max)
        credit_models = self.query(CreditModel).filter(
            and_(
                CreditModel.created_at >= created_at_min,
                CreditModel.created_at <= created_at_max,
                CreditModel.status == CreditStatus.CREATED,
                CreditModel.credit_type.in_(
                    [CreditType.PAYMENT, CreditType.CREDIT_REVERSAL]
                ),
                CreditModel.recorded_via == Modules.AR_MODULE,
                CreditModel.deleted.is_(False),
            )
        )
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model)).credit
            for db_model in credit_models
        ]

    def load_credits_and_reversal_cancelled_on_given_date(self, date):
        credit_models = self.query(CreditModel).filter(
            and_(
                CreditModel.cancellation_date == date,
                CreditModel.status == CreditStatus.CANCELLED,
                CreditModel.credit_type.in_(
                    [CreditType.PAYMENT, CreditType.CREDIT_REVERSAL]
                ),
                CreditModel.recorded_via == Modules.AR_MODULE,
                CreditModel.deleted.is_(False),
            )
        )
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model)).credit
            for db_model in credit_models
        ]

    def load_credits_for_given_reference_numbers(self, reference_numbers):
        credit_models = self.query(CreditModel).filter(
            and_(
                CreditModel.recorded_via == Modules.AR_MODULE,
                CreditModel.reference_number.in_(reference_numbers),
                CreditModel.deleted.is_(False),
            )
        )
        return [
            CreditAggregate(self.credit_adaptor.to_domain_entity(db_model)).credit
            for db_model in credit_models
        ]
