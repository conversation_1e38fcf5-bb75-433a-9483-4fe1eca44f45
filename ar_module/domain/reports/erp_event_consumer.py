import logging

from flask.cli import with_appcontext

from ar_module.application.consumers.decorator import consumer_middleware
from ar_module.domain.reports.services.erp_service import ERPService
from ar_module.infrastructure.consumers.base_consumer import BaseRMQConsumer
from ar_module.infrastructure.consumers.consumer_config import (
    InterfaceExchangeConsumerConfig,
)

logger = logging.getLogger(__name__)


class ERPServiceConsumer(BaseRMQConsumer):
    def __init__(self, tenant_id):
        super().__init__(InterfaceExchangeConsumerConfig(tenant_id))
        logger.info(
            f"Listening to RMQ on host: {self.connection} from queue: {self.queue}"
        )

    @consumer_middleware
    @with_appcontext
    def process_message(self, body, message):
        logger.info(f"Processing erp event {body}")
        try:
            payload = body.get("body")
            logger.info(f"Received erp pos Request with body: {payload}")
            if body.get("event_type") == "armodule-erp-data":
                hotel_id = payload.get("hotel_id")
                business_date = payload.get("business_date")
                module = payload.get("module_name")
                if module == "armodule":
                    erp_service = ERPService()
                    logger.info("preparing erp for ar-module")
                    erp_service.prepare_and_publish_erp_details(
                        hotel_id,
                        business_date,
                        body.get("event_id"),
                        body.get("batch_id"),
                    )

        except Exception as exc:
            logger.exception(f"Exception occurred while processing erp event: {body}")
            raise

        logger.info("erp message process complete. Message acknowledged")
        message.ack()
