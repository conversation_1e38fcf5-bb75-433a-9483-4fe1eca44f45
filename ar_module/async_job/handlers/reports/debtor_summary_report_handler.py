from ar_module.application.services.reporting.constants import DebtorSummaryReports
from ar_module.application.services.reporting.debtor_summary_reports.credit_advise.credit_advice_reminder import (
    CreditAdviceReminderDispatcher,
)
from ar_module.application.services.reporting.debtor_summary_reports.debit_overdue.debit_overdue_reminder import (
    DebitOverDueReminderDispatcher,
)
from ar_module.application.services.reporting.debtor_summary_reports.mis.mis_remider import (
    MISReminderDispatcher,
)
from ar_module.application.services.reporting.debtor_summary_reports.outstanding_due.outstanding_due_reminder import (
    OutStandingDuesReminderDispatcher,
)
from ar_module.application.services.reporting.dtos import DebtorSummaryRequestData
from ar_module.async_job.handlers.base_job_handler import BaseJobHandler
from object_registry import register_instance


@register_instance(
    dependencies=[
        MISReminderDispatcher,
        DebitOverDueReminderDispatcher,
        CreditAdviceReminderDispatcher,
        OutStandingDuesReminderDispatcher,
    ]
)
class DebtorSummaryReportHandler(BaseJobHandler):
    def __init__(
        self,
        mis_reminder_report_dispatcher: MISReminderDispatcher,
        debit_overdue_report_reminder_dispatcher: DebitOverDueReminderDispatcher,
        debit_advice_reminder_dispatcher: CreditAdviceReminderDispatcher,
        outstanding_dues_reminder_dispatcher: OutStandingDuesReminderDispatcher,
    ):
        self.report_dispatchers = {
            DebtorSummaryReports.MIS_REPORT: mis_reminder_report_dispatcher,
            DebtorSummaryReports.OVERDUE_REPORT: debit_overdue_report_reminder_dispatcher,
            DebtorSummaryReports.PAYMENT_ADVICE_REPORT: debit_advice_reminder_dispatcher,
            DebtorSummaryReports.OUTSTANDING_DUE_REPORT: outstanding_dues_reminder_dispatcher,
        }

    def execute_job(self, job_id, job_data):
        report_name = job_data.get("report_name")
        request = DebtorSummaryRequestData.load_from_dict(
            job_data.get("report_request_data")
        )
        self.report_dispatchers[report_name].dispatch(request)
