import csv
import json
import logging

import click
from flask.cli import with_appcontext
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from ar_module.application.services.debit_service import DebitService
from ar_module.common.slack_alert_helper import Slack<PERSON><PERSON>t
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import inject

logger = logging.getLogger(__name__)


@click.command("populate_hotel_ids_in_debits")
@click.option(
    "--file_path",
    help="csv file with invoice_number and hotel_id data",
    default=None,
)
@click.option(
    "--tenant_id",
    help="Tenant ID for which this command should be run.",
    default=TenantClient.get_default_tenant(),
)
@with_appcontext
@inject(
    debit_repo=DebitRepository,
)
def populate_hotel_ids_in_debits(debit_repo, file_path, tenant_id):
    request_context.tenant_id = tenant_id
    invoice_number_to_hotel_id_mapping = dict()
    with open(file_path, "r") as csvfile:
        next(csvfile)
        file_data = csv.reader(csvfile, delimiter=",")
        for row_data in file_data:
            invoice_number_to_hotel_id_mapping[row_data[0]] = row_data[1]
    try:
        update_hotel_id_in_debit_for_given_reference_numbers(
            debit_repo, invoice_number_to_hotel_id_mapping
        )
    except Exception as e:
        msg = "failed to populate hotel_ids due to exception: {0}".format(str(e))
        SlackAlert.send_alert(
            msg,
            tenant_id=tenant_id,
        )


@unit_of_work
def update_hotel_id_in_debit_for_given_reference_numbers(
    debit_repo, invoice_number_to_hotel_id_mapping
):
    reference_numbers = invoice_number_to_hotel_id_mapping.keys()
    debit_aggregates = debit_repo.get_debit_by_reference_numbers(reference_numbers)
    chunk_size = 1000
    for i in range(0, len(debit_aggregates), chunk_size):
        chunk = debit_aggregates[i : i + chunk_size]  # Get the current chunk

        for debit_aggregate in chunk:
            if debit_aggregate.debit.reference_number in reference_numbers:
                debit_aggregate.debit.hotel_id = invoice_number_to_hotel_id_mapping[
                    debit_aggregate.debit.reference_number
                ]

        debit_repo.update_all(chunk)
