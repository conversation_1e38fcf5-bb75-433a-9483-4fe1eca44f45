import logging

from ar_module.async_job.async_job_publisher import Async<PERSON><PERSON><PERSON><PERSON>, AsyncJobPublisher
from ar_module.async_job.job.dto.job_dto import Async<PERSON>obDTO
from ar_module.async_job.job.job_constants import Job<PERSON>ame
from ar_module.async_job.job.job_factory import JobFactory
from ar_module.async_job.job.repositories.job_repository import JobRepository
from ar_module.infrastructure.db_transaction import unit_of_work
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[JobRepository, AsyncJobPublisher])
class JobSchedulerService:
    def __init__(
        self, job_repository: JobRepository, async_job_publisher: AsyncJobPublisher
    ):
        self.job_repository = job_repository
        self.async_job_publisher = async_job_publisher

    def schedule(self, job_dto: AsyncJobDTO):
        job_aggregate = self.record_job_entry(job_dto)
        self.publish_job_for_execution(job_aggregate)
        return job_aggregate

    @unit_of_work
    def record_job_entry(self, job_dto: AsyncJobDTO):
        job_aggregate = JobFactory.create_job(job_dto)
        self.job_repository.save(job_aggregate)
        logger.info("Created a job with id: %s", job_aggregate.job_entity.job_id)
        return job_aggregate

    def publish_job_for_execution(self, job_aggregate):
        event_data = AsyncJobEvent(
            body=dict(job_id=job_aggregate.job_entity.job_id),
            job_name=job_aggregate.job_entity.job_name,
        )
        self.async_job_publisher.publish(event_data)

    def create_bulk_upload_payments_job(self, hotel_id, data, eta=None):
        return self._create_report_job(
            JobName.BULK_UPLOAD_OF_CREDITS, hotel_id, data, eta
        )

    def create_bulk_upload_settlements_job(self, hotel_id, data, eta=None):
        return self._create_report_job(
            JobName.BULK_UPLOAD_OF_SETTLEMENTS, hotel_id, data, eta
        )

    def create_debtor_summary_report_jobs(self, hotel_id, data, eta=None):
        return self._create_report_job(
            JobName.DEBTOR_SUMMARY_REPORTS, hotel_id, data, eta
        )

    def create_bulk_ingest_financial_data_job(self, hotel_id, data, eta=None):
        return self._create_report_job(
            JobName.BULK_INGESTION_OF_FINANCIAL_DATA, hotel_id, data, eta
        )

    def create_bulk_push_payments_job(self, hotel_id, data, eta=None):
        return self._create_report_job(
            JobName.BULK_PUSH_OF_PAYMENTS, hotel_id, data, eta
        )

    def _create_report_job(self, job_name, hotel_id, data, eta=None):
        job_dto = AsyncJobDTO(
            job_name=job_name,
            hotel_id=hotel_id,
            data=data,
        )
        job_aggregate = self.schedule(job_dto)
        return job_aggregate
