import json

from ar_module.integration_tests.config.common_config import (
    CREDIT_CANCELLED,
    SUPER_ADMIN,
)
from ar_module.integration_tests.config.sheet_names import CANCEL_CREDIT_SHEET_NAME
from ar_module.integration_tests.resources.db_queries import (
    GET_CREDIT,
    GET_SETTLEMENT_STATUS,
)
from ar_module.integration_tests.tests.base_validation import BaseValidations
from ar_module.integration_tests.utilities.common_utils import (
    assert_,
    query_execute,
    query_execute_and_convert_to_json,
)
from ar_module.integration_tests.utilities.excel_utils import get_test_case_data


class ValidateCancelCredit(BaseValidations):
    def __init__(
        self,
        client,
        test_case_id,
        credit_request,
        audit_trail_request,
        debtor_id,
        hotel_id,
    ):
        self.client = client
        self.test_data = get_test_case_data(CANCEL_CREDIT_SHEET_NAME, test_case_id)[0]
        self.credit_request = credit_request
        self.audit_trail_request = audit_trail_request
        self.debtor_id = debtor_id
        self.hotel_id = hotel_id

    def validate_response(self):
        query_response = query_execute_and_convert_to_json(GET_CREDIT)
        get_credits_response = self.credit_request.get_credits_request(
            self.client, 200, self.debtor_id, self.hotel_id, SUPER_ADMIN
        )["data"]["credits"]
        for actual_data, expected_data in zip(get_credits_response, query_response):
            assert_(actual_data["credit_id"], expected_data["credit_id"])
            assert_(actual_data["status"], expected_data["status"])
            assert_(actual_data["debtor_id"], expected_data["debtor_id"])
            assert_(actual_data["credit_type"], expected_data["credit_type"])
            assert_(actual_data["mode_of_credit"], expected_data["mode_of_credit"])
            assert_(actual_data["reference_number"], expected_data["reference_number"])
            assert_(actual_data["reference_number"], expected_data["reference_number"])
            assert_(actual_data["date"], expected_data["credit_date"])
            assert_(
                actual_data["amount_in_base_currency"],
                expected_data["amount_in_base_currency"],
            )
            assert_(
                actual_data["amount_in_credit_currency"],
                expected_data["amount_in_credit_currency"],
            )
            assert_(
                actual_data["unused_credit_amount"],
                expected_data["unused_credit_amount"],
            )
            assert_(
                actual_data["used_to_auto_settle_debit"],
                expected_data["used_to_auto_settle_debit"],
            )

        settlement_query_response = query_execute(GET_SETTLEMENT_STATUS)
        if settlement_query_response:
            for data in settlement_query_response:
                assert_(data[0], True)

    def validate_audit_trail(self):
        self.validate_audit_data(
            self.client,
            self.test_data,
            CREDIT_CANCELLED,
            self.audit_trail_request,
            self.credit_request,
        )
