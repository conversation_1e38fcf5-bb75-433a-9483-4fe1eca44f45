from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_settlements_facts import (
    CreateSettlementsFacts,
)
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CreateSettlementsRule(BaseRule):
    def allow(self, facts: CreateSettlementsFacts, privileges=None):
        if PrivilegeCode.CREATE_SETTLEMENTS not in privileges:
            raise PolicyAuthException(error=PolicyError.CREATE_SETTLEMENTS_NOT_ALLOWED)
        return True
